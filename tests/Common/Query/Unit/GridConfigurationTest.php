<?php

declare(strict_types=1);

namespace App\Tests\Common\Query\Unit;

use App\Common\Query\GridConfiguration;
use App\Common\Query\GridField;
use App\Common\Query\GridFilterMethod;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\DBAL\Configuration;
use Doctrine\DBAL\DriverManager;
use Doctrine\DBAL\Query\QueryBuilder;
use Doctrine\DBAL\Schema\DefaultSchemaManagerFactory;
use Doctrine\DBAL\Tools\DsnParser;
use PHPUnit\Framework\TestCase;

class GridConfigurationTest extends TestCase
{
    /** @var ArrayCollection<int, GridField> */
    private ArrayCollection $gridFields;
    private QueryBuilder $queryBuilder;

    protected function setUp(): void
    {
        $this->gridFields = new ArrayCollection();
        $this->gridFields->add(GridField::create('name', true, true, GridFilterMethod::CONTAINS));
        $this->gridFields->add(GridField::create('isActive', true, true, GridFilterMethod::BOOL));

        // Parsing the DSN
        $dsn = 'sqlite:///:memory:';
        $dsnParser = new DsnParser();
        $connectionParams = $dsnParser->parse($dsn);

        // Correcting the driver specification and adding schema manager factory configuration
        $connectionParams['driver'] = 'pdo_sqlite'; // Use 'pdo_sqlite', not 'sqlite'
        $config = new Configuration();
        $config->setSchemaManagerFactory(new DefaultSchemaManagerFactory());

        $conn = DriverManager::getConnection($connectionParams, $config);
        $this->queryBuilder = new QueryBuilder($conn);
    }

    public function testAddSortAndFilterToQueryBuilderWithAllFilterMethods(): void
    {
        $gridFields = new ArrayCollection();
        $gridFields->add(GridField::create('name', true, true, GridFilterMethod::CONTAINS));
        $gridFields->add(GridField::create('description', true, true, GridFilterMethod::STARTS_WITH));
        $gridFields->add(GridField::create('isActive', true, true, GridFilterMethod::BOOL));
        $gridFields->add(GridField::create('endDate', true, true, GridFilterMethod::ENDS_WITH));
        $gridFields->add(GridField::create('metadata', true, false, GridFilterMethod::JSON_CONTAINS));
        $gridFields->add(GridField::create('price', true, true, GridFilterMethod::EQUALS));

        $filters = [
            'name' => 'test', // CONTAINS
            'description' => 'testDescription', // STARTS_WITH
            'isActive' => 'true', // BOOL
            'endDate' => '2022', // ENDS_WITH
            'metadata' => '{"key":"value"}', // JSON_CONTAINS
            'price' => '100', // EQUALS
        ];

        $sorts = [
            'name' => 'DESC', // Valid sort
            'price' => 'ASC', // Valid sort
        ];

        $gridConfiguration = GridConfiguration::create($gridFields, 1, 20, $sorts, $filters);

        // When adding sort and filter to QueryBuilder
        $modifiedQueryBuilder = $gridConfiguration->addSortAndFilterToDbalQueryBuilder($this->queryBuilder, []);

        // Then the QueryBuilder should have the correct SQL for sorts and filters
        $sql = $modifiedQueryBuilder->getSQL();

        // Asserts for various filter methods
        self::assertStringContainsString('LIKE', $sql); // Checks CONTAINS, STARTS_WITH, ENDS_WITH
        self::assertStringContainsString('=', $sql); // Checks BOOL, EQUALS
        self::assertStringContainsString('JSON_CONTAINS', $sql); // Checks JSON_CONTAINS

        // Asserts for sorting
        self::assertStringContainsString('ORDER BY', $sql); // Checks sorting behavior
    }

    public function testExceptionForUnsupportedSortField(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage(GridConfiguration::SORTING_NOT_ALLOWED.' unknownField.');
        GridConfiguration::create($this->gridFields, 1, 20, ['unknownField' => 'ASC'], []);
    }

    public function testExceptionForUnsupportedSortDirection(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage(GridConfiguration::INVALID_SORT_DIRECTION.' INVALID.');
        GridConfiguration::create($this->gridFields, 1, 20, ['name' => 'INVALID'], []);
    }

    public function testExceptionForUnsupportedFilterField(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage(GridConfiguration::FILTERING_NOT_ALLOWED.' unknownFilter.');
        GridConfiguration::create($this->gridFields, 1, 20, [], ['unknownFilter' => 'value']);
    }

    public function testExceptionForNonFilterableField(): void
    {
        $this->gridFields->add(GridField::create('nonFilterableField', false, true, GridFilterMethod::CONTAINS));
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage(GridConfiguration::FILTERING_NOT_ALLOWED.' nonFilterableField.');
        GridConfiguration::create($this->gridFields, 1, 20, [], ['nonFilterableField' => 'value']);
    }

    public function testExceptionForNonSortableField(): void
    {
        $this->gridFields->add(GridField::create('nonSortableField', true, false, GridFilterMethod::CONTAINS));
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage(GridConfiguration::SORTING_NOT_ALLOWED.' nonSortableField.');
        GridConfiguration::create($this->gridFields, 1, 20, ['nonSortableField' => 'ASC'], []);
    }
}
