<?php

declare(strict_types=1);

namespace Hospital\Integration;

use App\Common\Uuid;
use App\Hospital\Application\HospitalFacade;
use App\Hospital\Application\Query\AccountQueryInterface;
use App\Hospital\Application\Query\HospitalQueryInterface;
use App\Hospital\Application\Query\PatientQueryInterface;
use App\Hospital\Application\Service\HospitalService;
use App\Hospital\Domain\HospitalRepositoryInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class HospitalFacadeTest extends KernelTestCase
{
    private HospitalFacade $facade;
    private static Uuid $hospitalId;

    public static function setUpBeforeClass(): void
    {
        self::$hospitalId = Uuid::generate();
    }

    protected function setUp(): void
    {
        parent::setUp();

        $hospitalRepository = self::getContainer()->get(HospitalRepositoryInterface::class);
        $hospitalQuery = self::getContainer()->get(HospitalQueryInterface::class);
        $accountQuery = self::getContainer()->get(AccountQueryInterface::class);
        $patientQuery = self::getContainer()->get(PatientQueryInterface::class);
        $hospitalService = new HospitalService($hospitalRepository, $accountQuery, $patientQuery);
        $this->facade = new HospitalFacade($hospitalService, $hospitalQuery);

        // Get the Doctrine DBAL Connection and enable savepoints
        $connection = self::getContainer()->get('doctrine.dbal.default_connection');
        $connection->setNestTransactionsWithSavepoints(true);
    }

    public function testCreateHospital(): void
    {
        $hospitalId = self::$hospitalId;
        $createShortName = substr('sn'.uniqid('_', true), 0, 20);
        $createFullName = 'Szpital Kliniczny nr 1';
        $createStreetWithNumber = 'Jana Pawła II 1';
        $createPostCode = '00-001';
        $createCity = 'Wrocław';

        $this->createHospital($hospitalId, $createShortName, $createFullName, $createStreetWithNumber, $createPostCode, $createCity);

        $hospitalView = $this->facade->findByHospitalId($hospitalId->toString());

        self::assertNotNull($hospitalView);
        self::assertSame($hospitalId->toString(), $hospitalView->hospitalId());
        self::assertSame($createShortName, $hospitalView->shortName());
        self::assertSame($createFullName, $hospitalView->fullName());
        self::assertSame($createStreetWithNumber, $hospitalView->streetWithNumber());
        self::assertSame($createPostCode, $hospitalView->postCode());
        self::assertSame($createCity, $hospitalView->city());
        self::assertTrue($hospitalView->isActive());
        self::assertNotNull($hospitalView->createdAt());
        self::assertNull($hospitalView->updatedAt());
    }

    public function testUpdateAndDeactivateAndActivateHospital(): void
    {
        $hospitalId = self::$hospitalId;
        $updateShortName = substr('snUPD'.uniqid('_', true), 0, 20);
        $updateFullName = 'Szpital Kliniczny nr 2';
        $updateStreetWithNumber = 'Jana Pawła II 3';
        $updatePostCode = '00-002';
        $updateCity = 'Poznan';

        $this->updateHospital($hospitalId, $updateShortName, $updateFullName, $updateStreetWithNumber, $updatePostCode, $updateCity);

        // Deactivate the hospital
        $this->facade->deactivate($hospitalId->toString(), 'Powód');

        $hospitalView = $this->facade->findByHospitalId($hospitalId->toString());
        self::assertNotNull($hospitalView);
        self::assertFalse($hospitalView->isActive());

        // Activate the hospital
        $this->facade->activate($hospitalId->toString());

        $hospitalView = $this->facade->findByHospitalId($hospitalId->toString());

        self::assertNotNull($hospitalView);
        self::assertTrue($hospitalView->isActive());

        // Check the rest of the fields
        self::assertNotNull($hospitalView);
        self::assertSame($hospitalId->toString(), $hospitalView->hospitalId());
        self::assertSame($updateShortName, $hospitalView->shortName());
        self::assertSame($updateFullName, $hospitalView->fullName());
        self::assertSame($updateStreetWithNumber, $hospitalView->streetWithNumber());
        self::assertSame($updatePostCode, $hospitalView->postCode());
        self::assertSame($updateCity, $hospitalView->city());
        self::assertNotNull($hospitalView->createdAt());
        self::assertNotNull($hospitalView->updatedAt());
    }

    private function createHospital(Uuid $hospitalId, string $shortName, string $name, string $address, string $postalCode, string $city): void
    {
        $this->facade->create(
            $hospitalId->valueString(),
            $shortName,
            $name,
            $address,
            $postalCode,
            $city,
        );
    }

    private function updateHospital(Uuid $hospitalId, string $shortName, string $name, string $address, string $postalCode, string $city): void
    {
        $this->facade->update(
            $hospitalId->toString(),
            $shortName,
            $name,
            $address,
            $postalCode,
            $city,
        );
    }
}
