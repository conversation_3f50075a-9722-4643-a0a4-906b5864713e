<?php

declare(strict_types=1);

namespace App\Tests\SharedKernel\Unit;

use App\SharedKernel\Protocol;
use App\SharedKernel\ProtocolType;
use PHPUnit\Framework\TestCase;

class ProtocolTest extends TestCase
{
    public function testProtocolTypeProviderCoversAllCases(): void
    {
        // Given
        $allEnumCasesCount = count(Protocol::cases());
        $providerCasesCount = count(self::protocolTypeProvider());

        // Then
        self::assertSame(
            $allEnumCasesCount,
            $providerCasesCount,
            sprintf(
                'The number of cases in protocolTypeProvider (%d) does not match the number of cases in the Protocol enum (%d). Please update the provider.',
                $providerCasesCount,
                $allEnumCasesCount
            )
        );
    }

    public function testShouldReturnAllProtocolValues(): void
    {
        $expectedValues = [
            'STS', 'WT', 'NHLB', 'ALCL', 'LBL', 'RBL', 'EGCT', 'NBL', 'OUN',
            'HL', 'ALL', 'AML', 'CML', 'MPAL', 'BT', 'HBL', 'MDS', 'LCH',
            'INNE', 'FOLLOWUP', 'TEST',
        ];
        $actualValues = Protocol::values();

        // Check if all expected values are present
        self::assertEmpty(array_diff($expectedValues, $actualValues), 'Not all expected protocol values were returned.');
        // Check if no unexpected values are present
        self::assertEmpty(array_diff($actualValues, $expectedValues), 'Unexpected protocol values were returned.');
        // Check the count matches the number of cases
        self::assertCount(count($expectedValues), $actualValues);
        self::assertCount(count(Protocol::cases()), $actualValues);
    }

    public function testShouldCorrectlyCompareProtocols(): void
    {
        $protocolAll1 = Protocol::ALL;
        $protocolAll2 = Protocol::ALL;
        $protocolAml = Protocol::AML;
        $protocolSts = Protocol::STS;
        $protocolTest = Protocol::TEST;

        self::assertTrue($protocolAll1->isEqualsTo($protocolAll2), 'ALL should be equal to ALL.');
        self::assertFalse($protocolAll1->isEqualsTo($protocolAml), 'ALL should not be equal to AML.');
        self::assertFalse($protocolSts->isEqualsTo($protocolTest), 'STS should not be equal to TEST.');
    }

    /** @dataProvider protocolTypeProvider */
    public function testShouldReturnCorrectProtocolType(Protocol $protocol, ProtocolType $expectedType): void
    {
        $actualType = $protocol->getProtocolType();

        self::assertSame($expectedType, $actualType);
    }

    public function testShouldReturnCorrectProtocolsByType(): void
    {
        $basicProtocols = Protocol::getProtocolsByType(ProtocolType::BASIC);
        $extendedProtocols = Protocol::getProtocolsByType(ProtocolType::EXTENDED);

        // Check counts
        self::assertCount(8, $extendedProtocols, 'There should be 8 extended protocols.');
        self::assertCount(count(Protocol::cases()) - 8, $basicProtocols, 'Basic protocols count should be total minus extended.');

        // Spot checks for basic protocols
        self::assertContains(Protocol::ALL, $basicProtocols);
        self::assertContains(Protocol::FOLLOWUP, $basicProtocols);
        self::assertNotContains(Protocol::NHLB, $basicProtocols);
        self::assertNotContains(Protocol::STS, $basicProtocols);

        // Spot checks for extended protocols
        self::assertContains(Protocol::NHLB, $extendedProtocols);
        self::assertContains(Protocol::LBL, $extendedProtocols);
        self::assertContains(Protocol::ALCL, $extendedProtocols);
        self::assertContains(Protocol::STS, $extendedProtocols);
        self::assertContains(Protocol::WT, $extendedProtocols);
        self::assertContains(Protocol::NBL, $extendedProtocols);
        self::assertContains(Protocol::RBL, $extendedProtocols);
        self::assertContains(Protocol::EGCT, $extendedProtocols);
        self::assertNotContains(Protocol::ALL, $extendedProtocols);
        self::assertNotContains(Protocol::FOLLOWUP, $extendedProtocols);

        // Check that every basic protocol is indeed basic
        foreach ($basicProtocols as $protocol) {
            self::assertSame(ProtocolType::BASIC, $protocol->getProtocolType(), $protocol->value.' should be BASIC');
        }

        // Check that every extended protocol is indeed extended
        foreach ($extendedProtocols as $protocol) {
            self::assertSame(ProtocolType::EXTENDED, $protocol->getProtocolType(), $protocol->value.' should be EXTENDED');
        }
    }

    /** @return array<string, array{Protocol, ProtocolType}> */
    public static function protocolTypeProvider(): array
    {
        return [
            'ALL is BASIC' => [Protocol::ALL, ProtocolType::BASIC],
            'AML is BASIC' => [Protocol::AML, ProtocolType::BASIC],
            'CML is BASIC' => [Protocol::CML, ProtocolType::BASIC],
            'FOLLOWUP is BASIC' => [Protocol::FOLLOWUP, ProtocolType::BASIC],
            'TEST is BASIC' => [Protocol::TEST, ProtocolType::BASIC],
            'INNE is BASIC' => [Protocol::INNE, ProtocolType::BASIC],
            'HL is BASIC' => [Protocol::HL, ProtocolType::BASIC],
            'OUN is BASIC' => [Protocol::OUN, ProtocolType::BASIC],
            'BT is BASIC' => [Protocol::BT, ProtocolType::BASIC],
            'HBL is BASIC' => [Protocol::HBL, ProtocolType::BASIC],
            'MDS is BASIC' => [Protocol::MDS, ProtocolType::BASIC],
            'LCH is BASIC' => [Protocol::LCH, ProtocolType::BASIC],
            'MPAL is BASIC' => [Protocol::MPAL, ProtocolType::BASIC],

            'NHLB is EXTENDED' => [Protocol::NHLB, ProtocolType::EXTENDED],
            'LBL is EXTENDED' => [Protocol::LBL, ProtocolType::EXTENDED],
            'ALCL is EXTENDED' => [Protocol::ALCL, ProtocolType::EXTENDED],
            'STS is EXTENDED' => [Protocol::STS, ProtocolType::EXTENDED],
            'WT is EXTENDED' => [Protocol::WT, ProtocolType::EXTENDED],
            'NBL is EXTENDED' => [Protocol::NBL, ProtocolType::EXTENDED],
            'RBL is EXTENDED' => [Protocol::RBL, ProtocolType::EXTENDED],
            'EGCT is EXTENDED' => [Protocol::EGCT, ProtocolType::EXTENDED],
        ];
    }
}
