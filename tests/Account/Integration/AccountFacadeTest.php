<?php

declare(strict_types=1);

namespace App\Tests\Account\Integration;

use App\Account\Application\AccountFacade;
use App\Account\Application\Query\AccountQueryInterface;
use App\Account\Application\Query\HospitalQueryInterface;
use App\Account\Application\Service\AccountService;
use App\Account\Domain\AccountRepositoryInterface;
use App\Auth\Application\AuthAccountFacade;
use App\Common\Uuid;
use App\SharedKernel\Protocol;
use App\SharedKernel\Roles;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class AccountFacadeTest extends KernelTestCase
{
    private AccountFacade $facade;
    private static Uuid $accountId;

    protected function setUp(): void
    {
        parent::setUp();

        self::$accountId = Uuid::generate();

        $accountRepository = self::getContainer()->get(AccountRepositoryInterface::class);
        $accountQuery = self::getContainer()->get(AccountQueryInterface::class);
        $authAccountFacade = self::getContainer()->get(AuthAccountFacade::class);
        $hospitalQuery = self::getContainer()->get(HospitalQueryInterface::class);
        $accountService = new AccountService(
            $accountQuery,
            $accountRepository,
            $authAccountFacade,
            $hospitalQuery
        );
        $this->facade = new AccountFacade($accountService, $accountQuery);

        // Get the Doctrine DBAL Connection and enable savepoints
        $connection = self::getContainer()->get('doctrine.dbal.default_connection');
        $connection->setNestTransactionsWithSavepoints(true);
    }

    public function testCreateUpdateActivateDeactivateCentralAdminAccount(): void
    {
        $this->createUpdateActivateDeactivateAccount(Roles::ROLE_CENTRAL_ADMINISTRATOR->value, [], []);
    }

    public function testCreateUpdateActivateDeactivateReportingPhysicianAccount(): void
    {
        $this->createUpdateActivateDeactivateAccount(Roles::ROLE_REPORTER_PHYSICIAN->value, [Protocol::ALCL->value], ['13cd98bf-5c0e-472f-a4ef-31e28422f0ec']);
    }

    public function testCreateUpdateActivateDeactivateCoordinatorAccount(): void
    {
        $this->createUpdateActivateDeactivateAccount(Roles::ROLE_COORDINATOR->value, [Protocol::ALL->value], []);
    }

    public function testCreateUpdateActivateDeactivateDataAdministratorAccount(): void
    {
        $this->createUpdateActivateDeactivateAccount(Roles::ROLE_DATA_ADMINISTRATOR->value, [], ['13cd98bf-5c0e-472f-a4ef-31e28422f0ec']);
    }

    /**
     * @param array<string> $protocols
     * @param array<string> $hospitals
     */
    private function createUpdateActivateDeactivateAccount(string $role, array $protocols, array $hospitals): void
    {
        $accountId = self::$accountId;
        $firstName = 'John';
        $lastName = 'Doe';
        $email = 'john.doe'.random_int(1, 99999).'@example.com';
        $mobilePhoneNumber = '*********'.random_int(1, 99999);

        $this->facade->create(
            $accountId->valueString(),
            $firstName,
            $lastName,
            $email,
            $mobilePhoneNumber,
            $role,
            $protocols,
            $hospitals
        );

        $accountView = $this->facade->findByAccountId($accountId->toString());

        self::assertNotNull($accountView);
        self::assertSame($accountId->toString(), $accountView->accountId());
        self::assertSame($firstName, $accountView->firstName());
        self::assertSame($lastName, $accountView->lastName());
        self::assertSame($email, $accountView->email());
        self::assertSame($mobilePhoneNumber, $accountView->mobilePhoneNumber());
        self::assertSame($role, $accountView->role());

        // Update the account
        $updatedFirstName = 'Jane';
        $updatedLastName = 'Doe';
        $updatedMobilePhoneNumber = '*********'.random_int(1, 99999);

        $this->facade->update(
            $accountId->toString(),
            $updatedFirstName,
            $updatedLastName,
            $updatedMobilePhoneNumber,
            $protocols,
            $hospitals
        );

        $updatedAccountView = $this->facade->findByAccountId($accountId->toString());

        self::assertNotNull($updatedAccountView);
        self::assertSame($accountId->toString(), $updatedAccountView->accountId());
        self::assertSame($updatedFirstName, $updatedAccountView->firstName());
        self::assertSame($updatedLastName, $updatedAccountView->lastName());
        self::assertSame($updatedMobilePhoneNumber, $updatedAccountView->mobilePhoneNumber());
        self::assertSame($role, $updatedAccountView->role());

        // Deactivate the account
        $this->facade->deactivate($accountId->toString(), 'Reason');

        $deactivatedAccountView = $this->facade->findByAccountId($accountId->toString());
        self::assertNotNull($deactivatedAccountView);
        self::assertFalse($deactivatedAccountView->isActive());

        // Activate the account
        $this->facade->activate($accountId->toString());

        $activatedAccountView = $this->facade->findByAccountId($accountId->toString());

        self::assertNotNull($activatedAccountView);
        self::assertTrue($activatedAccountView->isActive());
    }
}
