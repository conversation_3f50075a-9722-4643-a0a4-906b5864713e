<?php

declare(strict_types=1);

namespace App\Tests\Form\Integration;

use App\Form\FormService;
use App\Form\ProtocolFormConfig;
use App\Form\ProtocolFormDataRepository;
use App\Form\ValidationService;
use App\Patient\Application\PatientFacade;
use App\SharedKernel\Protocol;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class FormServiceTest extends KernelTestCase
{
    private FormService $formService;
    private ProtocolFormDataRepository $formDataRepository;
    private PatientFacade $patientFacade;

    protected function setUp(): void
    {
        $this->formDataRepository = $this->createMock(ProtocolFormDataRepository::class);
        $this->patientFacade = $this->createMock(PatientFacade::class);
        $parameterBag = self::getContainer()->get(ParameterBagInterface::class);
        $validationService = self::getContainer()->get(ValidationService::class);
        $this->formService = new FormService(
            $this->formDataRepository,
            $this->patientFacade,
            $parameterBag,
            $validationService
        );
    }

    public function testGetFormsForAllProtocols(): void
    {
        foreach (Protocol::values() as $protocolValue) {
            $protocol = Protocol::from($protocolValue);
            $result = $this->formService->getForms($protocol, null, null);

            self::assertNotNull($result);
            self::assertInstanceOf(ProtocolFormConfig::class, $result);
        }
    }

    public function testGetFormSchemaForAllProtocolsAndForms(): void
    {
        foreach (Protocol::values() as $protocolValue) {
            $protocol = Protocol::from($protocolValue);
            $protocolForm = $this->formService->getForms($protocol, null, null);

            foreach ($protocolForm->configure() as $form) {
                $schema = $this->formService->getFormSchema($protocol->value, $form->schema());

                self::assertNotNull($schema);
                self::assertIsString($schema);
            }
        }
    }
}
