<?php

declare(strict_types=1);

namespace App\Tests\Patient\Unit\Domain;

use App\Patient\Domain\Address;
use PHPUnit\Framework\TestCase;

class AddressTest extends TestCase
{
    public function testCreate(): void
    {
        // Given
        $streetAndNumber = 'ul. Polna 1';
        $city = 'Warszawa';
        $postalCode = '00-001';

        // When
        $address = Address::create($streetAndNumber, $city, $postalCode);

        // Then
        self::assertEquals($streetAndNumber, $address->streetWithNumber());
        self::assertEquals($city, $address->city());
        self::assertEquals($postalCode, $address->postCode());
    }

    public function testIsEqualsTo(): void
    {
        // Given
        $address1 = Address::create('ul. Polna 1', 'Warszawa', '00-001');
        $address2 = Address::create('ul. Polna 1', 'Warszawa', '00-001');
        $address3 = Address::create('ul. Ogrodowa 2', 'Kraków', '30-001');

        // When
        $areAddressesSame = $address1->isEqualsTo($address2);
        $areAddressesDifferent = $address1->isEqualsTo($address3);

        // Then
        self::assertTrue($areAddressesSame);
        self::assertFalse($areAddressesDifferent);
    }

    public function testCreateWithInvalidStreetNameShouldThrowError(): void
    {
        // Given
        $streetWithNumber = 'ul';
        $city = 'Warszawa';
        $postCode = '00-001';

        // Then
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Ulica i numer domu muszą składać się z co najmniej 3 znaków.');

        // When
        $address = Address::create($streetWithNumber, $city, $postCode);
    }

    public function testCreateWithInvalidCityNameShouldThrowError(): void
    {
        // Given
        $streetWithNumber = 'ul. Marszałkowska 1';
        $city = 'W';
        $postCode = '00-001';

        // Then
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Nazwa miasta musi składać się z co najmniej 3 znaków.');

        // When
        $address = Address::create($streetWithNumber, $city, $postCode);
    }

    public function testCreateWithInvalidPostCodeShouldThrowError(): void
    {
        // Given
        $streetWithNumber = 'ul. Marszałkowska 1';
        $city = 'Warszawa';
        $postCode = '00001';

        // Then
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Nieprawidłowy kod pocztowy.');

        // When
        $address = Address::create($streetWithNumber, $city, $postCode);
    }

    public function testJsonSerialize(): void
    {
        // Given
        $address = Address::create('ul. Polna 1', 'Warszawa', '00-001');
        $expectedJson = [
            'streetWithNumber' => 'ul. Polna 1',
            'city' => 'Warszawa',
            'postCode' => '00-001',
        ];

        // When
        $json = $address->jsonSerialize();

        // Then
        self::assertEquals($expectedJson, $json);
    }
}
