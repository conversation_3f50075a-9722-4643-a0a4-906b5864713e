<?php

declare(strict_types=1);

namespace App\Tests\Patient\Unit\Domain;

use App\Patient\Domain\PatientPublicId;
use PHPUnit\Framework\TestCase;

class PatientPublicIdTest extends TestCase
{
    public function testCreate(): void
    {
        $patientPublicId = PatientPublicId::create('Jan', '<PERSON><PERSON><PERSON>', '123456');
        self::assertInstanceOf(PatientPublicId::class, $patientPublicId);
    }

    public function testCreateReplacesPolishCharacters(): void
    {
        $patientPublicId = PatientPublicId::create('Jan', 'Śliwerski', '123456');
        self::assertEquals('JS123456', $patientPublicId->patientPublicId());
    }

    public function testCreateConvertsInitialsToUppercase(): void
    {
        $patientPublicId = PatientPublicId::create('jan', 'kowalski', '123456');
        self::assertEquals('JK123456', $patientPublicId->patientPublicId());
    }

    public function testCreateGeneratesNumberWhenNotProvided(): void
    {
        $patientPublicId = PatientPublicId::create('Jan', 'Kowalski');
        self::assertMatchesRegularExpression('/^JK\d{6}$/', $patientPublicId->patientPublicId());
    }

    public function testCreateThrowsExceptionWhenNumberIsNotNumeric(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        PatientPublicId::create('Jan', 'Kowalski', 'abc123');
    }

    public function testCreateThrowsExceptionWhenNumberIsNotSixCharactersLong(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        PatientPublicId::create('Jan', 'Kowalski', '12345');
    }
}
