<?php

declare(strict_types=1);

namespace App\Tests\Patient\Unit\Domain;

use App\Patient\Domain\BirthDate;
use App\Patient\Domain\Gender;
use App\Patient\Domain\Pesel;
use PHPUnit\Framework\TestCase;

class PeselTest extends TestCase
{
    public function testCreate(): void
    {
        $pesel = Pesel::create('***********');

        self::assertInstanceOf(Pesel::class, $pesel);
    }

    /** @dataProvider validPeselProvider */
    public function testValidPesels(string $peselNumber): void
    {
        $pesel = Pesel::create($peselNumber);
        self::assertInstanceOf(Pesel::class, $pesel);
    }

    /** @dataProvider invalidPeselProvider */
    public function testInvalidPesels(string $peselNumber): void
    {
        $this->expectException(\InvalidArgumentException::class);
        Pesel::create($peselNumber);
    }

    public function testGender(): void
    {
        $pesel = Pesel::create('***********');

        self::assertEquals(Gender::FEMALE, $pesel->gender());
    }

    public function testBirthDate(): void
    {
        $pesel = Pesel::create('***********');

        self::assertEquals(BirthDate::createFromString('1902-07-08'), $pesel->birthDate());
    }

    /** @return array<array<string>> */
    public static function validPeselProvider(): array
    {
        return [
            ['***********'],
            ['***********'],
            ['***********'], // Poprawny numer PESEL dla osoby urodzonej 14 maja 1944 roku
        ];
    }

    /** @return array<array<string>> */
    public static function invalidPeselProvider(): array
    {
        return [
            ['***********'], // Za krótki
            ['***********2'], // Za długi
            ['abcdefghijk'], // Zawiera litery
            ['***********'], // Niepoprawna suma kontrolna
            ['***********'], // Wszystkie cyfry są takie same
            ['***********'], // Wszystkie cyfry to zera
            ['***********'], // Wszystkie cyfry to dziewiątki
            ['***********'], // Dzień urodzenia większy niż 29 w lutym nieprzestępnego roku
            ['***********'], // Miesiąc urodzenia większy niż 12
            ['***********'], // Dzień urodzenia większy niż 29 w lutym przestępnego roku
            ['***********'], // Dzień urodzenia większy niż 30 w kwietniu
            ['***********'], // Niepoprawna suma kontrolna dla osoby urodzonej 14 maja 1944 roku
            ['***********'], // Niepoprawna suma kontrolna dla osoby urodzonej 8 lipca 2002 roku
            ['***********'], // Niepoprawna suma kontrolna dla osoby urodzonej 5 września 1990 roku
        ];
    }
}
