<?php

declare(strict_types=1);

namespace App\Tests\Patient\Unit\Domain;

use App\Common\Uuid;
use App\Patient\Domain\Address;
use App\Patient\Domain\BirthDate;
use App\Patient\Domain\Citizenship;
use App\Patient\Domain\Exception\HospitalCannotBeAddedToPatient;
use App\Patient\Domain\Exception\HospitalCannotBeRemovedFromPatient;
use App\Patient\Domain\Exception\PatientCannotBeChanged;
use App\Patient\Domain\Exception\PatientCannotBeCreated;
use App\Patient\Domain\Gender;
use App\Patient\Domain\PassportNumber;
use App\Patient\Domain\Patient;
use App\Patient\Domain\PatientPublicId;
use App\Patient\Domain\Pesel;
use App\SharedKernel\Protocol;
use App\Tests\Patient\DataFactory\PeselFactory;
use PHPUnit\Framework\TestCase;

class PatientTest extends TestCase
{
    private Uuid $patientId;
    private PatientPublicId $patientPublicId;
    private string $firstName;
    private string $lastName;
    private Address $residenceAddress;
    private BirthDate $birthDate;
    private Gender $gender;
    private Citizenship $polishCitizenship;
    private Citizenship $germanCitizenship;
    private Pesel $validPesel;
    private PassportNumber $validPassport;
    /** @var array<string> */
    private array $hospitals;
    private Protocol $alcl;

    protected function setUp(): void
    {
        parent::setUp(); // Dobra praktyka, aby wywołać metodę setUp rodzica

        $this->patientId = Uuid::generate();
        $this->firstName = 'Jan';
        $this->lastName = 'Kowalski';
        $this->patientPublicId = PatientPublicId::create($this->firstName, $this->lastName);
        $this->residenceAddress = Address::create('Testowa 1', 'Warszawa', '00-001');
        $birthDateString = '1980-01-01';
        $this->birthDate = BirthDate::createFromString($birthDateString);
        $this->gender = Gender::MALE;
        // Użycie PeselFactory do wygenerowania poprawnego PESEL
        $this->validPesel = Pesel::create(PeselFactory::generate($birthDateString, $this->gender === Gender::MALE));
        $this->polishCitizenship = Citizenship::PL;
        $this->germanCitizenship = Citizenship::DE;
        $this->validPassport = PassportNumber::create('ABC123456');
        $this->hospitals = [Uuid::generate()->toString()];
        $this->alcl = Protocol::ALCL;
    }

    /**
     * Helper function to create a patient with default valid data.
     *
     * @param array<string, mixed> $overrides
     */
    private function createDefaultPatient(array $overrides = []): Patient
    {
        $defaults = [
            'patientId' => $this->patientId,
            'patientPublicId' => $this->patientPublicId,
            'firstName' => $this->firstName,
            'lastName' => $this->lastName,
            'citizenship' => $this->polishCitizenship,
            'isRegisteredAddressSameAsResidence' => true,
            'isCorrespondenceAddressSameAsResidence' => true,
            'residenceAddress' => $this->residenceAddress,
            'registeredAddress' => null,
            'correspondenceAddress' => null,
            'email' => '<EMAIL>',
            'contactNumber' => '*********',
            'isPatientIdentified' => true,
            'pesel' => $this->validPesel,
            'passportNumber' => null,
            'legalRepresentativePesel' => null,
            'legalRepresentativePassportNumber' => null,
            'gender' => $this->gender,
            'birthDate' => $this->birthDate,
            'hospitals' => $this->hospitals,
            'protocol' => $this->alcl,
        ];

        $params = array_merge($defaults, $overrides);

        // Używamy bezpośrednio konstruktora lub metody fabrykującej z klasy Patient
        // W tym przypadku Patient::create jest aliasem do konstruktora z dodatkową logiką
        return Patient::create(
            $params['patientId'],
            $params['patientPublicId'],
            $params['firstName'],
            $params['lastName'],
            $params['citizenship'],
            $params['isRegisteredAddressSameAsResidence'],
            $params['isCorrespondenceAddressSameAsResidence'],
            $params['residenceAddress'],
            $params['registeredAddress'],
            $params['correspondenceAddress'],
            $params['email'],
            $params['contactNumber'],
            $params['isPatientIdentified'],
            $params['pesel'],
            $params['passportNumber'],
            $params['legalRepresentativePesel'],
            $params['legalRepresentativePassportNumber'],
            $params['gender'],
            $params['birthDate'],
            $params['hospitals'],
            $params['protocol']
        );
    }

    // --- Constructor Tests ---

    public function testCanCreatePatientSuccessfullyWithPolishCitizenshipAndPesel(): void
    {
        // Given: Default valid patient data (Polish citizen with PESEL)

        // When
        $patient = $this->createDefaultPatient();

        // Then
        self::assertInstanceOf(Patient::class, $patient);
        self::assertTrue($patient->patientId()->equals($this->patientId));
        self::assertTrue($patient->isActive());
        self::assertCount(2, $patient->protocols(), 'Powinny być protokoły ALCL i FOLLOWUP');

        $alclProtocol = $patient->protocols()->filter(fn ($p) => $p->protocol()->isEqualsTo(Protocol::ALCL))->first();
        self::assertNotFalse($alclProtocol, 'Protokół ALCL powinien istnieć');
        self::assertTrue($alclProtocol->isActive(), 'Protokół ALCL powinien być aktywny');

        $followupProtocol = $patient->protocols()->filter(fn ($p) => $p->protocol()->isEqualsTo(Protocol::FOLLOWUP))->first();
        self::assertNotFalse($followupProtocol, 'Protokół FOLLOWUP powinien istnieć');
        self::assertTrue($followupProtocol->isActive(), 'Protokół FOLLOWUP powinien być zawsze aktywny');
    }

    public function testCanCreatePatientSuccessfullyWithNonPolishCitizenshipAndPassport(): void
    {
        // Given
        $data = [
            'citizenship' => $this->germanCitizenship,
            'pesel' => null,
            'passportNumber' => $this->validPassport,
            'birthDate' => BirthDate::createFromString('1990-05-15'),
            'gender' => Gender::FEMALE,
        ];

        // When
        $patient = $this->createDefaultPatient($data);

        // Then
        self::assertInstanceOf(Patient::class, $patient);
        self::assertTrue($patient->isActive());
        self::assertCount(2, $patient->protocols());
        // Można dodać bardziej szczegółowe asercje, jeśli powstaną gettery
        // $this->assertTrue($patient->citizenship()->isEqualsTo($this->germanCitizenship));
        // $this->assertNull($patient->pesel());
        // $this->assertNotNull($patient->passportNumber());
        // $this->assertTrue($patient->passportNumber()->isEqualsTo($this->validPassport));
    }

    public function testCannotCreatePatientWithEmptyHospitalList(): void
    {
        // Given
        $data = ['hospitals' => []];

        // Then
        $this->expectException(PatientCannotBeCreated::class);
        $this->expectExceptionMessage(PatientCannotBeCreated::becauseHospitalListCannotBeEmpty()->getMessage());

        // When
        $this->createDefaultPatient($data);
    }

    public function testCannotCreatePatientWithFollowUpProtocolAsInitial(): void
    {
        // Given
        $data = ['protocol' => Protocol::FOLLOWUP];

        // Then
        $this->expectException(PatientCannotBeCreated::class);
        $this->expectExceptionMessage(PatientCannotBeCreated::becauseFollowUpProtocolCannotBeAddedToPatient()->getMessage());

        // When
        $this->createDefaultPatient($data);
    }

    // --- Edge Case Tests (Validation) ---

    public function testCannotCreatePolishCitizenWithoutPeselWhenIdentified(): void
    {
        // Given
        $data = [
            'citizenship' => $this->polishCitizenship,
            'isPatientIdentified' => true,
            'pesel' => null,
            'passportNumber' => null,
        ];

        // Then
        $this->expectException(PatientCannotBeChanged::class);
        $this->expectExceptionMessage(PatientCannotBeChanged::becausePeselIsRequiredForPolishCitizenship()->getMessage());

        // When
        $this->createDefaultPatient($data);
    }

    public function testCannotCreatePolishCitizenWithPassportWhenIdentified(): void
    {
        // Given
        $data = [
            'citizenship' => $this->polishCitizenship,
            'isPatientIdentified' => true,
            'pesel' => $this->validPesel,
            'passportNumber' => $this->validPassport,
        ];

        // Then
        $this->expectException(PatientCannotBeChanged::class);
        $this->expectExceptionMessage(PatientCannotBeChanged::becausePassportNumberShouldNotBeFilledInIfPatientIsPolishCitizenshipAndHasPesel()->getMessage());

        // When
        $this->createDefaultPatient($data);
    }

    public function testCannotCreatePolishCitizenWhenBirthDateDoesNotMatchPesel(): void
    {
        // Given
        $wrongBirthDate = BirthDate::createFromString('1980-01-02');
        $data = [
            'citizenship' => $this->polishCitizenship,
            'isPatientIdentified' => true,
            'pesel' => $this->validPesel,
            'birthDate' => $wrongBirthDate,
        ];

        // Then
        $this->expectException(PatientCannotBeChanged::class);
        $this->expectExceptionMessage(PatientCannotBeChanged::becauseBirthDateMustMatchPesel()->getMessage());

        // When
        $this->createDefaultPatient($data);
    }

    public function testCannotCreatePolishCitizenWhenGenderDoesNotMatchPesel(): void
    {
        // Given
        $wrongGender = Gender::FEMALE;
        $data = [
            'citizenship' => $this->polishCitizenship,
            'isPatientIdentified' => true,
            'pesel' => $this->validPesel,
            'gender' => $wrongGender,
        ];

        // Then
        $this->expectException(PatientCannotBeChanged::class);
        $this->expectExceptionMessage(PatientCannotBeChanged::becauseGenderMustMatchPesel()->getMessage());

        // When
        $this->createDefaultPatient($data);
    }

    public function testCannotCreateUnidentifiedPolishCitizenWithoutLegalRepPesel(): void
    {
        // Given
        $data = [
            'citizenship' => $this->polishCitizenship,
            'isPatientIdentified' => false,
            'pesel' => null,
            'passportNumber' => null,
            'legalRepresentativePesel' => null,
            'legalRepresentativePassportNumber' => null,
        ];

        // Then
        $this->expectException(PatientCannotBeChanged::class);
        $this->expectExceptionMessage(PatientCannotBeChanged::becauseLegalRepresentativePeselIsRequiredForPolishCitizenship()->getMessage());

        // When
        $this->createDefaultPatient($data);
    }

    // --- Non-Polish Edge Cases ---

    public function testCannotCreateNonPolishCitizenWithoutPeselOrPassportWhenIdentified(): void
    {
        // Given
        $data = [
            'citizenship' => $this->germanCitizenship,
            'isPatientIdentified' => true,
            'pesel' => null,
            'passportNumber' => null,
        ];

        // Then
        $this->expectException(PatientCannotBeChanged::class);
        $this->expectExceptionMessage(PatientCannotBeChanged::becausePeselOrPassportIsRequiredForNonPolishCitizenship()->getMessage());

        // When
        $this->createDefaultPatient($data);
    }

    public function testCannotCreateNonPolishCitizenWhenBirthDateDoesNotMatchPeselIfProvided(): void
    {
        // Given
        $peselForDate = Pesel::create(PeselFactory::generate('1995-03-10', true));
        $wrongBirthDate = BirthDate::createFromString('1995-03-11');
        $data = [
            'citizenship' => $this->germanCitizenship,
            'isPatientIdentified' => true,
            'pesel' => $peselForDate,
            'passportNumber' => null,
            'birthDate' => $wrongBirthDate,
        ];

        // Then
        $this->expectException(PatientCannotBeChanged::class);
        $this->expectExceptionMessage(PatientCannotBeChanged::becauseBirthDateMustMatchPesel()->getMessage());

        // When
        $this->createDefaultPatient($data);
    }

    public function testCannotCreateNonPolishCitizenWhenGenderDoesNotMatchPeselIfProvided(): void
    {
        // Given
        $peselForMale = Pesel::create(PeselFactory::generate('1992-07-20', true));
        $wrongGender = Gender::FEMALE;
        $data = [
            'citizenship' => $this->germanCitizenship,
            'isPatientIdentified' => true,
            'pesel' => $peselForMale,
            'passportNumber' => null,
            'gender' => $wrongGender,
            'birthDate' => BirthDate::createFromString('1992-07-20'),
        ];

        // Then
        $this->expectException(PatientCannotBeChanged::class);
        $this->expectExceptionMessage(PatientCannotBeChanged::becauseGenderMustMatchPesel()->getMessage());

        // When
        $this->createDefaultPatient($data);
    }

    public function testCannotCreateUnidentifiedNonPolishCitizenWithoutLegalRepPeselOrPassport(): void
    {
        // Given
        $data = [
            'citizenship' => $this->germanCitizenship,
            'isPatientIdentified' => false,
            'pesel' => null,
            'passportNumber' => null,
            'legalRepresentativePesel' => null,
            'legalRepresentativePassportNumber' => null,
        ];

        // Then
        $this->expectException(PatientCannotBeChanged::class);
        $this->expectExceptionMessage(PatientCannotBeChanged::becausePeselOrPassportIsRequiredForNonPolishCitizenship()->getMessage());

        // When
        $this->createDefaultPatient($data);
    }

    // --- Address Edge Cases ---

    public function testCannotCreatePatientWithRegisteredAddressWhenSameAsResidenceIsTrue(): void
    {
        // Given
        $registeredAddress = Address::create('Inna 1', 'Kraków', '30-001');
        $data = [
            'isRegisteredAddressSameAsResidence' => true,
            'registeredAddress' => $registeredAddress,
        ];

        // Then
        $this->expectException(PatientCannotBeChanged::class);
        $this->expectExceptionMessage(PatientCannotBeChanged::becauseRegisteredAddressCannotBeDefinedIfSameAsResidence()->getMessage());

        // When
        $this->createDefaultPatient($data);
    }

    public function testCannotCreatePatientWithoutRegisteredAddressWhenSameAsResidenceIsFalse(): void
    {
        // Given
        $data = [
            'isRegisteredAddressSameAsResidence' => false,
            'registeredAddress' => null,
        ];

        // Then
        $this->expectException(PatientCannotBeChanged::class);
        $this->expectExceptionMessage(PatientCannotBeChanged::becauseRegisteredAddressCannotBeEmptyIfDifferentFromResidence()->getMessage());

        // When
        $this->createDefaultPatient($data);
    }

    public function testCannotCreatePatientWithCorrespondenceAddressWhenSameAsResidenceIsTrue(): void
    {
        // Given
        $correspondenceAddress = Address::create('Korespondencyjna 1', 'Gdańsk', '80-001');
        $data = [
            'isCorrespondenceAddressSameAsResidence' => true,
            'correspondenceAddress' => $correspondenceAddress,
        ];

        // Then
        $this->expectException(PatientCannotBeChanged::class);
        $this->expectExceptionMessage(PatientCannotBeChanged::becauseCorrespondenceAddressCannotBeDefinedIfSameAsResidence()->getMessage());

        // When
        $this->createDefaultPatient($data);
    }

    public function testCannotCreatePatientWithoutCorrespondenceAddressWhenSameAsResidenceIsFalse(): void
    {
        // Given
        $data = [
            'isCorrespondenceAddressSameAsResidence' => false,
            'correspondenceAddress' => null,
        ];

        // Then
        $this->expectException(PatientCannotBeChanged::class);
        $this->expectExceptionMessage(PatientCannotBeChanged::becauseCorrespondenceAddressCannotBeEmptyIfDifferentFromResidence()->getMessage());

        // When
        $this->createDefaultPatient($data);
    }

    // --- Update Tests ---

    public function testCanUpdatePatientData(): void
    {
        // Given
        $patient = $this->createDefaultPatient();
        $newFirstName = 'Adam';
        $newLastName = 'Nowak';
        $newEmail = '<EMAIL>';
        $newContactNumber = '987654321';
        $newResidenceAddress = Address::create('Nowa 5', 'Łódź', '90-001');

        // When
        $patient->update(
            $newFirstName,
            $newLastName,
            $this->polishCitizenship,
            true,
            true,
            $newResidenceAddress,
            null,
            null,
            $newEmail,
            $newContactNumber,
            true,
            $this->validPesel,
            null,
            null,
            null,
            $this->gender,
            $this->birthDate
        );

        // Then
        // Ponieważ nie mamy getterów dla większości pól, możemy sprawdzić tylko pośrednio
        // np. przez sprawdzenie, czy metoda nie rzuciła wyjątku
        self::assertTrue(true, 'Update powinien zakończyć się sukcesem');
    }

    public function testCannotUpdatePatientWithInvalidData(): void
    {
        // Given
        $patient = $this->createDefaultPatient();
        $wrongBirthDate = BirthDate::createFromString('1990-01-01'); // Data niezgodna z PESEL

        // Then
        $this->expectException(PatientCannotBeChanged::class);
        $this->expectExceptionMessage(PatientCannotBeChanged::becauseBirthDateMustMatchPesel()->getMessage());

        // When
        $patient->update(
            $this->firstName,
            $this->lastName,
            $this->polishCitizenship,
            true,
            true,
            $this->residenceAddress,
            null,
            null,
            '<EMAIL>',
            '*********',
            true,
            $this->validPesel,
            null,
            null,
            null,
            $this->gender,
            $wrongBirthDate // Nieprawidłowa data urodzenia niezgodna z PESEL
        );
    }

    // --- Hospital Management Tests ---

    public function testCanAddHospitalToPatient(): void
    {
        // Given
        $patient = $this->createDefaultPatient();
        $newHospitalId = Uuid::generate();

        // When
        $patient->addHospital($newHospitalId);

        // Then
        self::assertContains($newHospitalId->toString(), $patient->hospitals(), 'Szpital powinien zostać dodany');
        self::assertCount(2, $patient->hospitals(), 'Pacjent powinien mieć teraz 2 szpitale');
    }

    public function testCannotAddDuplicateHospitalToPatient(): void
    {
        // Given
        $patient = $this->createDefaultPatient();
        $existingHospitalId = Uuid::fromString($this->hospitals[0]);

        // Then
        $this->expectException(HospitalCannotBeAddedToPatient::class);

        // When
        $patient->addHospital($existingHospitalId);
    }

    public function testCanRemoveHospitalFromPatient(): void
    {
        // Given
        $patient = $this->createDefaultPatient();
        $secondHospitalId = Uuid::generate();
        $patient->addHospital($secondHospitalId);
        $hospitalToRemoveId = Uuid::fromString($this->hospitals[0]);

        // When
        $patient->removeHospital($hospitalToRemoveId);

        // Then
        self::assertNotContains($hospitalToRemoveId->toString(), $patient->hospitals(), 'Szpital powinien zostać usunięty');
        self::assertCount(1, $patient->hospitals(), 'Pacjent powinien mieć teraz 1 szpital');
        self::assertContains($secondHospitalId->toString(), $patient->hospitals(), 'Drugi szpital powinien pozostać');
    }

    public function testCannotRemoveNonexistentHospitalFromPatient(): void
    {
        // Given
        $patient = $this->createDefaultPatient();
        $nonexistentHospitalId = Uuid::generate();

        // Then
        $this->expectException(HospitalCannotBeRemovedFromPatient::class);

        // When
        $patient->removeHospital($nonexistentHospitalId);
    }

    public function testCannotRemoveLastHospitalFromPatient(): void
    {
        // Given
        $patient = $this->createDefaultPatient();
        $hospitalToRemoveId = Uuid::fromString($this->hospitals[0]);

        // Then
        $this->expectException(HospitalCannotBeRemovedFromPatient::class);
        $this->expectExceptionMessage(HospitalCannotBeRemovedFromPatient::becausePatientMustHaveAtLeastOneHospital($patient->patientId()->toString())->getMessage());

        // When
        $patient->removeHospital($hospitalToRemoveId);
    }

    // --- Activation/Deactivation Tests ---

    public function testCanActivateAndDeactivatePatient(): void
    {
        // Given
        $patient = $this->createDefaultPatient();
        $deactivationReason = 'Testowy powód dezaktywacji';

        // When - najpierw dezaktywujemy
        $patient->deactivate($deactivationReason);

        // Then
        self::assertFalse($patient->isActive(), 'Pacjent powinien być nieaktywny po dezaktywacji');

        // When - następnie aktywujemy ponownie
        $patient->activate();

        // Then
        self::assertTrue($patient->isActive(), 'Pacjent powinien być aktywny po aktywacji');
    }

    public function testCanUpdateDeceasedStatus(): void
    {
        // Given
        $patient = $this->createDefaultPatient();

        // When
        $patient->updateDeceasedStatus(true);

        // Then
        // Brak gettera dla isDeceased, więc nie możemy bezpośrednio zweryfikować
        // Sprawdzamy, czy operacja nie rzuca wyjątku
        self::assertTrue(true, 'Aktualizacja statusu zgonu powinna zakończyć się sukcesem');
    }

    // --- Protocol Management Tests ---

    public function testCanActivatePatientProtocol(): void
    {
        // Given
        $patient = $this->createDefaultPatient();
        // Dodajemy nowy protokół, który początkowo będzie nieaktywny
        $patientId = $patient->patientId();
        $newProtocolId = Uuid::generate();
        $protocol = Protocol::ALCL;

        // Tworzymy i dodajemy rzeczywisty protokół, który ręcznie dezaktywujemy
        $patientProtocol = \App\Patient\Domain\PatientProtocol::create($newProtocolId, $patient, $protocol);

        // Ręcznie ustawiamy protokół jako nieaktywny
        $reflectionClass = new \ReflectionClass($patientProtocol);
        $isActiveProperty = $reflectionClass->getProperty('isActive');
        $isActiveProperty->setAccessible(true);
        $isActiveProperty->setValue($patientProtocol, false);

        // Dodajemy protokół do kolekcji
        $reflectionClass = new \ReflectionClass(Patient::class);
        $protocolsProperty = $reflectionClass->getProperty('protocols');
        $protocolsProperty->setAccessible(true);
        $protocols = $protocolsProperty->getValue($patient);
        $protocols->add($patientProtocol);

        // When
        $patient->activatePatientProtocol($newProtocolId);

        // Then
        self::assertTrue($patientProtocol->isActive(), 'Protokół powinien być aktywny po aktywacji');
    }

    public function testCannotActivateAlreadyActivePatientProtocol(): void
    {
        // Given
        $patient = $this->createDefaultPatient();
        $alclProtocol = $patient->protocols()->filter(fn ($p) => $p->protocol()->isEqualsTo(Protocol::ALCL))->first();
        self::assertNotFalse($alclProtocol, 'Protokół ALCL powinien istnieć');
        $alclProtocolId = $alclProtocol->patientProtocolId();

        // Then
        $this->expectException(\App\Patient\Domain\Exception\PatientProtocolCannotBeActivated::class);

        // When
        $patient->activatePatientProtocol($alclProtocolId);
    }

    public function testCannotActivateNonexistentPatientProtocol(): void
    {
        // Given
        $patient = $this->createDefaultPatient();
        $nonexistentProtocolId = Uuid::generate();

        // Then
        $this->expectException(\App\Patient\Domain\Exception\PatientProtocolCannotBeActivated::class);

        // When
        $patient->activatePatientProtocol($nonexistentProtocolId);
    }

    public function testCanDeactivatePatientProtocol(): void
    {
        // Given
        $patient = $this->createDefaultPatient();

        // Dodajemy drugi aktywny protokół (oprócz FOLLOWUP), aby można było dezaktywować pierwszy
        $patientId = $patient->patientId();
        $secondProtocolId = Uuid::generate();
        $protocol = Protocol::ALCL;

        // Tworzymy i dodajemy drugi aktywny protokół
        $secondProtocol = \App\Patient\Domain\PatientProtocol::create($secondProtocolId, $patient, Protocol::ALCL);

        // Tworzymy i dodajemy nieaktywny protokół
        $inactiveProtocolId = Uuid::generate();
        $inactiveProtocol = \App\Patient\Domain\PatientProtocol::create($inactiveProtocolId, $patient, Protocol::ALCL);

        // Ręcznie ustawiamy drugi protokół jako aktywny
        $reflectionClass = new \ReflectionClass($secondProtocol);
        $isActiveProperty = $reflectionClass->getProperty('isActive');
        $isActiveProperty->setAccessible(true);
        $isActiveProperty->setValue($secondProtocol, true);

        // Dodajemy protokoły do kolekcji
        $reflectionClass = new \ReflectionClass(Patient::class);
        $protocolsProperty = $reflectionClass->getProperty('protocols');
        $protocolsProperty->setAccessible(true);
        $protocols = $protocolsProperty->getValue($patient);
        $protocols->add($secondProtocol);
        $protocols->add($inactiveProtocol);

        // Pobieramy pierwszy protokół ALCL, który chcemy dezaktywować
        $alclProtocol = $patient->protocols()->filter(
            fn ($p) => $p->protocol()->isEqualsTo(Protocol::ALCL) && $p !== $secondProtocol
        )->first();
        self::assertNotFalse($alclProtocol, 'Protokół ALCL powinien istnieć');
        $alclProtocolId = $alclProtocol->patientProtocolId();

        // When
        $patient->deactivatePatientProtocol($alclProtocolId);

        // Then
        self::assertFalse($alclProtocol->isActive(), 'Pierwszy protokół powinien być nieaktywny po dezaktywacji');
    }

    public function testCannotDeactivateLastActivePatientProtocol(): void
    {
        // Given
        $patient = $this->createDefaultPatient();
        $alclProtocol = $patient->protocols()->filter(fn ($p) => $p->protocol()->isEqualsTo(Protocol::ALCL))->first();
        self::assertNotFalse($alclProtocol, 'Protokół ALCL powinien istnieć');
        $alclProtocolId = $alclProtocol->patientProtocolId();

        // Then
        $this->expectException(\App\Patient\Domain\Exception\PatientProtocolCannotBeDeactivated::class);
        $this->expectExceptionMessage(\App\Patient\Domain\Exception\PatientProtocolCannotBeDeactivated::becauseAtLeastOnePatientProtocolMustBeActive($patient->patientId()->toString())->getMessage());

        // When
        $patient->deactivatePatientProtocol($alclProtocolId);
    }

    public function testCannotDeactivateAlreadyDeactivatedPatientProtocol(): void
    {
        // Given
        $patient = $this->createDefaultPatient();

        // Dodajemy drugi aktywny protokół, aby można było dezaktywować pierwszy
        $patientId = $patient->patientId();
        $secondProtocolId = Uuid::generate();
        $inactiveProtocolId = Uuid::generate();

        // Tworzymy i dodajemy drugi aktywny protokół
        $secondProtocol = \App\Patient\Domain\PatientProtocol::create($secondProtocolId, $patient, Protocol::ALCL);

        // Tworzymy i dodajemy nieaktywny protokół
        $inactiveProtocol = \App\Patient\Domain\PatientProtocol::create($inactiveProtocolId, $patient, Protocol::ALCL);

        // Ręcznie ustawiamy nieaktywny protokół jako nieaktywny
        $reflectionClass = new \ReflectionClass($inactiveProtocol);
        $isActiveProperty = $reflectionClass->getProperty('isActive');
        $isActiveProperty->setAccessible(true);
        $isActiveProperty->setValue($inactiveProtocol, false);

        // Dodajemy protokoły do kolekcji
        $reflectionClass = new \ReflectionClass(Patient::class);
        $protocolsProperty = $reflectionClass->getProperty('protocols');
        $protocolsProperty->setAccessible(true);
        $protocols = $protocolsProperty->getValue($patient);
        $protocols->add($secondProtocol);
        $protocols->add($inactiveProtocol);

        // Then
        $this->expectException(\App\Patient\Domain\Exception\PatientProtocolCannotBeDeactivated::class);
        $this->expectExceptionMessage(\App\Patient\Domain\Exception\PatientProtocolCannotBeDeactivated::becauseIsAlreadyDeactivated($patient->patientId()->toString(), $inactiveProtocolId->toString())->getMessage());

        // When
        $patient->deactivatePatientProtocol($inactiveProtocolId);
    }

    // --- Protocol Update Tests ---

    public function testCanUpdatePatientProtocolIcd10Code(): void
    {
        // Utwórz pacjenta z domyślnymi danymi
        $patient = $this->createDefaultPatient();

        // Pobierz pierwszy protokół dla ustawienia kodu ICD10
        $protocol = $patient->protocols()->first();
        self::assertNotFalse($protocol, 'Protokół powinien istnieć');
        $protocolId = $protocol->patientProtocolId();

        // Ustaw kod ICD10
        $icd10Code = 'C43.9';
        $patient->updatePatientProtocolIcd10Code($protocolId, $icd10Code);

        // Pobierz protokół ponownie, aby sprawdzić czy kod został ustawiony
        $updatedProtocol = $patient->protocols()->filter(fn ($p) => $p->patientProtocolId()->equals($protocolId))->first();
        self::assertNotFalse($updatedProtocol, 'Protokół powinien istnieć po aktualizacji');

        // Wykorzystaj refleksję do sprawdzenia prywatnego pola
        $reflectionProperty = new \ReflectionProperty($updatedProtocol, 'icd10Code');
        $reflectionProperty->setAccessible(true);
        $actualIcd10Code = $reflectionProperty->getValue($updatedProtocol);
        self::assertSame($icd10Code, $actualIcd10Code);
    }

    public function testCanUpdatePatientProtocolTreatmentStartDate(): void
    {
        // Utwórz pacjenta z domyślnymi danymi
        $patient = $this->createDefaultPatient();

        // Pobierz pierwszy protokół dla ustawienia daty rozpoczęcia leczenia
        $protocol = $patient->protocols()->first();
        self::assertNotFalse($protocol, 'Protokół powinien istnieć');
        $protocolId = $protocol->patientProtocolId();

        // Ustaw datę rozpoczęcia leczenia
        $startDate = new \DateTimeImmutable('2023-01-01');
        $patient->updatePatientProtocolTreatmentStartDate($protocolId, $startDate);

        // Pobierz protokół ponownie, aby sprawdzić czy data została ustawiona
        $updatedProtocol = $patient->protocols()->filter(fn ($p) => $p->patientProtocolId()->equals($protocolId))->first();
        self::assertNotFalse($updatedProtocol, 'Protokół powinien istnieć po aktualizacji');

        // Wykorzystaj refleksję do sprawdzenia prywatnego pola
        $reflectionProperty = new \ReflectionProperty($updatedProtocol, 'treatmentStartDate');
        $reflectionProperty->setAccessible(true);
        $actualStartDate = $reflectionProperty->getValue($updatedProtocol);
        self::assertEquals($startDate, $actualStartDate);
    }

    public function testCannotUpdateNonexistentPatientProtocol(): void
    {
        // Utwórz pacjenta z domyślnymi danymi
        $patient = $this->createDefaultPatient();

        // Szukamy protokołu którego nie ma w pacjencie
        $nonExistentId = Uuid::generate();

        // Próba aktualizacji nieistniejącego protokołu powinna rzucić wyjątek
        $this->expectException(\App\Patient\Domain\Exception\PatientProtocolCannotBeUpdated::class);
        $patient->updatePatientProtocolIcd10Code($nonExistentId, 'C50.9');
    }

    // --- Patient Attachment Tests ---

    public function testCanAddAndRemovePatientAttachment(): void
    {
        // Given
        $patient = $this->createDefaultPatient();
        $attachmentId = Uuid::generate();
        $attachmentType = \App\Patient\Domain\PatientAttachmentType::EPIKRYZA;
        $fileName = 'test.pdf';
        $extension = 'pdf';

        // Tworzymy rzeczywisty obiekt załącznika
        $attachment = \App\Patient\Domain\PatientAttachment::create($attachmentId, $patient, $attachmentType, $fileName, $extension);

        // When - dodajemy załącznik
        $patient->addPatientAttachment($attachment);

        // Then
        self::assertCount(1, $patient->attachments(), 'Pacjent powinien mieć jeden załącznik');
        self::assertSame($attachment, $patient->getPatientAttachment($attachmentId), 'Powinniśmy móc pobrać dodany załącznik');

        // When - usuwamy załącznik
        $patient->removePatientAttachment($attachmentId);

        // Then
        self::assertCount(0, $patient->attachments(), 'Pacjent nie powinien mieć załączników po usunięciu');
    }

    public function testCannotRemoveNonexistentPatientAttachment(): void
    {
        // Given
        $patient = $this->createDefaultPatient();
        $nonexistentAttachmentId = Uuid::generate();

        // Then
        $this->expectException(\App\Patient\Domain\Exception\PatientAttachmentNotFound::class);

        // When
        $patient->removePatientAttachment($nonexistentAttachmentId);
    }

    public function testCannotGetNonexistentPatientAttachment(): void
    {
        // Given
        $patient = $this->createDefaultPatient();
        $nonexistentAttachmentId = Uuid::generate();

        // Then
        $this->expectException(\App\Patient\Domain\Exception\PatientAttachmentNotFound::class);

        // When
        $patient->getPatientAttachment($nonexistentAttachmentId);
    }

    // --- RODO Consent Tests ---

    public function testCanAddAndGetPatientRodoConsent(): void
    {
        // Given
        $patient = $this->createDefaultPatient();
        $consentId = Uuid::generate();
        $fileName = 'rodo_zgoda.pdf';
        $extension = 'pdf';

        // Tworzymy rzeczywisty obiekt zgody RODO
        $rodoConsent = \App\Patient\Domain\PatientRodoConsent::create($consentId, $fileName, $extension, $patient);

        // When - dodajemy zgodę RODO
        $patient->addPatientRodoConsent($rodoConsent);

        // Then
        self::assertCount(1, $patient->rodoConsent(), 'Pacjent powinien mieć jedną zgodę RODO');
        self::assertSame($rodoConsent, $patient->getPatientRodoConsent($consentId), 'Powinniśmy móc pobrać dodaną zgodę RODO');
    }

    public function testCannotGetNonexistentPatientRodoConsent(): void
    {
        // Given
        $patient = $this->createDefaultPatient();
        $nonexistentConsentId = Uuid::generate();

        // Then
        $this->expectException(\App\Patient\Domain\Exception\PatientRodoConsentNotFound::class);

        // When
        $patient->getPatientRodoConsent($nonexistentConsentId);
    }
}
