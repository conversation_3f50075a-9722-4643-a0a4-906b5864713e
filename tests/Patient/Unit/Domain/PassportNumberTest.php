<?php

declare(strict_types=1);

namespace App\Tests\Patient\Unit\Domain;

use App\Patient\Domain\PassportNumber;
use PHPUnit\Framework\TestCase;

class PassportNumberTest extends TestCase
{
    public function testCreateWithValidNumber(): void
    {
        // Given
        $number = 'AB123456';

        // When
        $passportNumber = PassportNumber::create($number);

        // Then
        self::assertEquals($number, $passportNumber->passportNumber());
    }

    public function testCreateWithInvalidNumberShouldThrowError(): void
    {
        // Given
        $number = 'AB12345678901234567890'; // More than 20 characters

        // Then
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Numer paszportu nie może przekraczać 20 znaków.');

        // When
        $passportNumber = PassportNumber::create($number);
    }

    public function testCreateWithNonAlphanumericNumberShouldThrowError(): void
    {
        // Given
        $number = 'AB123456!'; // Contains non-alphanumeric character

        // Then
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Numer paszportu może składać się tylko z liter i cyfr.');

        // When
        $passportNumber = PassportNumber::create($number);
    }

    public function testIsEqualsTo(): void
    {
        // Given
        $number1 = PassportNumber::create('AB123456');
        $number2 = PassportNumber::create('AB123456');
        $number3 = PassportNumber::create('CD123456');

        // When & Then
        self::assertTrue($number1->isEqualsTo($number2));
        self::assertFalse($number1->isEqualsTo($number3));
    }

    public function testJsonSerialize(): void
    {
        // Given
        $number = PassportNumber::create('AB123456');

        // When
        $json = $number->jsonSerialize();

        // Then
        self::assertIsString($json);
        self::assertEquals('AB123456', $json);
    }
}
