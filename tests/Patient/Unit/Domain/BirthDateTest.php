<?php

declare(strict_types=1);

namespace App\Tests\Patient\Unit\Domain;

use App\Patient\Domain\BirthDate;
use PHPUnit\Framework\TestCase;

class BirthDateTest extends TestCase
{
    public function testCreateFromStringWithValidDate(): void
    {
        // Given
        $birthDate = '1980-01-01';

        // When
        $date = BirthDate::createFromString($birthDate);

        // Then
        self::assertEquals($birthDate, $date->birthDateString());
    }

    public function testCreateFromStringWithInvalidDateShouldThrowError(): void
    {
        // Given
        $birthDate = 'invalid-date';

        // Then
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Nieprawidłowy format daty urodzenia.');

        // When
        $date = BirthDate::createFromString($birthDate);
    }

    public function testCreateFromStringWithFutureDateShouldThrowError(): void
    {
        // Given
        $birthDate = '3000-01-01';

        // Then
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Data urodzenia nie może być w przyszłości.');

        // When
        $date = BirthDate::createFromString($birthDate);
    }

    public function testIsEqualsTo(): void
    {
        // Given
        $date1 = BirthDate::createFromString('1980-01-01');
        $date2 = BirthDate::createFromString('1980-01-01');
        $date3 = BirthDate::createFromString('1990-01-01');

        // When & Then
        self::assertTrue($date1->isEqualsTo($date2));
        self::assertFalse($date1->isEqualsTo($date3));
    }

    public function testJsonSerialize(): void
    {
        // Given
        $date = BirthDate::createFromString('1980-01-01');

        // When
        $json = $date->jsonSerialize();

        // Then
        self::assertIsString($json);
        self::assertEquals('1980-01-01', $json);
    }
}
