<?php

declare(strict_types=1);

namespace App\Tests\Patient\Integration;

use App\Common\Uuid;
use App\Patient\Application\PatientFacade;
use App\Patient\Application\Query\PatientQueryInterface;
use App\Patient\Application\Service\PatientService;
use App\Patient\Application\Service\PeselService;
use App\Patient\Domain\Gender;
use App\Patient\Domain\PeselDetails;
use App\SharedKernel\Protocol;
use App\Tests\Patient\DataFactory\PatientAttachmentDtoFactory;
use App\Tests\Patient\DataFactory\PatientDtoFactory;
use App\Tests\Patient\DataFactory\PeselFactory;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class PatientFacadeTest extends KernelTestCase
{
    private PatientFacade $facade;

    protected function setUp(): void
    {
        parent::setUp();

        // Pobieramy zależności z kontenera
        $patientService = self::getContainer()->get(PatientService::class);
        $peselService = self::getContainer()->get(PeselService::class);
        $patientQuery = self::getContainer()->get(PatientQueryInterface::class);

        $this->facade = new PatientFacade($patientService, $peselService, $patientQuery);

        // Włącz wsparcie dla punktów zapisu transakcji
        $connection = self::getContainer()->get('doctrine.dbal.default_connection');
        $connection->setNestTransactionsWithSavepoints(true);
        $connection->beginTransaction(); // Rozpocznij transakcję
    }

    protected function tearDown(): void
    {
        // Wycofaj transakcję po każdym teście
        $connection = self::getContainer()->get('doctrine.dbal.default_connection');
        if ($connection->isTransactionActive()) {
            $connection->rollBack();
        }

        parent::tearDown();
    }

    /** @return array<string, mixed> */
    private function _createPatientForTest(): array
    {
        $patientId = Uuid::generate()->toString();

        // Użyj fabryki do utworzenia DTO pacjenta
        $createDto = PatientDtoFactory::createPatientDto();

        // Utwórz pacjenta
        $this->facade->create($patientId, $createDto);

        return [
            'patientId' => $patientId,
            'pesel' => $createDto->pesel,
            'firstName' => $createDto->firstName,
            'lastName' => $createDto->lastName,
            'email' => $createDto->email,
            'contactNumber' => $createDto->contactNumber,
            'protocol' => $createDto->protocol,
            'hospitals' => $createDto->hospitals,
            'createDto' => $createDto, // Przechowujemy też oryginalny DTO
        ];
    }

    public function testCreateUpdateActivateDeactivatePatient(): void
    {
        // Utwórz pacjenta za pomocą metody pomocniczej
        $patientData = $this->_createPatientForTest();
        $patientId = $patientData['patientId'];
        $pesel = $patientData['pesel'];
        $createDto = $patientData['createDto'];

        // Sprawdź czy pacjent został poprawnie utworzony
        $patientView = $this->facade->findByPatientId($patientId);

        self::assertNotNull($patientView);
        self::assertSame($patientId, $patientView->patientId());
        self::assertSame($patientData['firstName'], $patientView->firstName());
        self::assertSame($patientData['lastName'], $patientView->lastName());
        self::assertSame($pesel, $patientView->jsonSerialize()['pesel']);
        self::assertContains($patientData['protocol'], $patientView->protocolsValueList());
        self::assertSame($patientData['hospitals'], $patientView->hospitals());

        // Aktualizuj pacjenta używając fabryki
        $updatedFirstName = 'Adam';
        $updatedLastName = 'Nowak';
        $updatedContactNumber = '987654321'.random_int(1, 99999);

        $updateDto = PatientDtoFactory::createUpdateDto($createDto, [
            'firstName' => $updatedFirstName,
            'lastName' => $updatedLastName,
            'contactNumber' => $updatedContactNumber,
        ]);

        $this->facade->update($patientId, $updateDto);

        // Sprawdź aktualizację pacjenta
        $updatedPatientView = $this->facade->findByPatientId($patientId);

        self::assertNotNull($updatedPatientView);
        self::assertSame($patientId, $updatedPatientView->patientId());
        self::assertSame($updatedFirstName, $updatedPatientView->firstName());
        self::assertSame($updatedLastName, $updatedPatientView->lastName());
        self::assertSame($updatedContactNumber, $updatedPatientView->jsonSerialize()['contactNumber']);

        // Deaktywuj pacjenta
        $deactivationReason = 'Powód deaktywacji';
        $this->facade->deactivate($patientId, $deactivationReason);

        $deactivatedPatientView = $this->facade->findByPatientId($patientId);
        self::assertNotNull($deactivatedPatientView);
        self::assertFalse($deactivatedPatientView->jsonSerialize()['isActive']);
        self::assertSame($deactivationReason, $deactivatedPatientView->jsonSerialize()['deactivationReason']); // Sprawdź powód

        // Aktywuj pacjenta
        $this->facade->activate($patientId);

        $activatedPatientView = $this->facade->findByPatientId($patientId);
        self::assertNotNull($activatedPatientView);
        self::assertTrue($activatedPatientView->jsonSerialize()['isActive']);
        self::assertNull($activatedPatientView->jsonSerialize()['deactivationReason']); // Powód powinien być null po aktywacji
    }

    public function testAddAndRemoveHospital(): void
    {
        // Utwórz pacjenta na potrzeby tego testu
        $patientData = $this->_createPatientForTest();
        $patientId = $patientData['patientId'];

        // Dodaj szpital do pacjenta
        $newHospitalId = '23cd98bf-5c0e-472f-a4ef-31e28422f0ec'; // przykładowe ID
        $this->facade->addHospital($patientId, $newHospitalId);

        // Sprawdź czy szpital został dodany
        $patientView = $this->facade->findByPatientId($patientId);
        self::assertNotNull($patientView);
        self::assertContains($newHospitalId, $patientView->hospitals());

        // Usuń szpital z pacjenta
        $this->facade->removeHospital($patientId, $newHospitalId);

        // Sprawdź czy szpital został usunięty
        $patientView = $this->facade->findByPatientId($patientId);
        self::assertNotNull($patientView);
        self::assertNotContains($newHospitalId, $patientView->hospitals());
    }

    public function testAddAndManagePatientProtocol(): void
    {
        // Utwórz pacjenta na potrzeby tego testu
        $patientData = $this->_createPatientForTest();
        $patientId = $patientData['patientId'];

        // Dodaj protokół do pacjenta
        $patientProtocolId = Uuid::generate()->toString();
        $protocol = Protocol::STS->value;
        $this->facade->addPatientProtocol($patientId, $patientProtocolId, $protocol);

        // Sprawdź czy protokół został dodany
        $patientView = $this->facade->findByPatientId($patientId);
        self::assertNotNull($patientView);
        self::assertContains($protocol, $patientView->protocolsValueList());

        // Znajdź dodany protokół w widoku
        $addedProtocolView = null;
        foreach ($patientView->protocols() as $pView) {
            if ($pView->patientProtocolId() === $patientProtocolId) {
                $addedProtocolView = $pView;
                break;
            }
        }
        self::assertNotNull($addedProtocolView, 'Nie znaleziono dodanego protokołu.');
        self::assertTrue($addedProtocolView->isActive(), 'Nowy protokół powinien być aktywny.');

        // Zaktualizuj kod ICD10 dla protokołu
        $icd10Code = 'C40.0';
        $this->facade->updatePatientProtocolIcd10Code($patientId, $patientProtocolId, $icd10Code);
        $patientView = $this->facade->findByPatientId($patientId); // Odśwież widok
        self::assertNotNull($patientView);
        $updatedProtocolView = $patientView->protocols()->filter(fn ($p) => $p->patientProtocolId() === $patientProtocolId)->first();
        self::assertNotFalse($updatedProtocolView, 'Nie znaleziono zaktualizowanego protokołu.');
        self::assertSame($icd10Code, $updatedProtocolView->icd10Code());

        // Zaktualizuj datę rozpoczęcia leczenia
        $treatmentStartDate = new \DateTimeImmutable('2023-01-01');
        $this->facade->updatePatientProtocolStartTreatment($patientId, $patientProtocolId, $treatmentStartDate);
        $patientView = $this->facade->findByPatientId($patientId); // Odśwież widok
        self::assertNotNull($patientView);
        $updatedProtocolView = $patientView->protocols()->filter(fn ($p) => $p->patientProtocolId() === $patientProtocolId)->first();
        self::assertNotFalse($updatedProtocolView, 'Nie znaleziono zaktualizowanego protokołu.');
        self::assertEquals($treatmentStartDate, $updatedProtocolView->treatmentStartDate());

        // Deaktywuj protokół
        $this->facade->deactivatePatientProtocol($patientId, $patientProtocolId);
        $patientView = $this->facade->findByPatientId($patientId); // Odśwież widok
        self::assertNotNull($patientView);
        $deactivatedProtocolView = $patientView->protocols()->filter(fn ($p) => $p->patientProtocolId() === $patientProtocolId)->first();
        self::assertNotFalse($deactivatedProtocolView, 'Nie znaleziono deaktywowanego protokołu.');
        self::assertFalse($deactivatedProtocolView->isActive());

        // Aktywuj protokół
        $this->facade->activatePatientProtocol($patientId, $patientProtocolId);
        $patientView = $this->facade->findByPatientId($patientId); // Odśwież widok
        self::assertNotNull($patientView);
        $activatedProtocolView = $patientView->protocols()->filter(fn ($p) => $p->patientProtocolId() === $patientProtocolId)->first();
        self::assertNotFalse($activatedProtocolView, 'Nie znaleziono aktywowanego protokołu.');
        self::assertTrue($activatedProtocolView->isActive());
    }

    public function testGetPeselDetails(): void
    {
        // Generujemy prawidłowy PESEL do sprawdzenia za pomocą fabryki
        $pesel = PeselFactory::generate('1990-09-05', true);

        // Pobierz szczegóły PESEL - ten test nie wymaga tworzenia pacjenta w DB
        $peselDetails = $this->facade->getPeselDetails($pesel);

        self::assertInstanceOf(PeselDetails::class, $peselDetails);
        self::assertSame($pesel, $peselDetails->pesel()->pesel());
        self::assertSame('1990-09-05', $peselDetails->birthDate()->birthDateString());
        self::assertSame(Gender::MALE, $peselDetails->gender());
    }

    public function testPatientAttachments(): void
    {
        // Utwórz pacjenta na potrzeby tego testu
        $patientData = $this->_createPatientForTest();
        $patientId = $patientData['patientId'];

        // Utwórz załącznik za pomocą fabryki
        $fileName = 'wyniki_badan.pdf';
        $attachmentDto = PatientAttachmentDtoFactory::create($fileName);

        // Dodaj załącznik do pacjenta
        $attachmentId = $this->facade->addPatientAttachment($patientId, $attachmentDto);
        self::assertNotEmpty($attachmentId);

        // Sprawdź czy załącznik został dodany
        $patientView = $this->facade->findByPatientId($patientId);
        self::assertNotNull($patientView);
        self::assertCount(1, $patientView->attachments());
        $addedAttachment = $patientView->attachments()->first();
        self::assertNotFalse($addedAttachment, 'Nie znaleziono dodanego załącznika.');
        self::assertSame($attachmentId, $addedAttachment->patientAttachmentId());

        // Użyj metody z fabryki do sprawdzenia sanityzowanej nazwy pliku
        $expectedSanitizedName = PatientAttachmentDtoFactory::getSanitizedFileName($fileName);
        self::assertSame($expectedSanitizedName, $addedAttachment->fileName());
        self::assertSame($attachmentDto->type, $addedAttachment->type());

        // Pobierz ścieżkę do pliku załącznika
        $filePath = $this->facade->getPatientAttachmentFilePath($patientId, $attachmentId);
        self::assertStringContainsString($patientId, $filePath);
        self::assertStringContainsString($attachmentId.'.'.$attachmentDto->extension, $filePath);

        // Usuń załącznik
        $this->facade->deletePatientAttachment($patientId, $attachmentId);

        // Sprawdź czy załącznik został usunięty
        $patientView = $this->facade->findByPatientId($patientId);
        self::assertNotNull($patientView);
        self::assertCount(0, $patientView->attachments());
    }

    public function testUpdateDecreaseStatus(): void
    {
        // Utwórz pacjenta na potrzeby tego testu
        $patientData = $this->_createPatientForTest();
        $patientId = $patientData['patientId'];

        // Ustaw pacjenta jako zmarłego
        $this->facade->updateDecreaseStatus($patientId, true);

        // Sprawdź czy status został zaktualizowany
        $patientView = $this->facade->findByPatientId($patientId);
        self::assertNotNull($patientView);

        $jsonData = $patientView->jsonSerialize();
        self::assertTrue($jsonData['isDeceased']);

        // Ustaw pacjenta jako żyjącego
        $this->facade->updateDecreaseStatus($patientId, false);

        // Sprawdź czy status został zaktualizowany
        $patientView = $this->facade->findByPatientId($patientId);
        self::assertNotNull($patientView);

        $jsonData = $patientView->jsonSerialize();
        self::assertFalse($jsonData['isDeceased']);
    }
}
