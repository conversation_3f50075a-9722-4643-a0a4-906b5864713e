<?php

declare(strict_types=1);

namespace App\Tests\Patient\DataFactory;

/**
 * Klasa pomocnicza do generowania prawidłowych numerów PESEL na potrzeby testów.
 */
class PeselFactory
{
    /**
     * Generuje prawidłowy numer PESEL dla osoby urodzonej w podanej dacie.
     *
     * @param string $birthDate Data urodzenia w formacie 'Y-m-d'
     * @param bool   $isMale    Czy generować PESEL dla mężczyzny (true) czy kobiety (false)
     *
     * @return string Wygenerowany numer PESEL
     */
    public static function generate(string $birthDate = '1990-09-05', bool $isMale = true): string
    {
        // Parsuj datę urodzenia
        $date = \DateTime::createFromFormat('Y-m-d', $birthDate);
        if (!$date) {
            throw new \InvalidArgumentException('Nieprawidłowy format daty. Oczekiwano Y-m-d.');
        }

        // Pobierz elementy daty
        $year = (int) $date->format('Y');
        $yearLastTwoDigits = $date->format('y');
        $month = (int) $date->format('m');
        $day = $date->format('d');

        // Sprawdź, czy rok jest obsługiwany
        if ($year < 1800 || $year >= 2300) {
            throw new \InvalidArgumentException('Obsługiwane lata to 1800-2299');
        }

        // Dostosuj miesiąc na podstawie stulecia
        if ($year < 1900) {  // 1800-1899
            $month += 80;
        } elseif ($year >= 2000 && $year < 2100) {  // 2000-2099
            $month += 20;
        } elseif ($year >= 2100 && $year < 2200) {  // 2100-2199
            $month += 40;
        } elseif ($year >= 2200) {  // 2200-2299
            $month += 60;
        }
        // dla lat 1900-1999 miesiąc pozostaje bez zmian

        // Dodaj zera wiodące dla miesiąca i dnia
        $monthString = str_pad((string) $month, 2, '0', STR_PAD_LEFT);
        $dayString = str_pad($day, 2, '0', STR_PAD_LEFT);

        // Generuj losowe 3 cyfry numeru seryjnego
        $serial = str_pad((string) random_int(0, 999), 3, '0', STR_PAD_LEFT);

        // Ostatnia cyfra numeru seryjnego - nieparzysta oznacza mężczyznę, parzysta kobietę
        $gender = $isMale ? '1' : '0';

        // Tymczasowy PESEL bez cyfry kontrolnej
        $tempPesel = $yearLastTwoDigits.$monthString.$dayString.$serial.$gender;

        // Wagi do obliczenia cyfry kontrolnej
        $weights = [1, 3, 7, 9, 1, 3, 7, 9, 1, 3];

        // Obliczanie sumy kontrolnej
        $sum = 0;
        for ($i = 0; $i < 10; ++$i) {
            $sum += (int) $tempPesel[$i] * $weights[$i];
        }

        // Cyfra kontrolna
        $checksum = (10 - ($sum % 10)) % 10;

        // Pełny PESEL
        return $tempPesel.$checksum;
    }
}
