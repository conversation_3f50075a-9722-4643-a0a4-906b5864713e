<?php

declare(strict_types=1);

namespace App\Tests\Patient\DataFactory;

use App\BackendForFrontend\Security\Patient\Dto\AddPatientRodoConsentDto;
use App\BackendForFrontend\Security\Patient\Dto\AddressDto;
use App\BackendForFrontend\Security\Patient\Dto\CreatePatientDto;
use App\BackendForFrontend\Security\Patient\Dto\UpdatePatientDto;
use App\Patient\Domain\Citizenship;
use App\Patient\Domain\Gender;
use App\SharedKernel\Protocol;

/**
 * Klasa pomocnicza do generowania obiektów DTO dla pacjentów na potrzeby testów.
 */
class PatientDtoFactory
{
    /** Tworzy domyślny obiekt AddressDto */
    public static function createAddressDto(
        string $city = 'Warszawa',
        string $streetWithNumber = 'Przykładowa 10/5',
        string $postCode = '00-001',
    ): AddressDto {
        $addressDto = new AddressDto();
        $addressDto->city = $city;
        $addressDto->streetWithNumber = $streetWithNumber;
        $addressDto->postCode = $postCode;

        return $addressDto;
    }

    /** Tworzy domyślny obiekt AddPatientRodoConsentDto */
    public static function createRodoConsentDto(
        string $fileName = 'zgoda_rodo.pdf',
        string $extension = 'pdf',
        string $content = 'testowy plik PDF',
    ): AddPatientRodoConsentDto {
        $rodoConsentDto = new AddPatientRodoConsentDto();
        $rodoConsentDto->fileName = $fileName;
        $rodoConsentDto->extension = $extension;
        $rodoConsentDto->fileContentBase64 = base64_encode($content);

        return $rodoConsentDto;
    }

    /**
     * Tworzy domyślny obiekt CreatePatientDto z możliwością nadpisania dowolnych pól.
     *
     * @param array<string, mixed> $overrides Tablica z polami do nadpisania
     */
    public static function createPatientDto(array $overrides = []): CreatePatientDto
    {
        // Generuj losowy PESEL jeśli nie został podany
        if (!isset($overrides['pesel'])) {
            $birthDate = $overrides['birthDate'] ?? '1990-09-05';
            $isMale = isset($overrides['gender']) ? $overrides['gender'] === Gender::MALE->value : true;
            $overrides['pesel'] = PeselFactory::generate($birthDate, $isMale);
        }

        $createDto = new CreatePatientDto();

        // Ustaw domyślne wartości
        $createDto->firstName = $overrides['firstName'] ?? 'Jan';
        $createDto->lastName = $overrides['lastName'] ?? 'Kowalski';
        $createDto->citizenship = $overrides['citizenship'] ?? Citizenship::PL->value;
        $createDto->isRegisteredAddressSameAsResidence = $overrides['isRegisteredAddressSameAsResidence'] ?? true;
        $createDto->isCorrespondenceAddressSameAsResidence = $overrides['isCorrespondenceAddressSameAsResidence'] ?? true;
        $createDto->residenceAddress = $overrides['residenceAddress'] ?? self::createAddressDto();
        $createDto->registeredAddress = $overrides['registeredAddress'] ?? null;
        $createDto->correspondenceAddress = $overrides['correspondenceAddress'] ?? null;
        $createDto->email = $overrides['email'] ?? 'jan.kowalski'.random_int(1, 99999).'@example.com';
        $createDto->contactNumber = $overrides['contactNumber'] ?? '*********'.random_int(1, 99999);
        $createDto->isPatientIdentified = $overrides['isPatientIdentified'] ?? true;
        $createDto->pesel = $overrides['pesel'];
        $createDto->passportNumber = $overrides['passportNumber'] ?? null;
        $createDto->legalRepresentativePesel = $overrides['legalRepresentativePesel'] ?? null;
        $createDto->legalRepresentativePassportNumber = $overrides['legalRepresentativePassportNumber'] ?? null;
        $createDto->gender = $overrides['gender'] ?? Gender::MALE->value;
        $createDto->birthDate = $overrides['birthDate'] ?? '1990-09-05';
        $createDto->hospitals = $overrides['hospitals'] ?? ['13cd98bf-5c0e-472f-a4ef-31e28422f0ec'];
        $createDto->protocol = $overrides['protocol'] ?? Protocol::ALCL->value;
        $createDto->rodoConsent = $overrides['rodoConsent'] ?? self::createRodoConsentDto();

        return $createDto;
    }

    /**
     * Tworzy domyślny obiekt UpdatePatientDto na podstawie CreatePatientDto.
     *
     * @param CreatePatientDto     $createDto Obiekt CreatePatientDto jako bazowy
     * @param array<string, mixed> $overrides Tablica z polami do nadpisania
     */
    public static function createUpdateDto(CreatePatientDto $createDto, array $overrides = []): UpdatePatientDto
    {
        $updateDto = new UpdatePatientDto();

        // Kopiuj wartości z CreatePatientDto
        $updateDto->firstName = $overrides['firstName'] ?? $createDto->firstName;
        $updateDto->lastName = $overrides['lastName'] ?? $createDto->lastName;
        $updateDto->citizenship = $overrides['citizenship'] ?? $createDto->citizenship;
        $updateDto->isRegisteredAddressSameAsResidence = $overrides['isRegisteredAddressSameAsResidence'] ?? $createDto->isRegisteredAddressSameAsResidence;
        $updateDto->isCorrespondenceAddressSameAsResidence = $overrides['isCorrespondenceAddressSameAsResidence'] ?? $createDto->isCorrespondenceAddressSameAsResidence;
        $updateDto->residenceAddress = $overrides['residenceAddress'] ?? $createDto->residenceAddress;
        $updateDto->registeredAddress = $overrides['registeredAddress'] ?? $createDto->registeredAddress;
        $updateDto->correspondenceAddress = $overrides['correspondenceAddress'] ?? $createDto->correspondenceAddress;
        $updateDto->email = $overrides['email'] ?? $createDto->email;
        $updateDto->contactNumber = $overrides['contactNumber'] ?? $createDto->contactNumber;
        $updateDto->isPatientIdentified = $overrides['isPatientIdentified'] ?? $createDto->isPatientIdentified;
        $updateDto->pesel = $overrides['pesel'] ?? $createDto->pesel;
        $updateDto->passportNumber = $overrides['passportNumber'] ?? $createDto->passportNumber;
        $updateDto->legalRepresentativePesel = $overrides['legalRepresentativePesel'] ?? $createDto->legalRepresentativePesel;
        $updateDto->legalRepresentativePassportNumber = $overrides['legalRepresentativePassportNumber'] ?? $createDto->legalRepresentativePassportNumber;
        $updateDto->gender = $overrides['gender'] ?? $createDto->gender;
        $updateDto->birthDate = $overrides['birthDate'] ?? $createDto->birthDate;

        return $updateDto;
    }
}
