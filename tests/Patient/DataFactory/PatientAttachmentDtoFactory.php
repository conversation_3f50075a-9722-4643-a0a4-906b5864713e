<?php

declare(strict_types=1);

namespace App\Tests\Patient\DataFactory;

use App\BackendForFrontend\Security\Patient\Dto\AddPatientAttachmentDto;
use App\Patient\Domain\PatientAttachmentType;

/**
 * Klasa pomocnicza do generowania obiektów DTO dla załączników pacjenta na potrzeby testów.
 */
class PatientAttachmentDtoFactory
{
    /** Tworzy domyślny obiekt AddPatientAttachmentDto */
    public static function create(
        string $fileName = 'wyniki_badan.pdf',
        string $extension = 'pdf',
        string $content = 'testowa zawartość pliku PDF',
        ?string $type = null,
    ): AddPatientAttachmentDto {
        $attachmentDto = new AddPatientAttachmentDto();
        $attachmentDto->fileName = $fileName;
        $attachmentDto->extension = $extension;
        $attachmentDto->fileContentBase64 = base64_encode($content);
        $attachmentDto->type = $type ?? PatientAttachmentType::EPIKRYZA->value;

        return $attachmentDto;
    }

    /**
     * Zwraca oczekiwaną sanityzowaną nazwę pliku.
     *
     * Ta metoda symuluje działanie PatientAttachmentFileService::sanitizeFileName()
     */
    public static function getSanitizedFileName(string $fileName): string
    {
        // Usuń rozszerzenie pliku
        $nameWithoutExtension = pathinfo($fileName, PATHINFO_FILENAME);

        // Zamień podkreślenia na myślniki, usuń znaki specjalne itp.
        return str_replace('_', '-', $nameWithoutExtension);
    }
}
