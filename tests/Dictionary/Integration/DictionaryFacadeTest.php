<?php

declare(strict_types=1);

namespace App\Tests\Dictionary\Integration;

use App\Common\Query\GridConfiguration;
use App\Common\Query\GridField;
use App\Common\Query\GridFilterMethod;
use App\Common\Query\GridResult;
use App\Dictionary\DictionaryFacade;
use App\Dictionary\DictionaryService;
use App\SharedKernel\DictionaryType;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

class DictionaryFacadeTest extends KernelTestCase
{
    private DictionaryFacade $facade;

    protected function setUp(): void
    {
        parent::setUp();

        $dictionaryService = self::getContainer()->get(DictionaryService::class);
        $this->facade = new DictionaryFacade($dictionaryService);

        // Get the Doctrine DBAL Connection and enable savepoints
        $connection = self::getContainer()->get('doctrine.dbal.default_connection');
        $connection->setNestTransactionsWithSavepoints(true);
    }

    public function testAddDictionaryFacade(): void
    {
        // given
        $value = 'test_value'.random_int(0, 999999);
        $description = 'test_description';
        $searchableDescription = 'test_searchable_description';
        $dictionaryType = DictionaryType::from('ICD_DEATHS');

        // when
        $this->facade->addDictionaryEntry($value, $description, $searchableDescription, $dictionaryType);

        // then
        $exists = $this->facade->dictionaryValueExists($value, $dictionaryType->value);
        self::assertTrue($exists);

        $gridFields = new ArrayCollection();
        $gridFields->add(GridField::create('value', true, true, GridFilterMethod::EQUALS));

        $gridConfiguration = GridConfiguration::create($gridFields, 1, 10, [], ['value' => $value]);
        $dictionaryType = DictionaryType::from('ICD_DEATHS');

        $gridResult = $this->facade->findDictionaryEntriesForSelect($gridConfiguration, $dictionaryType->value);

        self::assertInstanceOf(GridResult::class, $gridResult);
        self::assertCount(1, $gridResult->data());
    }

    public function testEditDictionaryEntry(): void
    {
        // given
        $value = 'test_value'.random_int(0, 999999);
        $description = 'test_description';
        $searchableDescription = 'test_searchable_description';
        $dictionaryType = DictionaryType::from('ICD_DEATHS');

        $this->facade->addDictionaryEntry($value, $description, $searchableDescription, $dictionaryType);
        $lastInsertedId = $this->facade->getLastInsertedId();

        $newDescription = 'new_description';
        $newSearchableDescription = 'new_searchable_description';

        // when
        $this->facade->editDictionaryEntry($lastInsertedId, $newDescription, $newSearchableDescription);

        // then
        $dictionaryEntry = $this->facade->getDictionaryEntryById($lastInsertedId);
        self::assertNotNull($dictionaryEntry);
        self::assertEquals($newDescription, $dictionaryEntry->description());
        self::assertEquals($newSearchableDescription, $dictionaryEntry->searchableDescription());
    }

    public function testAddExistingDictionaryEntry(): void
    {
        // given
        $value = 'test_value'.random_int(0, 999999);
        $description = 'test_description';
        $searchableDescription = 'test_searchable_description';
        $dictionaryType = DictionaryType::from('ICD_DEATHS');

        $this->facade->addDictionaryEntry($value, $description, $searchableDescription, $dictionaryType);

        // when & then
        $this->expectException(\InvalidArgumentException::class);
        $this->facade->addDictionaryEntry($value, $description, $searchableDescription, $dictionaryType);
    }
}
