<?php

declare(strict_types=1);

namespace App\Tests\DataAnalytics\Http;

use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class DataAnalyticsAPITest extends WebTestCase
{
    private KernelBrowser $client;

    protected function setUp(): void
    {
        $this->client = static::createClient();
    }

    public function testGetAllAccounts(): void
    {
        $responseData = $this->getContent('/api/v1/ext/analytic/accounts');

        self::assertNotNull($responseData, 'JSON decoding failed.');
        self::assertIsArray($responseData, 'Response data is not an array.');
        self::assertNotEmpty($responseData, 'Response data is empty.');
    }

    public function testGetAllHospitals(): void
    {
        $responseData = $this->getContent('/api/v1/ext/analytic/hospitals');

        self::assertNotNull($responseData, 'JSON decoding failed.');
        self::assertIsArray($responseData, 'Response data is not an array.');
        self::assertNotEmpty($responseData, 'Response data is empty.');
    }

    public function testGetAllPatients(): void
    {
        $responseData = $this->getContent('/api/v1/ext/analytic/patients');

        self::assertNotNull($responseData, 'JSON decoding failed.');
        self::assertIsArray($responseData, 'Response data is not an array.');
        self::assertNotEmpty($responseData, 'Response data is empty.');
    }

    public function testGetAllForms(): void
    {
        $responseData = $this->getContent('/api/v1/ext/analytic/forms');
    }

    public function testGetAllDictionaries(): void
    {
        $responseData = $this->getContent('/api/v1/ext/analytic/dictionaries');

        self::assertNotNull($responseData, 'JSON decoding failed.');
        self::assertIsArray($responseData, 'Response data is not an array.');
        self::assertNotEmpty($responseData, 'Response data is empty.');
    }

    public function testEndpointsWithInvalidKey(): void
    {
        $invalidApiKey = 'invalid_key';

        $endpoints = [
            '/api/v1/ext/analytic/accounts',
            '/api/v1/ext/analytic/hospitals',
            '/api/v1/ext/analytic/patients',
            '/api/v1/ext/analytic/forms',
            '/api/v1/ext/analytic/dictionaries',
        ];

        foreach ($endpoints as $endpoint) {
            $this->client->request(
                'GET',
                $endpoint,
                [],
                [],
                [
                    'HTTP_Authorization' => $invalidApiKey,
                    'HTTP_Content-Type' => 'application/json',
                ]
            );

            $response = $this->client->getResponse();

            self::assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode(), "Endpoint $endpoint did not return 401 Unauthorized with invalid key.");
        }
    }

    private function getContent(string $url): mixed
    {
        $apiKey = $_ENV['API_KEY_EXTERNAL_ANALYTICS'];

        $this->client->request(
            'GET',
            $url,
            [],
            [],
            [
                'HTTP_Authorization' => $apiKey,
                'HTTP_Content-Type' => 'application/json',
            ]
        );

        $response = $this->client->getResponse();

        self::assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $content = $response->getContent();

        // Decode the JSON response
        return json_decode((string) $content, true);
    }
}
