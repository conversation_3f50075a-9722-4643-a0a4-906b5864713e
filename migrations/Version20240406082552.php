<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240406082552 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE account_account (firstName VARCHAR(50) NOT NULL, lastName VARCHAR(50) NOT NULL, mobilePhoneNumber VARCHAR(20) NOT NULL, role VARCHAR(255) NOT NULL, isActive TINYINT(1) NOT NULL, deactivationReason VARCHAR(1000) DEFAULT NULL, createdAt DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', updatedAt DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', version INT DEFAULT 1 NOT NULL, protocols JSON NOT NULL COMMENT \'(DC2Type:json)\', hospitals JSON NOT NULL COMMENT \'(DC2Type:json)\', accountId CHAR(36) NOT NULL COMMENT \'(DC2Type:uuid_symfony)\', email VARCHAR(100) NOT NULL, INDEX IDX_B41D42EC62DEB3E8 (accountId), UNIQUE INDEX UNIQ_B41D42ECE7927C74 (email), UNIQUE INDEX UNIQ_B41D42ECF6D1AE65 (mobilePhoneNumber), PRIMARY KEY(accountId)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE auth_refresh_token (id INT AUTO_INCREMENT NOT NULL, refresh_token VARCHAR(128) NOT NULL, username VARCHAR(255) NOT NULL, valid DATETIME NOT NULL, UNIQUE INDEX UNIQ_C0DCFD85C74F2195 (refresh_token), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE auth_reset_password_request (id INT AUTO_INCREMENT NOT NULL, verifier VARCHAR(20) NOT NULL, requestedAt DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', expiresAt DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', authUser CHAR(36) NOT NULL COMMENT \'(DC2Type:uuid_symfony)\', INDEX IDX_399909B1F22755F0 (authUser), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE auth_user (role VARCHAR(255) NOT NULL, isActive TINYINT(1) NOT NULL, verificationAt DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', loginAt DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', logoutAt DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', createdAt DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', updatedAt DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', authUserId CHAR(36) NOT NULL COMMENT \'(DC2Type:uuid_symfony)\', email VARCHAR(100) NOT NULL, password VARCHAR(60) NOT NULL, UNIQUE INDEX UNIQ_A3B536FD2500F513 (authUserId), UNIQUE INDEX UNIQ_A3B536FDE7927C74 (email), PRIMARY KEY(authUserId)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE dictionary_dictionary_entry (dictionaryEntryId INT AUTO_INCREMENT NOT NULL, value VARCHAR(50) NOT NULL, description VARCHAR(500) NOT NULL, searchableDescription VARCHAR(500) NOT NULL, dictionaryType VARCHAR(50) NOT NULL, INDEX IDX_31E29BC3E97356D2593ABA2 (searchableDescription, dictionaryType), UNIQUE INDEX UNIQ_31E29BC1D7758342593ABA2 (value, dictionaryType), PRIMARY KEY(dictionaryEntryId)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE form_protocol_form_data (protocolFormDataId CHAR(36) NOT NULL COMMENT \'(DC2Type:uuid_symfony)\', patientProtocolId CHAR(36) NOT NULL COMMENT \'(DC2Type:uuid_symfony)\', formId VARCHAR(255) NOT NULL, patientId CHAR(36) NOT NULL COMMENT \'(DC2Type:uuid_symfony)\', protocol VARCHAR(50) NOT NULL, data LONGTEXT NOT NULL, createdAt DATETIME NOT NULL, updatedAt DATETIME DEFAULT NULL, INDEX IDX_F250FD6E9E50CC118F803478C8C0BC4C (formId, patientId, protocol), UNIQUE INDEX UNIQ_F250FD6E10EC046E9E50CC11 (patientProtocolId, formId), PRIMARY KEY(protocolFormDataId)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE hospital_hospital (shortName VARCHAR(20) NOT NULL, fullName VARCHAR(255) NOT NULL, streetWithNumber VARCHAR(100) NOT NULL, postCode VARCHAR(6) NOT NULL, city VARCHAR(40) NOT NULL, isActive TINYINT(1) NOT NULL, deactivationReason VARCHAR(1000) DEFAULT NULL, createdAt DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', updatedAt DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', version INT DEFAULT 1 NOT NULL, hospitalId CHAR(36) NOT NULL COMMENT \'(DC2Type:uuid_symfony)\', UNIQUE INDEX UNIQ_681F5EAEC43A885D (shortName), PRIMARY KEY(hospitalId)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE logger_log (id INT AUTO_INCREMENT NOT NULL, correlationId VARCHAR(36) NOT NULL, logType VARCHAR(255) NOT NULL, action VARCHAR(255) NOT NULL, path VARCHAR(500) NOT NULL, objectId VARCHAR(40) DEFAULT NULL, objectDetails VARCHAR(5000) DEFAULT NULL, payload LONGTEXT NOT NULL, authUserId VARCHAR(255) DEFAULT NULL, ip VARCHAR(45) DEFAULT NULL, referer VARCHAR(2048) DEFAULT NULL, userAgent VARCHAR(255) DEFAULT NULL, createdAt DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', INDEX IDX_78F9A51B548B0F (path), INDEX IDX_78F9A51E62C836 (logType), INDEX IDX_78F9A5199722DDF (objectId), INDEX IDX_78F9A51A287595 (objectDetails), INDEX IDX_78F9A5186CA693C (createdAt), INDEX IDX_78F9A512500F513 (authUserId), INDEX IDX_78F9A5140F1812BB548B0F (correlationId, path), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE patient_patient (firstName VARCHAR(50) NOT NULL, lastName VARCHAR(50) NOT NULL, citizenship VARCHAR(255) NOT NULL, isRegisteredAddressSameAsResidence TINYINT(1) NOT NULL, isCorrespondenceAddressSameAsResidence TINYINT(1) NOT NULL, email VARCHAR(100) DEFAULT NULL, contactNumber VARCHAR(20) DEFAULT NULL, isPatientIdentified TINYINT(1) NOT NULL, gender VARCHAR(255) NOT NULL, isActive TINYINT(1) NOT NULL, deactivationReason VARCHAR(1000) DEFAULT NULL, isDeceased TINYINT(1) NOT NULL, createdAt DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', updatedAt DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', hospitals JSON NOT NULL COMMENT \'(DC2Type:json)\', patientId CHAR(36) NOT NULL COMMENT \'(DC2Type:uuid_symfony)\', patientPublicId VARCHAR(8) NOT NULL, residenceAddress_streetWithNumber VARCHAR(100) DEFAULT NULL, residenceAddress_city VARCHAR(40) DEFAULT NULL, residenceAddress_postCode VARCHAR(6) DEFAULT NULL, registeredAddress_streetWithNumber VARCHAR(100) DEFAULT NULL, registeredAddress_city VARCHAR(40) DEFAULT NULL, registeredAddress_postCode VARCHAR(6) DEFAULT NULL, correspondenceAddress_streetWithNumber VARCHAR(100) DEFAULT NULL, correspondenceAddress_city VARCHAR(40) DEFAULT NULL, correspondenceAddress_postCode VARCHAR(6) DEFAULT NULL, pesel_pesel VARCHAR(11) DEFAULT NULL, passportNumber_passportNumber VARCHAR(20) DEFAULT NULL, legalRepresentativePesel_pesel VARCHAR(11) DEFAULT NULL, legalRepresentativePassportNumber_passportNumber VARCHAR(20) DEFAULT NULL, birthDate DATE NOT NULL COMMENT \'(DC2Type:date_immutable)\', UNIQUE INDEX UNIQ_47B98200D95ACF55 (patientPublicId), UNIQUE INDEX UNIQ_47B98200BD936231 (pesel_pesel), UNIQUE INDEX UNIQ_47B98200753C100C (passportNumber_passportNumber), PRIMARY KEY(patientId)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE patient_patient_protocol (protocol VARCHAR(70) NOT NULL, isActive TINYINT(1) NOT NULL, treatmentStartDate DATE DEFAULT NULL COMMENT \'(DC2Type:date_immutable)\', icd10Code VARCHAR(50) DEFAULT NULL, patientProtocolId CHAR(36) NOT NULL COMMENT \'(DC2Type:uuid_symfony)\', patientId CHAR(36) NOT NULL COMMENT \'(DC2Type:uuid_symfony)\', INDEX IDX_50AE4B2C8F803478 (patientId), PRIMARY KEY(patientProtocolId)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE patient_patient_rodo_consent (fileName VARCHAR(255) NOT NULL, extension VARCHAR(4) NOT NULL, createdAt DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', patientRodoConsentId CHAR(36) NOT NULL COMMENT \'(DC2Type:uuid_symfony)\', patientId CHAR(36) NOT NULL COMMENT \'(DC2Type:uuid_symfony)\', INDEX IDX_D721A8888F803478 (patientId), PRIMARY KEY(patientRodoConsentId)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE messenger_messages (id BIGINT AUTO_INCREMENT NOT NULL, body LONGTEXT NOT NULL, headers LONGTEXT NOT NULL, queue_name VARCHAR(190) NOT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', available_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', delivered_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', INDEX IDX_75EA56E0FB7336F0 (queue_name), INDEX IDX_75EA56E0E3BD61CE (available_at), INDEX IDX_75EA56E016BA31DB (delivered_at), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE auth_reset_password_request ADD CONSTRAINT FK_399909B1F22755F0 FOREIGN KEY (authUser) REFERENCES auth_user (authUserId)');
        $this->addSql('ALTER TABLE patient_patient_protocol ADD CONSTRAINT FK_50AE4B2C8F803478 FOREIGN KEY (patientId) REFERENCES patient_patient (patientId)');
        $this->addSql('ALTER TABLE patient_patient_rodo_consent ADD CONSTRAINT FK_D721A8888F803478 FOREIGN KEY (patientId) REFERENCES patient_patient (patientId)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE auth_reset_password_request DROP FOREIGN KEY FK_399909B1F22755F0');
        $this->addSql('ALTER TABLE patient_patient_protocol DROP FOREIGN KEY FK_50AE4B2C8F803478');
        $this->addSql('ALTER TABLE patient_patient_rodo_consent DROP FOREIGN KEY FK_D721A8888F803478');
        $this->addSql('DROP TABLE account_account');
        $this->addSql('DROP TABLE auth_refresh_token');
        $this->addSql('DROP TABLE auth_reset_password_request');
        $this->addSql('DROP TABLE auth_user');
        $this->addSql('DROP TABLE dictionary_dictionary_entry');
        $this->addSql('DROP TABLE form_protocol_form_data');
        $this->addSql('DROP TABLE hospital_hospital');
        $this->addSql('DROP TABLE logger_log');
        $this->addSql('DROP TABLE patient_patient');
        $this->addSql('DROP TABLE patient_patient_protocol');
        $this->addSql('DROP TABLE patient_patient_rodo_consent');
        $this->addSql('DROP TABLE messenger_messages');
    }
}
