<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250405120718 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE patient_patient_attachment (type VARCHAR(255) NOT NULL, fileName VARCHAR(255) NOT NULL, extension VARCHAR(10) NOT NULL, createdAt DATETIME NOT NULL COMMENT '(DC2Type:datetime_immutable)', patientAttachmentId CHAR(36) NOT NULL COMMENT '(DC2Type:uuid_symfony)', patientId CHAR(36) NOT NULL COMMENT '(DC2Type:uuid_symfony)', INDEX IDX_3F7B4DCD8F803478 (patientId), PRIMARY KEY(patientAttachmentId)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE patient_patient_attachment ADD CONSTRAINT FK_3F7B4DCD8F803478 FOREIGN KEY (patientId) REFERENCES patient_patient (patientId)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE patient_patient_attachment DROP FOREIGN KEY FK_3F7B4DCD8F803478
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE patient_patient_attachment
        SQL);
    }
}
