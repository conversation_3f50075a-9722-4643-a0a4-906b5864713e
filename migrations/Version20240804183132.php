<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240804183132 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE patient_favorite_patients (id INT AUTO_INCREMENT NOT NULL, authUserId CHAR(36) NOT NULL COMMENT \'(DC2Type:uuid_symfony)\', patientId CHAR(36) NOT NULL COMMENT \'(DC2Type:uuid_symfony)\', createdAt DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', INDEX idx_createdAt (createdAt), UNIQUE INDEX unique_auth_user_patient (authUserId, patientId), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('DROP INDEX IDX_78F9A51A287595 ON logger_log');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP TABLE patient_favorite_patients');
        $this->addSql('DROP INDEX IDX_78F9A51A287595 ON logger_log');
        $this->addSql('CREATE INDEX IDX_78F9A51A287595 ON logger_log (objectDetails(768))');
    }
}
