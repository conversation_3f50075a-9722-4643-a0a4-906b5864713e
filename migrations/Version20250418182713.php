<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250418182713 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE patient_correction_flags (id CHAR(36) NOT NULL COMMENT '(DC2Type:uuid_symfony)', patientId CHAR(36) NOT NULL COMMENT '(DC2Type:uuid_symfony)', hospitalId CHAR(36) NOT NULL COMMENT '(DC2Type:uuid_symfony)', comment VARCHAR(1000) NOT NULL, createdAt DATETIME NOT NULL COMMENT '(DC2Type:datetime_immutable)', resolvedById CHAR(36) DEFAULT NULL COMMENT '(DC2Type:uuid_symfony)', resolvedAt DATETIME DEFAULT NULL COMMENT '(DC2Type:datetime_immutable)', version INT DEFAULT 1 NOT NULL, INDEX IDX_DE95AA8F803478 (patientId), INDEX IDX_DE95AAE64DCF6D (hospitalId), INDEX IDX_DE95AAAC6B26A5 (resolvedAt), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP TABLE patient_correction_flags
        SQL);
    }
}
