parameters:
    level: 8
    featureToggles:
    ignoreErrors:
    paths:
        - 'src'
        - 'tests'
        - 'fixtures'
    symfony:
        container_xml_path: '%rootDir%/../../../var/cache/test/App_KernelTestDebugContainer.xml'
        console_application_loader: config/phpstan/console-loader.php
    doctrine:
            objectManagerLoader: config/phpstan/doctrine-orm-bootstrap.php
    bootstrapFiles:
            - config/bootstrap.php
