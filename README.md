# 🏥 HOPE REGISTRY BACKEND

<div align="center">

![Version](https://img.shields.io/badge/version-0.3.2-blue.svg)
![PHP](https://img.shields.io/badge/PHP-8.3+-777BB4.svg?logo=php)
![Symfony](https://img.shields.io/badge/Symfony-6.4-000000.svg?logo=symfony)
![License](https://img.shields.io/badge/license-Proprietary-red.svg)

**Zaawansowany system zarządzania rejestrem pacjentów onkologicznych**

*Wykorzystujący architekturę Domain-Driven Design (DDD) z Symfony 6.4*

</div>

## 📋 Spis treści

- [Wprowadzenie](#-wprowadzenie)
- [Wymagania systemowe](#-wymagania-systemowe)
- [Szybki start](#-szybki-start)
- [Konfiguracja środowiska](#-konfiguracja-środowiska)
- [Architektura systemu](#-architektura-systemu)
- [Moduły systemu](#-moduły-systemu)
- [API Documentation](#-api-documentation)
- [Protokoły medyczne](#-protokoły-medyczne)
- [Testowanie](#-testowanie)
- [Narzędzia deweloperskie](#-narzędzia-deweloperskie)
- [Deployment](#-deployment)
- [Troubleshooting](#-troubleshooting)
- [Contributing](#-contributing)

## 🎯 Wprowadzenie

Hope Registry Backend to nowoczesny system zarządzania rejestrem pacjentów onkologicznych, zbudowany w oparciu o najlepsze praktyki Domain-Driven Design (DDD). System umożliwia:

- 👥 **Zarządzanie kontami użytkowników** - wielopoziomowy system uprawnień
- 🏥 **Zarządzanie szpitalami** - rejestr placówek medycznych
- 🧑‍⚕️ **Zarządzanie danymi pacjentów** - kompletne profile medyczne
- 📋 **Protokoły medyczne** - wsparcie dla 20+ typów nowotworów
- 🔐 **Bezpieczeństwo** - JWT authentication, RODO compliance
- 📊 **Analityka danych** - zaawansowane raporty i statystyki

### Kluczowe funkcjonalności

- **Architektura DDD** z wyraźnie oddzielonymi domenami biznesowymi
- **RESTful API**
- **Protokoły medyczne** dla różnych typów nowotworów (ALCL, ALL, AML, BT, CML, EGCT, HL, LBL, NBL, etc.)
- **System uprawnień** z role-based access control
- **RODO compliance** z zarządzaniem zgodami pacjentów
- **Monitoring błędów** z integracją Sentry
- **Testowanie** - unit, integration i HTTP tests

## 💻 Wymagania systemowe

### Minimalne wymagania

- **Docker** >= 20.10
- **Docker Compose** >= 2.0
- **Git** >= 2.30
- **1GB RAM** (zalecane 8GB)
- **2GB miejsca na dysku**

### Opcjonalne (dla developerów)

- **PHP** >= 8.3 (jeśli chcesz uruchamiać bez Docker)
- **Composer** >= 2.5
- **Symfony CLI** >= 5.4

### Wspierane środowiska

- ✅ **Linux** (Ubuntu 20.04+, CentOS 8+)
- ✅ **macOS** (10.15+)
- ✅ **Windows** (Windows 10+ z WSL2)

## 🚀 Szybki start

### 1. Klonowanie repozytorium

```bash
git clone [URL_REPOZYTORIUM] hope_registry_backend
cd hope_registry_backend
```

### 2. Konfiguracja środowiska

```bash
# Skopiuj plik konfiguracyjny
cp .env .env.local

# Edytuj plik .env.local i ustaw swoje dane
nano .env.local
```

### 3. Uruchomienie Docker

```bash
# Uruchom wszystkie kontenery
sh bin/docker-start.sh
```

### 4. Instalacja dependencies i konfiguracja bazy danych

```bash
# Zaloguj się do kontenera PHP
sh bin/run-zsh.sh

# Zainstaluj dependencies PHP
composer install

# Skonfiguruj bazę danych
sh bin/rebuild-db.sh
```

### 5. Weryfikacja instalacji

Aplikacja powinna być dostępna pod adresem: **http://localhost:8080**

MailCatcher (dev SMTP): **http://localhost:1080**

## ⚙️ Konfiguracja środowiska

### Plik .env.local

Utwórz plik `.env.local` na podstawie `.env` i skonfiguruj następujące zmienne:

```bash
# Database Configuration
DATABASE_URL="mysql://hope_registry:hope_registry@db:3306/hope_registry"

# Application URLs
APP_URL=http://localhost:8080
APP_FRONT_URL=http://localhost:3000

# JWT Configuration
JWT_SECRET_KEY=%kernel.project_dir%/config/jwt/private.pem
JWT_PUBLIC_KEY=%kernel.project_dir%/config/jwt/public.pem
JWT_PASSPHRASE=your_jwt_passphrase

# Mailer Configuration
MAILER_DSN=smtp://mailer:1025
MAILER_FROM_NAME="Hope Registry"
MAILER_FROM_EMAIL=<EMAIL>

# Reset Password Settings
APP_RESET_PASSWORD_REQUEST_LIFETIME=3600
APP_RESET_PASSWORD_THROTTLE_LIMIT=1

# Sentry (Production)
SENTRY_DSN=your_sentry_dsn_here

# CORS Settings
CORS_ALLOW_ORIGIN='^https?://(localhost|127\.0\.0\.1)(:[0-9]+)?$'
```

### Konfiguracja JWT

Wygeneruj klucze JWT:

```bash
# Wejdź do kontenera
sh bin/run-zsh.sh

# Wygeneruj klucze
mkdir -p config/jwt
openssl genpkey -out config/jwt/private.pem -aes256 -algorithm rsa -pkeyopt rsa_keygen_bits:4096
openssl pkey -in config/jwt/private.pem -out config/jwt/public.pem -pubout
```

### Docker Services

System składa się z następujących kontenerów:

| Serwis | Port | Opis |
|--------|------|------|
| **web** (nginx) | 8080 | Web server |
| **php** | - | PHP-FPM 8.3 |
| **db** (MariaDB) | 3306 | Baza danych |
| **mailer** | 1025/1080 | MailCatcher SMTP |

## 🏗️ Architektura systemu

Hope Registry Backend wykorzystuje architekturę **Domain-Driven Design (DDD)** z wyraźnym podziałem na warstwy:

### Warstwy architektury

```
┌─────────────────────────────┐
│    BackendForFrontend       │ ← API Layer (Controllers, DTOs)
├─────────────────────────────┤
│    Application Layer        │ ← Use Cases, Services, Facades  
├─────────────────────────────┤
│    Domain Layer             │ ← Entities, Value Objects, Rules
├─────────────────────────────┤
│    Infrastructure Layer     │ ← Repositories, External Services
└─────────────────────────────┘
```

### Diagramy C4

Poniżej znajdują się diagramy architektury C4 dla systemu Hope Registry. Pliki źródłowe PlantUML znajdują się w katalogu `docs/`.

#### C1: Kontekst Systemu

Diagram przedstawia ogólny kontekst systemu, jego użytkowników oraz interakcje z systemami zewnętrznymi.

![Diagram Kontekstu Systemu](docs/img/c1_system_context.png)

#### C2: Diagram Kontenerów

Diagram pokazuje główne kontenery (aplikacje, bazy danych) wchodzące w skład systemu.

![Diagram Kontenerów](docs/img/c2_container_diagram.png)

#### C3: Diagram Komponentów Backendu

Diagram szczegółowo opisuje wewnętrzne komponenty i moduły aplikacji backendowej.

![Diagram Komponentów Backendu](docs/img/c3_backend_components.png)

## 🧩 Moduły systemu

### 👤 Account Module

**Ścieżka:** `src/Account/`

Zarządzanie kontami użytkowników systemu.

**Kluczowe komponenty:**
- `Account` - encja użytkownika z profilem
- `AccountService` - logika biznesowa kont
- `AccountRepository` - persystencja danych

**Funkcjonalności:**
- Tworzenie i aktualizacja kont użytkowników
- Zarządzanie profilami użytkowników
- Integracja z modułem Hospital

### 🔐 Auth Module

**Ścieżka:** `src/Auth/`

System uwierzytelniania i autoryzacji.

**Kluczowe komponenty:**
- `AuthUser` - encja uwierzytelniania
- `Password` - value object hasła
- `RefreshToken` - tokeny odświeżania
- `ResetPasswordRequest` - żądania reset hasła

**Funkcjonalności:**
- JWT authentication
- Reset hasła z tokenami
- Refresh tokens
- External authentication strategies

### 🧑‍⚕️ Patient Module

**Ścieżka:** `src/Patient/`

Rdzeń systemu - zarządzanie danymi pacjentów.

**Kluczowe komponenty:**
- `Patient` - encja pacjenta
- `PatientProtocol` - protokoły medyczne
- `PatientAttachment` - załączniki
- `PatientCorrectionFlag` - flagi korekt

**Value Objects:**
- `Pesel`, `Gender`, `BirthDate`
- `Address`, `Citizenship`
- `PassportNumber`

**Funkcjonalności:**
- Kompleksowe zarządzanie danymi pacjentów
- System protokołów medycznych
- Zarządzanie załącznikami
- RODO consent management
- System flag korekt danych

### 🏥 Hospital Module

**Ścieżka:** `src/Hospital/`

Zarządzanie placówkami medycznymi.

**Kluczowe komponenty:**
- `Hospital` - encja szpitala
- `HospitalService` - logika biznesowa

**Funkcjonalności:**
- Rejestr szpitali i placówek
- Powiązania pacjent-szpital
- Zarządzanie uprawnieniami dostępu

### 📋 Form Module

**Ścieżka:** `src/Form/`

System protokołów medycznych i formularzy.

**Protokoły medyczne:**
- **ALCL** - Anaplastic Large Cell Lymphoma
- **ALL** - Acute Lymphoblastic Leukemia  
- **AML** - Acute Myeloid Leukemia
- **BT** - Brain Tumors
- **CML** - Chronic Myeloid Leukemia
- **EGCT** - Extracranial Germ Cell Tumors
- **HL** - Hodgkin Lymphoma
- **LBL** - Lymphoblastic Lymphoma
- **NBL** - Neuroblastoma
- **WT** - Wilms Tumor
- **+10 innych typów**

### 📖 Dictionary Module

**Ścieżka:** `src/Dictionary/`

Słowniki systemowe i dane referencyjne.

**Funkcjonalności:**
- Centralne zarządzanie słownikami
- Cache'owanie danych słownikowych
- API dla frontendów

### 🌐 BackendForFrontend

**Ścieżka:** `src/BackendForFrontend/`

Warstwa API z kontrolerami REST.

**Sekcje:**
- **Public/** - publiczne endpointy (auth, reset password)
- **Security/** - chronione endpointy (CRUD operations)
- **External/** - integracje zewnętrzne

## 📡 API Documentation

### Uwierzytelnianie

System używa **JWT Bearer tokens** do uwierzytelniania.

#### Logowanie

```http
POST /api/auth/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password123"
}
```

**Odpowiedź:**
```json
{
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...",
    "refresh_token": "def502004a8c1f..."
}
```

#### Refresh Token

```http
POST /api/auth/refresh
Content-Type: application/json

{
    "refresh_token": "def502004a8c1f..."
}
```

### Szczegółowe endpointy API

#### 🔓 Publiczne endpointy autoryzacji

**Reset hasła**
- `PUT /reset-password` - Żądanie resetowania hasła
- `PUT /reset-password/reset/?token={token}` - Weryfikacja tokenu resetowania
- `POST /reset-password/change-password` - Zmiana hasła z tokenem

```http
PUT /reset-password
Content-Type: application/json

{
    "email": "<EMAIL>"
}
```

#### 🔒 Endpointy autoryzacji

**Zarządzanie sesją**
- `POST /logout` - Wylogowanie użytkownika

```http
POST /logout
Authorization: Bearer {token}
Content-Type: application/json

{
    "refreshToken": "def502004a8c1f..."
}
```

#### 👥 Zarządzanie kontami

**Wymagane uprawnienia: `ROLE_CENTRAL_ADMINISTRATOR`**

- `POST /account/accounts` - Tworzenie nowego konta
- `PUT /account/accounts/{accountId}` - Aktualizacja konta
- `PATCH /account/accounts/{accountId}/change-email` - Zmiana adresu email
- `PATCH /account/accounts/{accountId}/activate` - Aktywacja konta
- `PATCH /account/accounts/{accountId}/deactivate` - Deaktywacja konta
- `GET /account/accounts/{accountId}` - Pobieranie szczegółów konta
- `GET /account/accounts` - Lista kont (grid)

**Dostępne dla wszystkich użytkowników:**
- `GET /account/accounts/me` - Moje konto

```http
POST /account/accounts
Authorization: Bearer {token}
Content-Type: application/json

{
    "firstName": "Jan",
    "lastName": "Kowalski", 
    "email": "<EMAIL>",
    "mobilePhoneNumber": "+***********",
    "role": "ROLE_REPORTER_PHYSICIAN",
    "protocols": ["ALL", "AML"],
    "hospitals": ["uuid-hospital-1", "uuid-hospital-2"]
}
```

#### 🏥 Zarządzanie szpitalami

**Wymagane uprawnienia: `ROLE_CENTRAL_ADMINISTRATOR`**

- `POST /hospital/hospitals` - Tworzenie szpitala
- `PUT /hospital/hospitals/{hospitalId}` - Aktualizacja szpitala
- `PATCH /hospital/hospitals/{hospitalId}/activate` - Aktywacja szpitala
- `PATCH /hospital/hospitals/{hospitalId}/deactivate` - Deaktywacja szpitala
- `GET /hospital/hospitals/{hospitalId}` - Pobieranie szczegółów szpitala
- `GET /hospital/hospitals` - Lista szpitali (grid)

**Dostępne dla wszystkich użytkowników:**
- `POST /hospital/hospitals/for-select` - Lista aktywnych szpitali do wyboru

```http
POST /hospital/hospitals
Authorization: Bearer {token}
Content-Type: application/json

{
    "shortName": "WSK",
    "fullName": "Wojewódzki Szpital Kliniczny im. Prof. Jonschera",
    "streetWithNumber": "ul. Długa 1/2",
    "postCode": "61-848",
    "city": "Poznań"
}
```

#### 👨‍⚕️ Zarządzanie pacjentami

**Podstawowe operacje CRUD**
- `POST /patient/patients` - Tworzenie pacjenta
- `GET /patient/patients/{patientId}` - Pobieranie szczegółów pacjenta
- `PUT /patient/patients/{patientId}` - Aktualizacja pacjenta
- `GET /patient/patients` - Lista pacjentów (grid)

**Aktywacja/Deaktywacja (ROLE_CENTRAL_ADMINISTRATOR)**
- `PATCH /patient/patients/{patientId}/activate` - Aktywacja pacjenta
- `PATCH /patient/patients/{patientId}/deactivate` - Deaktywacja pacjenta

**Zarządzanie szpitalami pacjenta (ROLE_CENTRAL_ADMINISTRATOR)**
- `POST /patient/patients/{patientId}/hospitals/{hospitalId}` - Dodanie szpitala
- `DELETE /patient/patients/{patientId}/hospitals/{hospitalId}` - Usunięcie szpitala

**Zarządzanie protokołami pacjenta**
- `POST /patient/patients/{patientId}/protocols` - Dodanie protokołu
- `PATCH /patient/patients/{patientId}/protocols/{patientProtocolId}/activate` - Aktywacja protokołu (ROLE_CENTRAL_ADMINISTRATOR)
- `PATCH /patient/patients/{patientId}/protocols/{patientProtocolId}/deactivate` - Deaktywacja protokołu (ROLE_CENTRAL_ADMINISTRATOR)

**Załączniki i dokumenty**
- `POST /patient/patients/{patientId}/attachments` - Dodanie załącznika
- `GET /patient/patients/{patientId}/attachments/{patientAttachmentId}` - Pobieranie załącznika
- `DELETE /patient/patients/{patientId}/attachments/{patientAttachmentId}` - Usunięcie załącznika (ROLE_CENTRAL_ADMINISTRATOR)

**Zgody RODO**
- `POST /patient/patients/{patientId}/rodo-consents` - Dodanie zgody RODO
- `GET /patient/patients/{patientId}/rodo-consents/{patientRodoConsentId}` - Pobieranie pliku zgody RODO

**Ulubieni pacjenci**
- `POST /patient/patients/{patientId}/favorite-patients` - Dodanie do ulubionych
- `DELETE /patient/patients/{patientId}/favorite-patients` - Usunięcie z ulubionych
- `GET /patient/patients/{patientId}/favorite-patients` - Sprawdzenie czy ulubiony

**Flagi korekt**
- `POST /patient/patients/{patientId}/correction-flags` - Utworzenie flagi korekty (ROLE_CENTRAL_ADMINISTRATOR)
- `PATCH /patient/patients/{patientId}/correction-flags/{flagId}/resolve` - Rozwiązanie flagi korekty

**Narzędzia PESEL**
- `POST /patient/pesel/details` - Pobieranie szczegółów z numeru PESEL

```http
POST /patient/patients
Authorization: Bearer {token}
Content-Type: application/json

{
    "firstName": "Anna",
    "lastName": "Nowak",
    "citizenship": "POLISH",
    "isRegisteredAddressSameAsResidence": true,
    "isCorrespondenceAddressSameAsResidence": true,
    "residenceAddress": {
        "streetWithNumber": "ul. Słoneczna 15",
        "postCode": "61-001",
        "city": "Poznań"
    },
    "email": "<EMAIL>",
    "contactNumber": "123456789",
    "isPatientIdentified": true,
    "pesel": "***********",
    "gender": "FEMALE",
    "birthDate": "1985-01-01",
    "hospitals": ["uuid-hospital-1"],
    "protocol": "ALL",
    "rodoConsent": {
        "fileName": "zgoda_rodo.pdf",
        "extension": "pdf",
        "fileContentBase64": "JVBERi0xLjQ..."
    }
}
```

#### 📋 Zarządzanie formularzami medycznymi

**Formularze protokołów**
- `GET /form/patient-forms/{patientId}` - Formularze pacjenta
- `GET /form/forms/{formId}/protocol-form-data/{patientProtocolId}` - Schema i dane formularza
- `POST /form/forms/{formId}/protocol-form-data/{patientProtocolId}` - Zapisanie danych formularza

```http
GET /form/patient-forms/{patientId}
Authorization: Bearer {token}

Response:
{
    "forms": [
        {
            "formId": "FOLLOWUP_FORM_1",
            "title": "Formularz obserwacji",
            "protocol": "ALL",
            "isCompleted": false
        }
    ]
}
```

#### 📚 Zarządzanie słownikami

**Administracja słowników (ROLE_CENTRAL_ADMINISTRATOR)**  
- `POST /dictionary/dictionaries/{dictionaryType}/entries` - Dodanie wpisu do słownika
- `PUT /dictionary/dictionaries/{dictionaryType}/entries/{dictionaryEntryId}` - Aktualizacja wpisu
- `GET /dictionary/dictionaries/dictionaryTypes` - Lista typów słowników
- `GET /dictionary/dictionaries/entries` - Wszystkie wpisy słowników (grid)

**Dostępne dla wszystkich użytkowników:**
- `GET /dictionary/dictionaries/{dictionaryType}/entries` - Wpisy słownika do wyboru

```http
POST /dictionary/dictionaries/DIAGNOSIS_TYPE/entries
Authorization: Bearer {token}
Content-Type: application/json

{
    "value": "NEW_DIAGNOSIS",
    "description": "Nowa diagnoza",
    "searchableDescription": "nowa diagnoza onkologiczna"
}
```

#### 📊 Dashboard i statystyki

**Dashboard**
- `GET /dashboard/stats` - Statystyki dashboardu
- `GET /dashboard/favorite-patients` - Ulubieni pacjenci (grid)  
- `GET /dashboard/correction-flags` - Flagi korekt (grid)

```http
GET /dashboard/stats?year=2024
Authorization: Bearer {token}

Response:
{
    "uniqueTreatmentStartYears": [2022, 2023, 2024],
    "countOfPatientsByHospitals": {...},
    "countOfPatientsByGender": {...},
    "countOfPatientsByProtocols": {...},
    "countOfVisiblePatientsForAccount": 150,
    "countOfPatientsAddedInLast30DaysByAccount": 12,
    "countOfUpdatedFormsInLast30DaysByAccount": 45
}
```

#### 🔍 Logi systemowe

**Wymagane uprawnienia: `ROLE_CENTRAL_ADMINISTRATOR`**
- `GET /logger/logs` - Pobieranie logów systemowych (grid)

#### 📈 API analityczne (zewnętrzne)

**Wymagane uprawnienia: `ROLE_EXTERNAL_API_ANALYTICS`**

- `GET /analytic/accounts` - Wszystkie konta
- `GET /analytic/hospitals` - Wszystkie szpitale  
- `GET /analytic/patients` - Wszyscy pacjenci
- `GET /analytic/forms` - Wszystkie formularze (streamowane)
- `GET /analytic/correction-flags` - Wszystkie flagi korekt
- `GET /analytic/protocols` - Dostępne protokoły
- `GET /analytic/dictionaries` - Wszystkie słowniki

**Parametry wspólne:** `?changedSince=2024-01-01T00:00:00Z`

```http
GET /analytic/patients?changedSince=2024-01-01T00:00:00Z
Authorization: Bearer {token}

Response:
[
    {
        "patientId": "uuid-123",
        "firstName": "Jan",
        "lastName": "Kowalski",
        "hospitals": ["uuid-hospital-1"],
        "protocols": ["ALL"],
        "updatedAt": "2024-01-15T10:30:00Z"
    }
]
```

### System uprawnień i ról

System wykorzystuje cztery główne role użytkowników z różnymi poziomami dostępu:

| Rola | Opis | Uprawnienia |
|------|------|-------------|
| `ROLE_CENTRAL_ADMINISTRATOR` | Administrator centralny | Pełny dostęp do wszystkich funkcji systemu |
| `ROLE_REPORTER_PHYSICIAN` | Lekarz raportujący | Dostęp do pacjentów w swoich szpitalach i protokołach |
| `ROLE_COORDINATOR` | Koordynator | Dostęp do pacjentów w swoich protokołach |
| `ROLE_DATA_ADMINISTRATOR` | Administrator danych | Dostęp do pacjentów w swoich szpitalach |

### Voters (kontrola dostępu)

System używa Symfony Voters do precyzyjnej kontroli dostępu:

#### PatientVoter
Kontroluje dostęp do operacji na pacjentach na podstawie:
- Przypisanych szpitali użytkownika
- Przypisanych protokołów użytkownika  
- Roli użytkownika

**Operacje:**
- `CREATE` - tworzenie pacjenta
- `UPDATE` - aktualizacja pacjenta
- `SHOW` - wyświetlanie pacjenta
- `ADD_PATIENT_PROTOCOL` - dodawanie protokołu
- `ADD_PATIENT_RODO_CONSENT` - dodawanie zgody RODO
- `DOWNLOAD_PATIENT_RODO_CONSENT` - pobieranie zgody RODO
- `ADD_ATTACHMENT` - dodawanie załącznika
- `DOWNLOAD_ATTACHMENT` - pobieranie załącznika
- `DELETE_ATTACHMENT` - usuwanie załącznika
- `RESOLVE_CORRECTION_FLAG` - rozwiązywanie flag korekt

#### FormVoter
Kontroluje dostęp do formularzy medycznych:
- `GET_PATIENT_FORMS` - pobieranie formularzy pacjenta
- `GET_FORM_SCHEMA_AND_DATA` - pobieranie schematu i danych
- `SAVE_FORM_DATA` - zapisywanie danych formularza

### Walidacja danych (DTO)

System używa Data Transfer Objects z walidacją Symfony:

**Przykłady walidacji:**
- Email: `#[Assert\Email()]`
- PESEL: `#[Assert\Regex(pattern: "/^\d+$/")]` + długość 11 znaków
- Telefon: `#[Assert\Regex(pattern: '/^\+[0-9]{2}[0-9]{9}$/')]`
- Kod pocztowy: `#[Assert\Regex(pattern: "/^\d{2}-\d{3}$/")]`
- Base64File: `#[Base64File]` (custom validator)
- UUID: `#[Assert\Uuid()]`

### Nagłówki żądań

```http
Authorization: Bearer {your_jwt_token}
Content-Type: application/json
Accept: application/json
```

### Kody odpowiedzi

| Kod | Opis |
|-----|------|
| `200` | OK - żądanie wykonane pomyślnie |
| `201` | Created - zasób utworzony |
| `204` | No Content - operacja wykonana, brak treści |
| `400` | Bad Request - błędne dane wejściowe |
| `401` | Unauthorized - brak lub nieprawidłowa autoryzacja |
| `403` | Forbidden - brak uprawnień (Voter odrzucił dostęp) |
| `404` | Not Found - zasób nie istnieje |
| `422` | Unprocessable Entity - błąd walidacji DTO |
| `500` | Internal Server Error - błąd serwera |

### Przykłady błędów walidacji

```json
{
    "type": "validation_error",
    "title": "Validation Failed", 
    "violations": [
        {
            "propertyPath": "email",
            "message": "This value is not a valid email address."
        },
        {
            "propertyPath": "pesel", 
            "message": "Numer PESEL powinien składać się wyłącznie z cyfr."
        }
    ]
}
```

### Przykłady błędów uprawnień

```json
{
    "type": "access_denied",
    "title": "Access Denied",
    "detail": "Nie możesz pobrać tego pacjenta. Nie znajduje się on w zakresie Twoich uprawnień."
}
```

## 🧬 Protokoły medyczne

System obsługuje kompleksowe protokoły medyczne dla różnych typów nowotworów:

### Struktura protokołów

Protokoły są zdefiniowane jako JSON Schema w katalogu `src/Form/Protocols/Schemas/`:

```
src/Form/Protocols/Schemas/
├── ALCL/          # Anaplastic Large Cell Lymphoma
├── ALL/           # Acute Lymphoblastic Leukemia
├── AML/           # Acute Myeloid Leukemia
├── BT/            # Brain Tumors
├── CML/           # Chronic Myeloid Leukemia
├── EGCT/          # Extracranial Germ Cell Tumors
├── FOLLOWUP/      # Follow-up protocols
├── HBL/           # Hepatoblastoma
├── HL/            # Hodgkin Lymphoma
├── LBL/           # Lymphoblastic Lymphoma
├── LCH/           # Langerhans Cell Histiocytosis
├── MDS/           # Myelodysplastic Syndrome
├── MPAL/          # Mixed Phenotype Acute Leukemia
├── NBL/           # Neuroblastoma
├── NHLB/          # Non-Hodgkin B-cell Lymphoma
├── OUN/           # Central Nervous System Tumors
├── RBL/           # Retinoblastoma
├── STS/           # Soft Tissue Sarcoma
├── TEST/          # Test protocols
└── WT/            # Wilms Tumor
```

### Przykład użycia protokołu

```php
// Tworzenie nowego protokołu dla pacjenta
$patientProtocol = new PatientProtocol(
    $patient,
    ProtocolType::ALL,
    $protocolData,
    $createdBy
);

$patient->addProtocol($patientProtocol);
```

## 🧪 Testowanie

System zawiera kompleksowe testy na różnych poziomach.

### Struktura testów

```
tests/
├── Unit/              # Testy jednostkowe
├── Integration/       # Testy integracyjne
└── Http/             # Testy HTTP/API
```

### Uruchamianie testów

```bash
# Wszystkie testy
composer tests

# Tylko testy jednostkowe
composer tests:unit

# Tylko testy integracyjne  
composer tests:integration

# Tylko testy HTTP
composer tests:http
```

### Konfiguracja testowa

Testy używają dedykowanej bazy danych i środowiska `test`:

```bash
# Przygotowanie środowiska testowego
APP_ENV=test php bin/console doctrine:database:create --if-not-exists
APP_ENV=test php bin/console doctrine:schema:create
APP_ENV=test php bin/console doctrine:fixtures:load --no-interaction
```

### Coverage raport

```bash
# Generowanie raportu pokrycia kodu
php -d xdebug.mode=coverage vendor/bin/phpunit --coverage-html coverage/
```

## 🛠️ Narzędzia deweloperskie

### Komendy konsoli

#### Tworzenie kont użytkowników

```bash
# Interaktywne tworzenie nowego konta
php bin/console app:account:create
```

**Komenda przeprowadzi przez proces:**
1. Podanie imienia (max 50 znaków)
2. Podanie nazwiska (max 50 znaków)  
3. Podanie adresu email (walidacja email)
4. Podanie numeru telefonu (format: +***********)
5. Wybór roli z listy:
   - `ROLE_CENTRAL_ADMINISTRATOR`
   - `ROLE_REPORTER_PHYSICIAN`
   - `ROLE_COORDINATOR`
   - `ROLE_DATA_ADMINISTRATOR`
6. Potwierdzenie danych

**Przykład użycia:**
```bash
$ php bin/console app:account:create
======================================
Podaj imię: Jan
Podaj nazwisko: Kowalski
Podaj adres email: <EMAIL>
Podaj numer telefonu: +***********
Wybierz role:
  [0] ROLE_CENTRAL_ADMINISTRATOR
  [1] ROLE_REPORTER_PHYSICIAN
  [2] ROLE_COORDINATOR
  [3] ROLE_DATA_ADMINISTRATOR
 > 1

======================================
Podsumowanie:
Imię: Jan
Nazwisko: Kowalski
Email: <EMAIL>
Numer telefonu: +***********
Rola: ROLE_REPORTER_PHYSICIAN
======================================
Czy dodać użytkownika? 
[t] Tak
[n] Nie
 wybrano: t
```

### Zarządzanie bazą danych i fixtures

#### Przebudowa bazy danych

Skrypt `bin/rebuild-db.sh` wykonuje kompletną przebudowę bazy danych:

```bash
# Kompletna rekonstrukcja bazy danych z fixtures
sh bin/rebuild-db.sh
```

**Co robi skrypt:**
1. `symfony security:check` - sprawdza bezpieczeństwo dependencies
2. `doctrine:database:create --if-not-exists` - tworzy bazę danych jeśli nie istnieje
3. `doctrine:schema:drop --force --full-database` - **USUWA** całą strukturę bazy
4. `doctrine:schema:create` - tworzy nową strukturę z migracji
5. `messenger:setup-transports` - konfiguruje transport dla Messenger
6. `doctrine:fixtures:load --no-interaction` - ładuje dane testowe
7. Generuje dump schematu do `.cursor/rules/schemat_bazy_danych.sql`

#### Dostępne fixtures

System zawiera następujące fixtures z danymi testowymi:

| Fixture | Opis | Lokalizacja |
|---------|------|-------------|
| **AccountAndAuthFixtures** | Konta użytkowników i dane auth | `fixtures/AccountAndAuthFixtures.php` |
| **PatientFixtures** | Przykładowi pacjenci | `fixtures/PatientFixtures.php` |
| **HospitalFixtures** | Szpitale i placówki | `fixtures/HospitalFixtures.php` |
| **DictionaryEntryFixtures** | Słowniki systemowe | `fixtures/DictionaryEntryFixtures.php` |
| **ProtocolFormDataFixture** | Dane protokołów medycznych | `fixtures/ProtocolFormDataFixture.php` |

#### Indywidualne komendy bazy danych

```bash
# Tylko migracje (bez fixtures)
php bin/console doctrine:migrations:migrate

# Tylko fixtures (bez zmiany schematu)
php bin/console doctrine:fixtures:load --no-interaction

# Sprawdzenie statusu migracji
php bin/console doctrine:migrations:status

# Sprawdzenie schematu bazy
php bin/console doctrine:schema:validate

# Wygenerowanie nowej migracji
php bin/console doctrine:migrations:diff

# Rollback ostatniej migracji
php bin/console doctrine:migrations:migrate prev
```

#### Fixtures dla środowiska testowego

```bash
# Przygotowanie środowiska testowego z fixtures
APP_ENV=test php bin/console doctrine:database:create --if-not-exists
APP_ENV=test php bin/console doctrine:schema:create
APP_ENV=test php bin/console doctrine:fixtures:load --no-interaction
```

#### ⚠️ Uwaga bezpieczeństwa

- **Nigdy** nie uruchamiaj `bin/rebuild-db.sh` na produkcji
- Skrypt **całkowicie usuwa** wszystkie dane z bazy
- Zawsze wykonuj backup przed przebudową bazy

### Statyczna analiza kodu

```bash
# Kompletna analiza statyczna
composer static:analyze

# Tylko PHPStan
vendor/bin/phpstan analyze -c phpstan.neon

# Tylko PHP CS Fixer (dry run)
vendor/bin/php-cs-fixer fix --dry-run --diff
```

### Formatowanie kodu

```bash
# Automatyczne naprawianie stylu kodu
composer fix-cs
```

### Sprawdzanie bezpieczeństwa

```bash
# Sprawdzanie znanych luk bezpieczeństwa
symfony security:check
```

### Komendy Symfony

```bash
# Lista wszystkich komend
php bin/console list

# Czyszczenie cache
php bin/console cache:clear

# Informacje o routingu
php bin/console debug:router

# Debug kontenerów DI
php bin/console debug:container
```

### Uruchomienie SMTP MailCatcher

MailCatcher obsługuje bardzo prosty serwer SMTP, który przechwytuje każdą wysłaną do niego wiadomość w celu wyświetlenia jej w przeglądarce internetowej.

Uruchom program mailcatcher, ustaw swoją ulubioną aplikację tak, aby dostarczała pocztę na adres `smtp://127.0.0.1:1025` zamiast na domyślny serwer SMTP, a następnie sprawdź adres `http://127.0.0.1:1080`, aby zobaczyć pocztę, która dotarła do tej pory.

Konfiguracja znajduje się w docker-compose.yml

Wymaga SYMFONY CLI:

```bash
symfony open:local:webmail
```

### Debugging

```bash
# Debug queries
php bin/console doctrine:query:sql "SELECT * FROM patients LIMIT 5"

# Debug migrations
php bin/console doctrine:migrations:status

# Debug services
php bin/console debug:autowiring
```

## 🚀 Deployment

### Środowiska

- **Development** - lokalne środowisko z Docker
- **Test** - środowisko testowe (automatyczny deploy z brancha `test`)
- **Production** - środowisko produkcyjne (automatyczny deploy z brancha `master`)

### CI/CD Pipeline

Projekt wykorzystuje **GitHub Actions** dla automatyzacji procesu buildowania, testowania i wdrażania.

#### 📋 Build and Test Pipeline

**Plik:** `.github/workflows/build-and-test.yml`

**Wyzwalacze:** Pull Requests do branchy `master`, `test`, `develop`

**Środowisko testowe:**
- **OS:** Ubuntu 22.04
- **PHP:** 8.3
- **Extensions:** mbstring, xml, ctype, iconv, intl, pdo_mysql, json, fileinfo

**Kroki wykonywane:**
1. **Checkout kodu** z repozytorium
2. **Setup PHP 8.3** z wymaganymi extensionami
3. **Cache Composer** dependencies dla szybszych buildów
4. **Walidacja** `composer.json` i `composer.lock`
5. **Instalacja dependencies** z `composer install --optimize-autoloader`
6. **Sprawdzenie stylu kodu** z PHP CS Fixer (dry-run)
7. **Lint Symfony container** sprawdzenie konfiguracji
8. **Uruchomienie testów jednostkowych** `composer tests:unit`
9. **Statyczna analiza kodu** z PHPStan

#### 🚀 Production Deployment

**Plik:** `.github/workflows/manual-prod-deploy.yml`

**Wyzwalacz:** Manual dispatch z brancha `master`

**Opcje wersjonowania:**
- `major` - duże zmiany (1.0.0 → 2.0.0)
- `minor` - nowe funkcjonalności (1.0.0 → 1.1.0)  
- `patch` - bugfixy (1.0.0 → 1.0.1)

**Proces deployment:**
1. **Wszystkie kroki z Build and Test** (quality gates)
2. **Bumping wersji** w `package.json`
3. **Tworzenie Git tag** z nową wersją
4. **Push zmian** do repozytorium
5. **Pakowanie aplikacji** do ZIP (bez dev tools)
6. **Upload na serwer produkcyjny** via SCP
7. **Wykonanie skryptu deployment** `/home/<USER>/preper_new_backend.sh`

#### 🧪 Test Environment Deployment

**Plik:** `.github/workflows/manual-test-deploy.yml`

**Wyzwalacz:** Manual dispatch z brancha `test`

**Opcje wersjonowania (pre-release):**
- `premajor` - 1.0.0 → 2.0.0-0
- `preminor` - 1.0.0 → 1.1.0-0
- `prepatch` - 1.0.0 → 1.0.1-0

**Identyczny proces jak produkcja**, ale deploy na serwer testowy.

### Deployment Secrets

W GitHub repository skonfigurowane są następujące secrets:

| Secret | Opis |
|--------|------|
| `SSH_PROD_HOST` | Adres serwera produkcyjnego |
| `SSH_TEST_HOST` | Adres serwera testowego |
| `SSH_LOGIN` | Login SSH dla deployments |
| `SSH_KEY` | Klucz prywatny SSH |
| `SSH_PORT` | Port SSH |
| `SSH_DEPLOY_PATH` | Ścieżka docelowa na serwerze |

### Uruchamianie deployments

#### Deployment na środowisko testowe

1. Przejdź do **Actions** w GitHub repository
2. Wybierz workflow **"TEST - Manual Deploy instance 🚀"**
3. Kliknij **"Run workflow"**
4. Wybierz branch **`test`**
5. Wybierz typ wersji: `premajor`, `preminor`, lub `prepatch`
6. Kliknij **"Run workflow"**

#### Deployment na produkcję

1. Przejdź do **Actions** w GitHub repository  
2. Wybierz workflow **"PRODUCTION - Manual Deploy instance 🚀"**
3. Kliknij **"Run workflow"**
4. Wybierz branch **`master`**
5. Wybierz typ wersji: `major`, `minor`, lub `patch`
6. Kliknij **"Run workflow"**

#### ⚠️ Quality Gates

Każdy deployment **MUSI przejść** przez quality gates:
- ✅ Walidacja Composer
- ✅ PHP CS Fixer check (PSR-12)
- ✅ Symfony container lint
- ✅ Testy jednostkowe
- ✅ Statyczna analiza PHPStan

**Deployment zostanie zatrzymany** jeśli którykolwiek krok się nie powiedzie.

#### 📦 Artifacts

Po udanym deployment tworzony jest:
- **Git tag** z numerem wersji
- **ZIP package** `hope_r_backend_{version}.zip` zawierający:
  - Kod źródłowy (bez dev tools)
  - Dependencies produkcyjne
  - Migracje i fixtures
  - Konfigurację

#### Monitoring deployment

```bash
# Sprawdzenie statusu w GitHub Actions
# Logi dostępne w czasie rzeczywistym w zakładce Actions

# Sprawdzenie wersji na serwerze (po SSH)
cat package.json | grep version

# Sprawdzenie logów aplikacji
tail -f var/log/prod.log
```

### Przygotowanie do wdrożenia

```bash
# 1. Optymalizacja autoloadera
composer install --no-dev --optimize-autoloader

# 2. Czyszczenie i rozgrzewanie cache
php bin/console cache:clear --env=prod
php bin/console cache:warmup --env=prod

# 3. Migracje bazy danych
php bin/console doctrine:migrations:migrate --no-interaction

# 4. Sprawdzenie konfiguracji
php bin/console about
```

### Zmienne środowiskowe produkcyjne

```bash
APP_ENV=prod
APP_DEBUG=false
APP_SECRET=your_production_secret
DATABASE_URL=mysql://user:pass@host:port/dbname
SENTRY_DSN=your_production_sentry_dsn
```

### Monitoring

System integruje się z **Sentry** do monitorowania błędów:

```bash
# Testowanie integracji Sentry
php bin/console sentry:test
```

## 🔧 Troubleshooting

### Częste problemy

#### 1. Błąd połączenia z bazą danych

```
SQLSTATE[HY000] [2002] Connection refused
```

**Rozwiązanie:**
```bash
# Sprawdź status kontenerów
docker-compose ps

# Restart serwisu bazy danych
docker-compose restart db
```

#### 2. Problemy z uprawnieniami plików

```
Permission denied
```

**Rozwiązanie:**
```bash
# Popraw uprawnienia
sudo chown -R $USER:$USER .
chmod -R 755 var/
```

#### 3. Błędy JWT

```
Unable to load key
```

**Rozwiązanie:**
```bash
# Sprawdź istnienie kluczy JWT
ls -la config/jwt/

# Wygeneruj ponownie klucze
sh bin/run-zsh.sh
openssl genpkey -out config/jwt/private.pem -aes256 -algorithm rsa -pkeyopt rsa_keygen_bits:4096
openssl pkey -in config/jwt/private.pem -out config/jwt/public.pem -pubout
```

#### 4. Problemy z kompozerem

```
Your requirements could not be resolved
```

**Rozwiązanie:**
```bash
# Wyczyść cache composera
composer clear-cache

# Aktualizuj dependencies
composer update
```

#### 5. Błędy migracji

```
Migration already executed
```

**Rozwiązanie:**
```bash
# Sprawdź status migracji
php bin/console doctrine:migrations:status

# Zresetuj migracje (UWAGA: tylko w dev!)
php bin/console doctrine:migrations:version --add --all --no-interaction
```

### Logi systemowe

```bash
# Logi aplikacji
tail -f var/log/dev.log

# Logi Dockera
docker-compose logs -f

# Logi konkretnego serwisu
docker-compose logs -f php
```

### Performance debugging

```bash
# Profiler Symfony (tylko dev)
php bin/console debug:router | grep _profiler

# Analyze slow queries
php bin/console doctrine:query:sql "SHOW PROCESSLIST"
```

## 🤝 Contributing

### Proces rozwoju

1. **Fork** repozytorium
2. Utwórz **feature branch** (`git checkout -b feature/amazing-feature`)
3. **Commit** zmian (`git commit -m 'Add amazing feature'`)
4. **Push** do brancha (`git push origin feature/amazing-feature`)
5. Otwórz **Pull Request**

### Standardy kodowania

- **PSR-12** - standard kodowania PHP
- **Domain-Driven Design** - architektura
- **SOLID principles** - zasady projektowania
- **100% test coverage** dla logiki biznesowej

### Pre-commit checklist

```bash
# 1. Testy
composer tests

# 2. Statyczna analiza
composer static:analyze

# 3. Formatowanie kodu
composer fix-cs

# 4. Security check
symfony security:check
```

### Git commit messages

Format: `type(scope): description`

Przykłady:
- `feat(patient): add RODO consent management`
- `fix(auth): resolve JWT token refresh issue`
- `docs(readme): update API documentation`
- `refactor(hospital): simplify repository pattern`

## 📄 Licencja

Ten projekt jest własnością **Fundacji Na Ratunek** i jest objęty licencją proprietary.

## 📞 Kontakt

- **Projekt:** Hope Registry Backend  
- **Organizacja:** Fundacja Na Ratunek
- **Wersja:** 0.3.2

---

<div align="center">
  <strong>Zbudowane z ❤️ dla onkologii dziecięcej</strong>
</div>
