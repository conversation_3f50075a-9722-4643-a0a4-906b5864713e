# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    app_url: '%env(APP_URL)%'
    app_front_url: '%env(APP_FRONT_URL)%'
    app_reset_password_request_lifetime: '%env(APP_RESET_PASSWORD_REQUEST_LIFETIME)%'
    app_reset_password_throttle_limit: '%env(APP_RESET_PASSWORD_THROTTLE_LIMIT)%'
    mailer_from_name: '%env(MAILER_FROM_NAME)%'
    mailer_from_email: '%env(MAILER_FROM_EMAIL)%'

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'
            - '../src/*/Tests/'
            - '../src/*/*/Tests/'

    acme_api.event.jwt_decoded_listener:
        class: App\Auth\Infrastructure\Security\TokenDecodeListener
        arguments: ['@App\Auth\Infrastructure\Doctrine\Persistence\DoctrineAuthUserRepository']
        tags:
            - { name: kernel.event_listener, event: lexik_jwt_authentication.on_jwt_decoded, method: invalidateTokenAfterLogout }

    # Repositories
    App\Hospital\Domain\HospitalRepositoryInterface:
        alias: App\Hospital\Infrastructure\Persistence\DoctrineHospitalRepository
        public: true

    # Queries
    App\Hospital\Application\HospitalQueryInterface:
        alias: App\Hospital\Infrastructure\Query\DbalHospitalQuery
        public: true

    App\Common\ApiExceptionListener:
        tags:
            - { name: kernel.event_listener, event: kernel.exception }

    App\Common\Doctrine\RequestTransactionSubscriber:
        arguments:
            $entityManager: '@doctrine.orm.entity_manager'
        tags:
            - { name: 'kernel.event_subscriber' }

    App\Common\Console\ConsoleTransactionSubscriber:
        arguments:
            $entityManager: '@doctrine.orm.entity_manager'
        tags:
            - { name: 'kernel.event_subscriber' }

    # Auth
    App\Auth\Domain\Persistence\AuthUserRepositoryInterface: '@App\Auth\Infrastructure\Doctrine\Persistence\DoctrineAuthUserRepository'


    command_handlers:
        namespace: App\
        resource: '%kernel.project_dir%/src/*/Application/CommandHandler/*Handler.php'
        autoconfigure: false
        tags:
            - { name: messenger.message_handler, bus: command.bus }

    App\Common\CorrelationId\CorrelationIdRequestMonologProcessor:
        arguments: ["@request_stack"]
        tags:
            - { name: monolog.processor }

    App\Logger\DoctrineEntityChangeSubscriber:
        tags:
            - { name: 'doctrine.event_subscriber' }
    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones

when@dev:
    services:
        App\DataFixtures\:
            resource: '../fixtures/'

when@test:
    services:
        App\DataFixtures\:
            resource: '../fixtures/'
