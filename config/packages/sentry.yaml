when@prod:
    sentry:
        dsn: '%env(SENTRY_DSN)%'
        messenger:
            enabled: true # flushes Sentry messages at the end of each message handling
            capture_soft_fails: true # capture
#        If you are using Monolog, you also need these additional configuration and services to log the errors correctly:
#        https://docs.sentry.io/platforms/php/guides/symfony/#monolog-integration
        register_error_listener: false
        options:
            send_default_pii: false
            profiles_sample_rate: 0.2
            traces_sample_rate: 0.2
            before_send: 'sentry.callback.before_send'

    monolog:
        handlers:
            sentry:
                type: service
                id: Sentry\Monolog\Handler

    services:
        Sentry\Monolog\Handler:
            arguments:
                $hub: '@Sentry\State\HubInterface'
                $level: !php/const Monolog\Logger::ERROR
                $bubble: false
        sentry.callback.before_send:
            class: 'App\Common\Sentry\SentryConfig'
            factory: [ '@App\Common\Sentry\SentryConfig', 'getBeforeSend' ]
when@dev:
    sentry:
        dsn: '%env(SENTRY_DSN)%'
        messenger:
            enabled: true # flushes Sentry messages at the end of each message handling
            capture_soft_fails: true # captures exceptions marked for retry too
        options:
            send_default_pii: true

            before_send: 'sentry.callback.before_send'
#        If you are using Monolog, you also need these additional configuration and services to log the errors correctly:
#        https://docs.sentry.io/platforms/php/guides/symfony/#monolog-integration
        register_error_listener: false

    monolog:
        handlers:
            sentry:
                type: service
                id: Sentry\Monolog\Handler

    services:
        Sentry\Monolog\Handler:
            arguments:
                $hub: '@Sentry\State\HubInterface'
                $level: !php/const Monolog\Logger::ERROR
                $bubble: false
        sentry.callback.before_send:
            class: 'App\Common\Sentry\SentryConfig'
            factory: [ '@App\Common\Sentry\SentryConfig', 'getBeforeSend' ]
