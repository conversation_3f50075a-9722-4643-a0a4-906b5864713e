security:
    # https://symfony.com/doc/current/security.html#registering-the-user-hashing-passwords
    password_hashers:
        Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: 'auto'
    # https://symfony.com/doc/current/security.html#loading-the-user-the-user-provider
    providers:
        doctrine_user_provider:
            id: App\Auth\Infrastructure\Security\DoctrineUserProvider
        users_in_memory: { memory: null }
    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false
        login:
            pattern: ^/api/login
            user_checker: App\Auth\Infrastructure\Security\UserChecker
            provider: doctrine_user_provider
            stateless: true
            json_login:
                check_path: /api/login_check
                success_handler: lexik_jwt_authentication.handler.authentication_success
                failure_handler: lexik_jwt_authentication.handler.authentication_failure
        api_token_refresh:
            pattern: /api/token/refresh
            stateless: true
            provider: doctrine_user_provider # provider in database
            refresh_jwt:
                check_path: gesdinet_jwt_refresh_token
        external_api:
            pattern: ^/api/v1/ext/
            stateless: true
            provider: users_in_memory
            custom_authenticators:
                - App\Auth\Infrastructure\Security\External\ExternalAuthenticator
        api:
            pattern: ^/api
            provider: doctrine_user_provider
            stateless: true
            jwt: ~
        main:
            lazy: true
            # activate different ways to authenticate
            # https://symfony.com/doc/current/security.html#the-firewall

            # https://symfony.com/doc/current/security/impersonating_user.html
            # switch_user: true

    # Easy way to control access for large sections of your site
    # Note: Only the *first* access control that matches will be used
    access_control:
        - { path: ^/api/(login|token/refresh), roles: PUBLIC_ACCESS }
        - { path: ^/api/v1/reset-password, roles: PUBLIC_ACCESS }
        - { path: ^/api/v1/reset-password/change-password, roles: PUBLIC_ACCESS }
        - { path: ^/api/v1/ext/, roles: ROLE_EXTERNAL_API }
        - { path: ^/api, roles: [ ROLE_CENTRAL_ADMINISTRATOR, ROLE_REPORTER_PHYSICIAN, ROLE_COORDINATOR, ROLE_DATA_ADMINISTRATOR ] }

when@test:
    security:
        password_hashers:
            # By default, password hashers are resource intensive and take time. This is
            # important to generate secure password hashes. In tests however, secure hashes
            # are not important, waste resources and increase test times. The following
            # reduces the work factor to the lowest possible values.
            Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
                algorithm: auto
                cost: 4 # Lowest possible value for bcrypt
                time_cost: 3 # Lowest possible value for argon
                memory_cost: 10 # Lowest possible value for argon
