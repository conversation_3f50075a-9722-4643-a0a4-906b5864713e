framework:
    messenger:
        serializer:
            default_serializer: messenger.transport.symfony_serializer
            symfony_serializer:
                format: json
                context: { }

        failure_transport: failed
        default_bus: command.bus
        buses:
            command.bus:
                middleware:
                    - doctrine_transaction
                    - dispatch_after_current_bus
            event.bus:
                default_middleware: allow_no_handlers
                middleware:
                    - App\Common\CorrelationId\AddCorrelationIdMiddleware
                    - dispatch_after_current_bus
                    - doctrine_transaction
                    - App\Logger\EventLoggerMiddleware
        transports:
            mailer:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                serializer: messenger.transport.native_php_serializer
                retry_strategy:
                    max_retries: 5
                    delay: 2000
                    multiplier: 2
                    max_delay: 0
                options:
                    queue_name: mailer
            async:
                dsn: '%env(MESSENGER_TRANSPORT_DSN)%'
                serializer: messenger.transport.symfony_serializer
                retry_strategy:
                    max_retries: 5
                    delay: 2000
                    multiplier: 2
                    max_delay: 0
                options:
                    queue_name: async
            failed:
                dsn: 'doctrine://default?queue_name=failed'
                serializer: messenger.transport.symfony_serializer
            sync: 'sync://'

        routing:
            'Symfony\Component\Mailer\Messenger\SendEmailMessage': mailer

when@test:
    framework:
        messenger:
            routing:
                'Symfony\Component\Mailer\Messenger\SendEmailMessage': sync
