doctrine:
    dbal:
        url: '%env(resolve:DATABASE_URL)%'

        # IMPORTANT: You MUST configure your server version,
        # either here or in the DATABASE_URL env var (see .env file)
        #server_version: '15'

        profiling_collect_backtrace: '%kernel.debug%'
    orm:
        auto_generate_proxy_classes: true
        enable_lazy_ghost_objects: true
        report_fields_where_declared: true
        validate_xml_mapping: true
        naming_strategy: doctrine.orm.naming_strategy.default
        auto_mapping: true
        mappings:
            Account:
                is_bundle: false
                type: xml
                dir: '%kernel.project_dir%/src/Account/Infrastructure/Doctrine/config/mapping'
                prefix: 'App\Account\Domain'
            Auth:
                is_bundle: false
                type: xml
                dir: '%kernel.project_dir%/src/Auth/Infrastructure/Doctrine/config/mapping'
                prefix: 'App\Auth\Domain'
            Logger:
                is_bundle: false
                dir: '%kernel.project_dir%/src/Logger'
                prefix: 'App\Logger'
            Hospital:
                is_bundle: false
                type: xml
                dir: '%kernel.project_dir%/src/Hospital/Infrastructure/Doctrine/config/mapping'
                prefix: 'App\Hospital\Domain'
            Patient:
                is_bundle: false
                type: xml
                dir: '%kernel.project_dir%/src/Patient/Infrastructure/Doctrine/config/mapping'
                prefix: 'App\Patient\Domain'
            Form:
                is_bundle: false
                dir: '%kernel.project_dir%/src/Form'
                prefix: 'App\Form'
            Dictionary:
                is_bundle: false
                dir: '%kernel.project_dir%/src/Dictionary'
                prefix: 'App\Dictionary'
            SharedKernel:
                is_bundle: false
                type: xml
                dir: '%kernel.project_dir%/src/SharedKernel/Doctrine/config/mapping'
                prefix: 'App\SharedKernel'

when@prod:
    doctrine:
        orm:
            auto_generate_proxy_classes: false
            proxy_dir: '%kernel.build_dir%/doctrine/orm/Proxies'
            query_cache_driver:
                type: pool
                pool: doctrine.system_cache_pool
            result_cache_driver:
                type: pool
                pool: doctrine.result_cache_pool

    framework:
        cache:
            pools:
                doctrine.result_cache_pool:
                    adapter: cache.app
                doctrine.system_cache_pool:
                    adapter: cache.system
