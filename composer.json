{"name": "fundacja-na-ratunek/hope-r-api", "description": "HOPE R API", "type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.3", "ext-ctype": "*", "ext-fileinfo": "*", "ext-iconv": "*", "ext-intl": "*", "ext-json": "*", "ext-pdo_mysql": "*", "doctrine/dbal": "^3.8", "doctrine/doctrine-bundle": "^2.11", "doctrine/doctrine-migrations-bundle": "^3.2", "doctrine/orm": "^2.0", "fakerphp/faker": "^1.23", "gesdinet/jwt-refresh-token-bundle": "^1.3", "lexik/jwt-authentication-bundle": "^2.19", "nelmio/cors-bundle": "^2.4", "phpdocumentor/reflection-docblock": "^5.3", "sentry/sentry-symfony": "^4.13", "symfony/clock": "6.4.*", "symfony/console": "6.4.*", "symfony/doctrine-messenger": "6.4.*", "symfony/dotenv": "6.4.*", "symfony/expression-language": "6.4.*", "symfony/flex": "^2", "symfony/framework-bundle": "6.4.*", "symfony/google-mailer": "6.4.*", "symfony/mailer": "6.4.*", "symfony/messenger": "6.4.*", "symfony/monolog-bundle": "3.10.*", "symfony/runtime": "6.4.*", "symfony/security-bundle": "6.4.*", "symfony/serializer": "6.4.*", "symfony/string": "6.4.*", "symfony/translation": "6.4.*", "symfony/twig-bundle": "6.4.*", "symfony/uid": "6.4.*", "symfony/validator": "6.4.*", "symfony/yaml": "6.4.*", "webmozart/assert": "^1.11"}, "require-dev": {"doctrine/doctrine-fixtures-bundle": "^3.5", "friendsofphp/php-cs-fixer": "^3.45", "phpstan/extension-installer": "^1.3", "phpstan/phpstan": "^1.10", "phpstan/phpstan-doctrine": "^1.3", "phpstan/phpstan-phpunit": "^1.3", "phpstan/phpstan-symfony": "^1.3", "phpstan/phpstan-webmozart-assert": "^1.2", "phpunit/phpunit": "^10.5", "roave/security-advisories": "dev-latest", "symfony/browser-kit": "6.4.*", "symfony/css-selector": "6.4.*", "symfony/debug-bundle": "6.4.*", "symfony/phpunit-bridge": "^7.0", "symfony/stopwatch": "6.4.*", "symfony/web-profiler-bundle": "6.4.*"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true, "phpstan/extension-installer": true}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests", "App\\DataFixtures\\": "fixtures"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "symfony-serve": "symfony server:start", "post-update-cmd": ["@auto-scripts"], "fix-cs": ["php-cs-fixer fix --diff --allow-risky=yes --config .php-cs-fixer.php"], "static:analyze": ["php bin/console cache:clear -q --env test", "php-cs-fixer fix --diff --allow-risky=yes --dry-run --config .php-cs-fixer.php", "php -d memory_limit=-1 vendor/bin/phpstan clear-result-cache", "php -d memory_limit=-1 vendor/bin/phpstan analyze -c phpstan.neon -v"], "tests": ["php bin/console cache:clear -q --env test", "php bin/phpunit --colors=always"], "tests:unit": ["php bin/phpunit --filter Unit --colors=always"], "tests:integration": ["php bin/phpunit --filter Integration --colors=always"], "tests:http": ["php bin/phpunit --filter Http --colors=always"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "6.4.*"}}}