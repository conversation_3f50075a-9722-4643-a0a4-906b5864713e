FROM php:8.3-fpm

# Instalacja pakietów
RUN apt-get update \
  && apt-get install -y --no-install-recommends zlib1g-dev libssl-dev libicu-dev g++ libzip-dev libpq-dev libonig-dev \
  && apt-get install -y --no-install-recommends gnupg libfreetype6-dev libjpeg62-turbo-dev libpng-dev openssl \
  && apt-get install -y --no-install-recommends unzip mc nano wget locales git

# Sentry profiling
RUN pecl install excimer
RUN echo "extension=excimer.so" > /usr/local/etc/php/conf.d/excimer.ini

# Instalacja rozszerzenia intl
RUN docker-php-ext-configure intl
RUN docker-php-ext-install intl

RUN docker-php-ext-configure opcache --enable-opcache

# Instalacja rozszerzenia OpCache
RUN docker-php-ext-install opcache

# Instalacja rozszerzenia PostgresSQL
RUN docker-php-ext-install pdo_pgsql

# Instalacja rozszerzenia MYSQL
RUN docker-php-ext-install mysqli pdo_mysql

# Install Curl ext
RUN apt-get install -y libcurl4-openssl-dev \
&& docker-php-ext-install curl

# Install Composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# Install Symfony CLI
RUN curl -sS https://get.symfony.com/cli/installer | bash
RUN mv /root/.symfony5/bin/symfony /usr/local/bin/symfony

# ZSH
RUN sh -c "$(wget -O- https://github.com/deluan/zsh-in-docker/releases/download/v1.1.5/zsh-in-docker.sh)" -- \
    -t apple \
    -a 'SPACESHIP_PROMPT_ADD_NEWLINE="false"' \
    -a 'SPACESHIP_PROMPT_SEPARATE_LINE="false"' \
    -p git \
    -p ssh-agent \
    -p https://github.com/zsh-users/zsh-autosuggestions \
    -p https://github.com/zsh-users/zsh-completions

# Ustawienie strefy czasowej na 'Europe/Warsaw'
RUN apt-get update && apt-get install -y tzdata \
    && ln -fs /usr/share/zoneinfo/Europe/Warsaw /etc/localtime \
    && dpkg-reconfigure -f noninteractive tzdata
