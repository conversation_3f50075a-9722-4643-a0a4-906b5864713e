server {
   listen 80;
   server_name localhost;
   root /var/www/html/public;

   client_max_body_size 1000M;

   location / {
      try_files $uri /index.php$is_args$args;
   }

   location ~ ^/.+\.php(/|$) {
      fastcgi_pass php:9000;
      fastcgi_split_path_info ^(.+\.php)(/.*)$;
      include fastcgi_params;
      fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
      fastcgi_param DOCUMENT_ROOT $realpath_root;
      internal;
   }

   error_log /var/log/nginx/project_error.log;
   access_log /var/log/nginx/project_access.log;
}
