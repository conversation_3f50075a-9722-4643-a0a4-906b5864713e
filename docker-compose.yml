version: '3'
services:
  web:
    image: nginx:latest
    container_name: hope_web
    restart: always
    ports:
      - 8080:80
    volumes:
      - .:/var/www/html
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - php
    links:
      - mailer
    networks:
      - hope_network

  php:
    build:
      context: docker/php
      dockerfile: Dockerfile
    container_name: hope_php
    restart: always
    working_dir: /var/www/html
    environment:
      - TZ=Europe/Warsaw
    volumes:
      - ./docker/php/php-fpm.conf:/usr/local/etc/php-fpm.conf
      - ./docker/php/php.ini:/usr/local/etc/php/php.ini
      - .:/var/www/html
    networks:
      - hope_network

  db:
    image: mariadb:10.6
    container_name: hope_db
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: hope_registry
      MYSQL_USER: hope_registry
      MYSQL_PASSWORD: hope_registry
    ports:
      - "3306:3306"
    volumes:
      - ./docker/db/data:/var/lib/mysql
    networks:
      - hope_network

  ###> symfony/mailer ###
  mailer:
    image: schickling/mailcatcher
    container_name: hope_mailer
    restart: always
    ports: [ "1025:1025", "1080:1080" ]
    networks:
      - hope_network
  ###< symfony/mailer ###

networks:
  hope_network:
