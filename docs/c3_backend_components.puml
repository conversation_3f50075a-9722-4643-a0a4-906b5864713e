@startuml C3 - Component Diagram for Backend API

!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Component.puml

LAYOUT_WITH_LEGEND()

title Component Diagram for Hope Registry - Backend API

Container_Boundary(backend_api_c, "Backend API (Symfony 6.4, PHP 8.3+)") {
    Component(security_comp, "Security Component", "Symfony Security, LexikJWTAuthenticationBundle", "Obsługuje uwierzytelnianie użytkowników (JWT) i autoryzację.")
    Component(bff_facade, "BackendForFrontend Facade", "PHP/Symfony Controller/Service", "Dostarcza zunifikowaną bramę API dla Frontend PWA. Eksponuje endpointy z src/BackendForFrontend.")
    Component(account_mgmt, "Account Management", "PHP/Symfony Service", "Zarządza kontami użytkowników, profilami, rolami. (src/Account)")
    Component(patient_mgmt, "Patient Management", "PHP/Symfony Service", "Zarządza danymi pacjentów, kartotekami i powiązaną logiką. (src/Patient)")
    Component(hospital_mgmt, "Hospital Management", "PHP/Symfony Service", "Zarządza informacjami o szpitalach i powiązaną logiką. (src/Hospital)")
    Component(dictionary_mgmt, "Dictionary Management", "PHP/Symfony Service", "Zarządza słownikami systemowymi i wartościami lookup. (src/Dictionary)")
    Component(form_processing, "Form Processing", "Symfony Forms", "Obsługuje przesyłanie formularzy po stronie serwera, walidację. (src/Form)")
    Component(common_services, "Common Services", "PHP/Symfony Services", "Obejmuje logowanie, powiadomienia (email) i inne współdzielone narzędzia. (src/Common, src/Logger)")
    Component(doctrine_orm, "Database Access (Doctrine ORM)", "Doctrine", "Zapewnia trwałość danych i warstwę abstrakcji dla bazy danych.")

    Rel(bff_facade, security_comp, "Używa", "Do uwierzytelniania/autoryzacji żądań")
    Rel(bff_facade, account_mgmt, "Deleguje do")
    Rel(bff_facade, patient_mgmt, "Deleguje do")
    Rel(bff_facade, hospital_mgmt, "Deleguje do")
    Rel(bff_facade, dictionary_mgmt, "Deleguje do")
    Rel(bff_facade, form_processing, "Używa", "Do obsługi danych formularzy")

    Rel(account_mgmt, doctrine_orm, "Używa", "Odczytuje/Zapisuje dane użytkowników")
    Rel(patient_mgmt, doctrine_orm, "Używa", "Odczytuje/Zapisuje dane pacjentów")
    Rel(hospital_mgmt, doctrine_orm, "Używa", "Odczytuje/Zapisuje dane szpitali")
    Rel(dictionary_mgmt, doctrine_orm, "Używa", "Odczytuje/Zapisuje dane słowników")

    Rel(account_mgmt, common_services, "Używa", "np. do wysyłania emaili związanych z kontem")
    Rel(patient_mgmt, common_services, "Używa", "np. do powiadomień")
    Rel(hospital_mgmt, common_services, "Używa", "np. do powiadomień")
    
    Rel(common_services, security_comp, "Używa", "Aby uzyskać kontekst bieżącego użytkownika dla logowania/powiadomień")
}

System_Ext(frontend_pwa_ext, "Frontend PWA", "Vue.js", "Aplikacja kliencka wykonująca żądania.")
System_Ext(email_service_ext, "Email Service", "Zewnętrzna usługa do wysyłania emaili.")
System_Ext(sentry_ext, "Sentry", "Zewnętrzna usługa do śledzenia błędów.")
System_Ext(database_ext, "Database", "MariaDB 10.6", "Relacyjna baza danych.")

Rel(frontend_pwa_ext, bff_facade, "Wykonuje wywołania API do", "HTTPS/JSON")
Rel(common_services, email_service_ext, "Wysyła emaile poprzez", "SMTP/API")
Rel(backend_api_c, sentry_ext, "Wysyła raporty o błędach do", "HTTPS")
Rel(doctrine_orm, database_ext, "Odczytuje z i zapisuje do", "SQL")

@enduml
