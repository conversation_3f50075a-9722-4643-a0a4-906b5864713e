@startuml C1 - System Context

!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Context.puml

LAYOUT_WITH_LEGEND()

title System Context Diagram for Hope Registry

Person(patient, "Patient", "Osoba zarejestrowana w systemie, poszukująca lub zarządzająca informacjami medycznymi/wsparciem.")
Person(hospital_staff, "Administrator", "Analityka danych pacjentów i szpitali.")

System_Ext(email_service, "Email Service", "Zewnętrzna usługa do wysyłania transakcyjnych wiadomości email (np. powiadomienia, resetowanie hasła).")
System_Ext(sentry, "Sentry", "Zewnętrzna usługa do śledzenia błędów i monitorowania.")

System(hope_registry, "Hope Registry", "Platforma do zarządzania informacjami o pacjentach i szpitalach, ułatwiająca wsparcie i koordynację.")

Rel(patient, hope_registry, "Używa", "Przegląda i zarządza swoim profilem, uzyskuje dostęp do informacji")
Rel(hospital_staff, hope_registry, "Zarządza", "Zarządza kartotekami pacjentów, danymi szpitali, konfiguracjami systemu")

Rel_Back(hope_registry, email_service, "Wysyła Emaile poprzez", "SMTP/API")
Rel_Back(hope_registry, sentry, "Wysyła Raporty o Błędach do", "HTTPS")

@enduml
