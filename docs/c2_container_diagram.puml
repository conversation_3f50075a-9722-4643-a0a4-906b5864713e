@startuml C2 - Container Diagram

!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Container.puml

LAYOUT_WITH_LEGEND()

title Container Diagram for Hope Registry

Person(patient, "Patient", "Osoba zarejestrowana w systemie, poszukująca lub zarządzająca informacjami medycznymi/wsparciem.")
Person(hospital_staff, "Administrator", "Analityka danych pacjentów i szpitali.")

System_Ext(email_service, "Email Service", "Zewnętrzna usługa do wysyłania transakcyjnych wiadomości email.")
System_Ext(sentry, "Sentry", "Zewnętrzna usługa do śledzenia błędów i monitorowania.")

System_Boundary(hope_registry_sb, "Hope Registry System") {
    Container(frontend_pwa, "Frontend PWA", "Vue.js, TypeScript, Vite", "Dostarcza interfejs użytkownika dla pacjentów i personelu szpitala. Działa w przeglądarce użytkownika.")
    Container(backend_api, "Backend API", "Symfony 6.4 (PHP 8.3+)", "Obsługuje logikę biznesową, przetwarzanie danych i dostarcza API dla frontendu.")
    ContainerDb(database, "Database", "MariaDB 10.6", "Przechowuje konta użytkowników, dane pacjentów, informacje o szpitalach itp.")

    Rel(frontend_pwa, backend_api, "Wykonuje wywołania API do", "HTTPS/JSON")
    Rel(backend_api, database, "Odczytuje i zapisuje do", "SQL/Doctrine ORM")
    Rel_Back(backend_api, email_service, "Wysyła emaile poprzez", "SMTP/API")
    Rel_Back(backend_api, sentry, "Wysyła raporty o błędach do", "HTTPS")
}

Rel(patient, frontend_pwa, "Używa", "HTTPS")
Rel(hospital_staff, frontend_pwa, "Używa", "HTTPS")

@enduml
