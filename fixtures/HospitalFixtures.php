<?php

declare(strict_types=1);

namespace App\DataFixtures;

use App\Common\Uuid;
use App\Hospital\Domain\Hospital;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use Faker\Factory;
use Faker\Generator;

class HospitalFixtures extends Fixture
{
    public const DEFAULT_HOSPITAL_ID_1 = '13cd98bf-5c0e-472f-a4ef-31e28422f0ec';
    public const DEFAULT_HOSPITAL_ID_2 = '9ef94ba8-838c-42ee-81ce-cdd0ae01a1ec';

    public function load(ObjectManager $manager): void
    {
        $faker = Factory::create('pl_PL');

        $defaultHospital_1 = $this->createDefaultHospital(self::DEFAULT_HOSPITAL_ID_1, $faker);
        $defaultHospital_2 = $this->createDefaultHospital(self::DEFAULT_HOSPITAL_ID_2, $faker);
        $manager->persist($defaultHospital_1);
        $manager->persist($defaultHospital_2);

        for ($i = 0; $i < 500; ++$i) {
            $hospital = $this->createHospital($faker, $i);

            $manager->persist($hospital);
        }

        $manager->flush();
    }

    protected function createDefaultHospital(string $hospitalId, Generator $faker): Hospital
    {
        $city = $faker->city;

        return Hospital::create(
            Uuid::fromString($hospitalId),
            'HOSP_'.$faker->randomNumber(4, true),
            'Szpital Specjalistyczny w '.$city,
            $faker->streetAddress,
            $faker->postcode,
            $city
        );
    }

    protected function createHospital(Generator $faker, int $i): Hospital
    {
        $city = $faker->city;

        return Hospital::create(
            Uuid::generate(),
            'HOSP_'.$i,
            'Szpital Specjalistyczny w '.$city,
            $faker->streetAddress,
            $faker->postcode,
            $city
        );
    }
}
