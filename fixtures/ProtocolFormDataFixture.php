<?php

declare(strict_types=1);

namespace App\DataFixtures;

use App\Common\Uuid;
use App\Form\ProtocolFormData;
use App\SharedKernel\Protocol;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use Faker\Factory;

class ProtocolFormDataFixture extends Fixture
{
    public function load(ObjectManager $manager): void
    {
        $faker = Factory::create();

        for ($i = 0; $i < 5; ++$i) {
            $randomJsonData = [];
            for ($j = 0; $j < 20; ++$j) {
                $randomJsonData[$faker->word] = $faker->randomElement([$faker->word, $faker->numberBetween(1, 100), $faker->boolean]);
            }

            $protocolFormData = ProtocolFormData::create(
                Uuid::fromString($faker->uuid),
                Uuid::fromString($faker->uuid),
                $faker->word,
                Uuid::fromString($faker->uuid),
                Protocol::from($faker->randomElement(Protocol::values())),
                (string) json_encode($randomJsonData)
            );

            $manager->persist($protocolFormData);

            if ($i % 50 === 0) {
                $manager->flush();
                $manager->clear();
            }
        }

        $manager->flush();
    }
}
