<?php

declare(strict_types=1);

namespace App\DataFixtures;

use App\Common\Uuid;
use App\Patient\Domain\Address;
use App\Patient\Domain\BirthDate;
use App\Patient\Domain\Citizenship;
use App\Patient\Domain\Gender;
use App\Patient\Domain\PassportNumber;
use App\Patient\Domain\Patient;
use App\Patient\Domain\PatientProtocol;
use App\Patient\Domain\PatientPublicId;
use App\SharedKernel\Protocol;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use Faker\Factory;

class PatientFixtures extends Fixture
{
    public function load(ObjectManager $manager): void
    {
        $faker = Factory::create('pl_PL');
        for ($i = 0; $i < 400; ++$i) {
            // Exclude 'PL' from the possible citizenship values
            $citizenshipValues = array_diff(Citizenship::values(), ['PL']);
            $citizenship = $faker->randomElement($citizenshipValues);
            $gender = $faker->randomElement(Gender::values());
            $birthDate = BirthDate::createFromString($faker->date());
            $passportNumber = PassportNumber::create($faker->numerify('###########'));
            $patientPublicId = PatientPublicId::create($faker->firstName, $faker->lastName);
            $residenceAddress = Address::create($faker->streetAddress, $faker->city, $faker->postcode);
            $protocol = $faker->randomElement(Protocol::values());

            if ($protocol === Protocol::FOLLOWUP->value) {
                $protocol = Protocol::STS->value;
            }

            // Randomly assign either one or two hospitals to each patient
            $hospitals = $faker->randomElement([
                [HospitalFixtures::DEFAULT_HOSPITAL_ID_1],
                [HospitalFixtures::DEFAULT_HOSPITAL_ID_1, HospitalFixtures::DEFAULT_HOSPITAL_ID_2],
            ]);

            $patient = Patient::create(
                Uuid::generate(),
                $patientPublicId,
                $faker->firstName,
                $faker->lastName,
                Citizenship::from($citizenship),
                true,
                true,
                $residenceAddress,
                null,
                null,
                $faker->email,
                $faker->phoneNumber,
                true,
                null,
                $passportNumber,
                null,
                null,
                Gender::from($gender),
                $birthDate,
                $hospitals,
                Protocol::from($protocol)
            );

            $patientProtocol = $patient->protocols()->first();
            if ($patientProtocol instanceof PatientProtocol) {
                $patient->updatePatientProtocolTreatmentStartDate(
                    $patientProtocol->patientProtocolId(),
                    \DateTimeImmutable::createFromMutable(
                        $faker->dateTimeBetween('-2 year', 'now')
                    )
                );
            }

            $manager->persist($patient);
        }

        $manager->flush();
    }
}
