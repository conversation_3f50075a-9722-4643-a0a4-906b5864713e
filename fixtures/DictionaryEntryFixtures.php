<?php

declare(strict_types=1);

namespace App\DataFixtures;

use App\Dictionary\DictionaryEntry;
use App\Dictionary\DictionaryEntryRepository;
use App\SharedKernel\DictionaryType;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use Faker\Factory;

class DictionaryEntryFixtures extends Fixture
{
    private DictionaryEntryRepository $dictionaryEntryRepository;

    public function __construct(DictionaryEntryRepository $dictionaryEntryRepository)
    {
        $this->dictionaryEntryRepository = $dictionaryEntryRepository;
    }

    public function load(ObjectManager $manager): void
    {
        $faker = Factory::create();

        foreach (DictionaryType::cases() as $dictionaryType) {
            for ($i = 0; $i < 100; ++$i) {
                $value = $dictionaryType->value.$i;
                $dictionaryEntry = DictionaryEntry::create(
                    $value,
                    $value.' '.$faker->sentence,
                    $value.' '.$faker->sentence,
                    $dictionaryType
                );

                $this->dictionaryEntryRepository->add($dictionaryEntry);
            }
        }

        $manager->flush();
    }
}
