<?php

declare(strict_types=1);

namespace App\DataFixtures;

use App\Account\Domain\Account;
use App\Auth\Domain\AuthUser;
use App\Auth\Domain\Password;
use App\Common\Uuid;
use App\SharedKernel\Email;
use App\SharedKernel\Protocol;
use App\SharedKernel\Roles;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;
use Doctrine\Persistence\ObjectManager;

class AccountAndAuthFixtures extends Fixture implements DependentFixtureInterface
{
    public function load(ObjectManager $manager): void
    {
        $this->createCentralAdministrator($manager);
        $this->createReporterPhysician($manager);
        $this->createCoordinator($manager);
        $this->createDataAdministrator($manager);

        $manager->flush();
    }

    private function createCentralAdministrator(ObjectManager $manager): void
    {
        $accountId = Uuid::generate();
        $firstName = 'Central';
        $lastName = 'Administrator';
        $email = Email::create('<EMAIL>');
        $mobilePhoneNumber = '+***********';
        $password = new Password('DaBT3cdKk2Z!');
        $role = Roles::ROLE_CENTRAL_ADMINISTRATOR;

        $account = Account::createCentralAdminAccount(
            $accountId,
            $firstName,
            $lastName,
            $email,
            $mobilePhoneNumber
        );

        $authUser = $this->createAuthUser($accountId, $email, $password, $role, $account->isActive());

        $manager->persist($account);
        $manager->persist($authUser);
    }

    public function createReporterPhysician(ObjectManager $manager): void
    {
        $accountId = Uuid::generate();
        $firstName = 'Reporter';
        $lastName = 'Physician';
        $email = Email::create('<EMAIL>');
        $mobilePhoneNumber = '+***********';
        $password = new Password('DaBT3cdKk2Z!');
        $role = Roles::ROLE_REPORTER_PHYSICIAN;
        $protocols = [Protocol::ALCL->value, Protocol::STS->value];
        $hospitals = [HospitalFixtures::DEFAULT_HOSPITAL_ID_1];

        $account = Account::createReportingPhysician(
            $accountId,
            $firstName,
            $lastName,
            $email,
            $mobilePhoneNumber,
            $protocols,
            $hospitals
        );

        $authUser = $this->createAuthUser($accountId, $email, $password, $role, $account->isActive());

        $manager->persist($account);
        $manager->persist($authUser);
    }

    public function createCoordinator(ObjectManager $manager): void
    {
        $accountId = Uuid::generate();
        $firstName = 'Coordinator';
        $lastName = 'Coordinator';
        $email = Email::create('<EMAIL>');
        $mobilePhoneNumber = '+***********';
        $password = new Password('DaBT3cdKk2Z!');
        $role = Roles::ROLE_COORDINATOR;
        $protocols = [Protocol::ALCL->value, Protocol::STS->value];

        $account = Account::createCoordinator(
            $accountId,
            $firstName,
            $lastName,
            $email,
            $mobilePhoneNumber,
            $protocols
        );

        $authUser = $this->createAuthUser($accountId, $email, $password, $role, $account->isActive());

        $manager->persist($account);
        $manager->persist($authUser);
    }

    public function createDataAdministrator(ObjectManager $manager): void
    {
        $accountId = Uuid::generate();
        $firstName = 'Data';
        $lastName = 'Administrator';
        $email = Email::create('<EMAIL>');
        $mobilePhoneNumber = '+***********';
        $password = new Password('DaBT3cdKk2Z!');
        $role = Roles::ROLE_DATA_ADMINISTRATOR;
        $hospitals = [HospitalFixtures::DEFAULT_HOSPITAL_ID_2];

        $account = Account::createDataAdministrator(
            $accountId,
            $firstName,
            $lastName,
            $email,
            $mobilePhoneNumber,
            $hospitals
        );

        $authUser = $this->createAuthUser($accountId, $email, $password, $role, $account->isActive());

        $manager->persist($account);
        $manager->persist($authUser);
    }

    private function createAuthUser(Uuid $id, Email $email, Password $password, Roles $role, bool $isActive): AuthUser
    {
        $authUser = AuthUser::createAuthUser($id, $email, $password, $role, $isActive);
        $authUser->changePassword('DaBT3cdKk2Z!', 'DaBT3cdKk2Z!');

        return $authUser;
    }

    public function getDependencies(): array
    {
        return [
            HospitalFixtures::class,
        ];
    }
}
