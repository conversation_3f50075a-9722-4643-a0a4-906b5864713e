name: Build and Test
on:
  pull_request:
    branches: [ master, test, develop ]
jobs:
  build-and-test:
    name: <PERSON><PERSON><PERSON><PERSON>  (PHP ${{ matrix.php-versions }} on ${{ matrix.operating-system }})
    runs-on: ${{ matrix.operating-system }}
    strategy:
      fail-fast: false
      matrix:
        operating-system: [ubuntu-22.04]
        php-versions: ['8.3']
    steps:
      # Mandatory : fetch the current repository
      - name: Checkout
        uses: actions/checkout@v4

      # Docs: https://github.com/shivammathur/setup-php
      - name: Setup PHP, with composer and extensions
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-versions }}
          extensions: mbstring, xml, ctype, iconv, intl, pdo_mysql, json, fileinfo
        env:
          update: true

      # To be faster, use a cache system for the Composer
      - name: Get composer cache directory
        id: composer-cache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache composer dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      # Ensure that composer.json is valid
      - name: Validate composer.json and composer.lock
        run: composer validate

      - name: Install Composer dependencies
        run: composer install --no-progress --prefer-dist --optimize-autoloader

      - name: Run Check CS
        run: |
          mv .php-cs-fixer.dist.php .php-cs-fixer.php
          vendor/bin/php-cs-fixer fix --diff --allow-risky=yes --dry-run --config .php-cs-fixer.php

      - name: Generate Symfony container
        run: php bin/console lint:container --env dev

      - name: Run tests
        run: composer tests:unit

      - name: Run PHPStan (static analyze)
        run: |
          mv phpstan.neon.dist phpstan.neon
          composer static:analyze
