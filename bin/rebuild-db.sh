#!/usr/bin/env bash
symfony security:check
php bin/console doctrine:database:create --if-not-exists
php bin/console doctrine:schema:drop --force --full-database
php bin/console doctrine:schema:create
php bin/console messenger:setup-transports
export FIXTURES_LOADING=true
trap 'unset FIXTURES_LOADING' EXIT
php bin/console doctrine:fixtures:load --no-interaction

# Dump schema
mkdir -p .cursor/rules/
php bin/console doctrine:schema:create --dump-sql > .cursor/rules/schemat_bazy_danych.sql