<?php

declare(strict_types=1);
$finder = PhpCsFixer\Finder::create()
    ->in(__DIR__.'/src')
    ->in(__DIR__.'/tests')
    ->in(__DIR__.'/fixtures')
;

$config = (new PhpCsFixer\Config())
    ->setParallelConfig(PhpCsFixer\Runner\Parallel\ParallelConfigFactory::detect())
    ->setCacheFile(__DIR__.'var/.php-cs-fixer.cache');

return $config->setRules([
    '@PSR12' => true,
    '@PHP81Migration' => true,
    '@DoctrineAnnotation' => true,
    '@Symfony' => true,
    'full_opening_tag' => false,
    'array_syntax' => ['syntax' => 'short'],
    'no_superfluous_elseif' => true,
    'no_superfluous_phpdoc_tags' => ['allow_mixed' => true],
    'no_useless_else' => true,
    'no_useless_return' => true,
    'no_unused_imports' => true,
    'ordered_imports' => [
        'imports_order' => null,
        'sort_algorithm' => 'alpha',
    ],
    'phpdoc_line_span' => [
        'property' => 'single',
        'method' => 'single',
        'const' => 'single',
    ],
    'phpdoc_order' => true,
    'phpdoc_align' => true,
    'phpdoc_no_access' => true,
    'phpdoc_separation' => true,
    'single_quote' => true,
    'trim_array_spaces' => true,
    'blank_lines_before_namespace' => true,
    'yoda_style' => false,
    'function_typehint_space' => true,
    'nullable_type_declaration_for_default_null_value' => true,

    // risk
    'no_unreachable_default_argument_value' => false,
    'declare_strict_types' => true,
    'void_return' => true,
    'php_unit_test_case_static_method_calls' => [
        'call_type' => 'self'
    ],
])->setFinder($finder);
