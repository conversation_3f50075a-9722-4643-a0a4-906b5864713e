---
description: Documentation practices for code and API
globs: ["src/**/*.php"]
alwaysApply: false
---

# Documentation

## Code Documentation

- Use PHPDoc to document classes, methods, and properties
- Document all public APIs
- Use types in PHPDoc annotations

### PHPDoc Example

```php
/**
 * Creates a new patient in the system.
 *
 * @param string $patientId The UUID of the patient to create
 * @param CreatePatientDto $dto Data transfer object containing patient information
 * @throws PatientCannotBeCreatedException When patient creation fails due to business rules
 * @throws PatientAlreadyExistsException When a patient with the same PESEL already exists
 */
public function create(string $patientId, CreatePatientDto $dto): void
{
    // Implementation...
}
```

## Documentation for Complex Types

For arrays and collections, specify the contained types:

```php
/**
 * @param array<string, mixed> $data Raw data from the request
 * @return array<int, PatientView> List of patient views
 */
public function processPatientData(array $data): array
{
    // Implementation...
}
```

## API Documentation

- API endpoints should be documented with annotations
- API documentation should be generated automatically
- Document request and response formats, status codes, and error cases

### API Documentation Example

```php
/**
 * Create a new patient.
 *
 * @Route("/patient", name="patient_create", methods={"POST"})
 *
 * @param CreatePatientDto $dto Data transfer object containing patient information
 * @return JsonResponse Response with the created patient ID
 *
 * @throws ValidationException When the input data is invalid
 *
 * @Response(
 *     response=201,
 *     description="Patient created successfully",
 *     @JsonContent(
 *         type="object",
 *         @Property(property="patientId", type="string", example="123e4567-e89b-12d3-a456-426614174000")
 *     )
 * )
 * @Response(
 *     response=400,
 *     description="Invalid input data",
 *     @JsonContent(ref="#/components/schemas/Error")
 * )
 * @Response(
 *     response=409,
 *     description="Patient with the same PESEL already exists",
 *     @JsonContent(ref="#/components/schemas/Error")
 * )
 */
public function create(CreatePatientDto $dto): JsonResponse
{
    // Implementation...
}
```

## Project Documentation

- Project documentation should be stored in the `docs/` directory
- Each major feature or component should have its own documentation file
- Maintain a central README.md with links to specific documentation files

## Documentation Maintenance

- Documentation should be updated when code changes
- Documentation should be reviewed during code reviews
- Keep documentation concise and focused on the most important aspects
