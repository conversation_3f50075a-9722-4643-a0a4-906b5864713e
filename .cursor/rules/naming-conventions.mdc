---
description: Naming conventions for classes, methods, and files
globs: ["src/**/*.php", "tests/**/*.php"]
alwaysApply: false
---

# Naming Conventions

## General Rules

- Use **camelCase** for variables, methods, and properties
- Use **PascalCase** for class names, interfaces, and types
- Use **snake_case** for database table names
- Use contextual prefixes for tables (e.g., `patient_patient`, `hospital_hospital`)

## Specific Conventions

- **Interfaces**: Interface names end with `Interface` (e.g., `PatientRepositoryInterface`)
- **Repositories**: Repository implementations start with the technology name (e.g., `DoctrinePatientRepository`)
- **Facades**: Facade names end with `Facade` (e.g., `PatientFacade`)
- **Controllers**: Controller names end with `Controller` (e.g., `PatientApiController`)
- **DTOs**: DTO names end with `Dto` (e.g., `CreatePatientDto`)
- **Exceptions**: Exception names end with `Exception` (e.g., `PatientNotFoundException`)
- **Queries**: Query interfaces end with `QueryInterface` (e.g., `PatientQueryInterface`)
- **Views**: View objects end with `View` (e.g., `PatientView`)

## Method Naming Conventions

### Entity Methods

- **Static Constructors**: Start with `create` (e.g., `Patient::create()`)
- **Getters**: Getter names do not include the `get` prefix (e.g., `$patient->firstName()`)
- **Modifiers**: Describe the action (e.g., `$account->deactivate()`, `$patient->addHospital()`)
- **Exception Factory Methods**: Start with `because` (e.g., `PatientNotFoundException::becauseNotExistInDatabase()`)

### Repository Methods

- **Retrieving a Single Object**: `getBy[Criteria]` - throws an exception if the object doesn't exist
- **Finding an Object**: `findBy[Criteria]` - returns `null` if the object doesn't exist
- **Adding an Object**: `add` (e.g., `$repository->add($patient)`)
- **Updating an Object**: `update` (e.g., `$repository->update($patient)`)

### Facade Methods

- **CRUD Operations**: `create`, `update`, `activate`, `deactivate`
- **Finding**: `findBy[Criteria]` (e.g., `$facade->findByPatientId($id)`)
- **Retrieving Collections**: `getAll[Entities]` (e.g., `$facade->getAllHospitals()`)

## File Naming Conventions

- One file per class/interface
- The file name matches the class/interface name
- Doctrine mapping files: `[Entity].orm.xml`
- Test files: `[Tested class]Test.php`
