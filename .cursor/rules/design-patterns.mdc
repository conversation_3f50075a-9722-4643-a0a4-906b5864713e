---
description: Design patterns used in the project
globs: ["src/**/*.php"]
alwaysApply: false
---

# Design Patterns

## Architectural Patterns

- **Hexagonal Architecture (Ports and Adapters)**: Separation of business logic from technical details
- **Domain-Driven Design (DDD)**: Modeling the business domain
- **Command Query Responsibility Segregation (CQRS)**: Separation of read and write operations
- **Backend For Frontend (BFF)**: Dedicated API layer for the frontend application

## Design Patterns

- **Repository Pattern**: Abstraction of data access
- **Facade Pattern**: Simplified interface to a complex subsystem
- **Factory Pattern**: Creating objects without revealing the creation logic
- **DTO Pattern**: Data transfer between layers
- **Value Object Pattern**: Immutable objects representing business concepts

## Pattern Implementation Examples

### Repository Pattern

Repository interface definition in the domain layer:

```php
interface PatientRepositoryInterface
{
    public function add(Patient $patient): void;
    public function update(Patient $patient): void;
    public function getByPatientId(Uuid $patientId): Patient;
    public function findByPesel(Pesel $pesel, ?Uuid $excludePatientId = null): ?Patient;
}
```

Repository implementation in the infrastructure layer:

```php
class DoctrinePatientRepository implements PatientRepositoryInterface
{
    public function __construct(private EntityManagerInterface $em) {}

    public function add(Patient $patient): void
    {
        $this->em->persist($patient);
        $this->em->flush();
    }

    public function getByPatientId(Uuid $patientId): Patient
    {
        $patient = $this->em->find(Patient::class, $patientId);
        if (!$patient instanceof Patient) {
            throw PatientNotFoundException::withId($patientId->toString());
        }
        return $patient;
    }
}
```

### Value Object Pattern

```php
class Pesel implements ValueObject
{
    private ?string $pesel;

    private function __construct(string $pesel)
    {
        if (!$this->isValidPesel($pesel)) {
            throw new \InvalidArgumentException('The provided PESEL number is invalid.');
        }
        $this->pesel = $pesel;
    }

    public static function create(string $pesel): self
    {
        return new self($pesel);
    }

    public function pesel(): string
    {
        return $this->pesel;
    }

    // Validation and helper methods...
}
```
