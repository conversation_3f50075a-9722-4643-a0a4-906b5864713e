---
description: Project architecture conventions
globs: ["src/**/*.php"]
alwaysApply: false
---

# Architecture Conventions

## Hexagonal Architecture

- The project follows a hexagonal architecture (ports and adapters) with Domain-Driven Design (DDD) principles
- Maintain clear separation between domain, application, infrastructure, and UI layers
- Domain layer must remain independent of implementation details
- Communication between layers should happen through interfaces (ports)

## Layer Structure

1. **Domain Layer**:
   - Contains business logic, entities, value objects, and repository interfaces
   - Must not depend on other layers or frameworks
   - Defines ports (interfaces) for communication with infrastructure

2. **Application Layer**:
   - Coordinates data flow between domain and external world
   - Implements use cases through application services
   - Contains facades as entry points to module functionality
   - Uses CQRS pattern to separate read and write operations

3. **Infrastructure Layer**:
   - Implements interfaces defined in domain layer
   - Contains repository implementations, ORM mappings
   - Handles database communication and external services

4. **UI Layer / Backend For Frontend**:
   - Handles HTTP requests and responses
   - Contains controllers, DTOs, and validators
   - Communicates with application layer through facades

## Directory Structure

- Organize code by business contexts (bounded contexts), not technical layers
- Each bounded context should have its own directory with Domain, Application, and Infrastructure subdirectories
- Use SharedKernel for elements shared between contexts
- Avoid direct dependencies between domains of different contexts

## Data Flow

1. HTTP request arrives at controller in BackendForFrontend layer
2. Controller validates input data and maps to DTOs
3. Controller calls facade method from application layer
4. Facade delegates to application service
5. Application service uses repositories and entities from domain layer
6. Entities perform business logic and return result
7. Result is passed back through layers and returned as HTTP response
