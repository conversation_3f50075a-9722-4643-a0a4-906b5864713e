---
description: Error handling and logging conventions
globs: ["src/**/Domain/Exception/*.php", "src/**/Application/Exception/*.php"]
alwaysApply: false
---

# Error Handling

## Exceptions

- Each module should define its own domain exceptions
- Domain exceptions should inherit from a base class for the given module
- Exceptions should contain factory methods with descriptive names (e.g., `becausePatientNotFound`)

### Exception Example

```php
class PatientNotFoundException extends \RuntimeException
{
    private function __construct(string $message)
    {
        parent::__construct($message, 404);
    }

    public static function withId(string $patientId): self
    {
        return new self(sprintf('Patient with ID "%s" not found.', $patientId));
    }

    public static function withPesel(string $pesel): self
    {
        return new self(sprintf('Patient with PESEL "%s" not found.', $pesel));
    }
}
```

## Exception Hierarchy

- Base exceptions for each module (e.g., `PatientException`)
- Specific exceptions inheriting from the base exception (e.g., `PatientNotFoundException`, `PatientCannotBeCreatedException`)
- Runtime exceptions for unexpected errors
- Domain exceptions for business rule violations

## Exception Handling

- Controllers should handle exceptions and return appropriate HTTP responses
- The `ApiExceptionListener` catches unhandled exceptions and formats them as JSON responses
- Domain exceptions should be translated to appropriate HTTP status codes

## Logging

- Use Symfony Logger to log errors and information
- Critical errors should be logged with the `critical` level
- Application errors should be logged with the `error` level
- Diagnostic information should be logged with the `info` or `debug` level

### Logging Example

```php
try {
    $this->patientService->create($patientId, $dto);
} catch (PatientCannotBeCreatedException $e) {
    $this->logger->error('Failed to create patient', [
        'patientId' => $patientId,
        'error' => $e->getMessage(),
    ]);
    throw $e;
}
```

## Error Response Format

API error responses should follow a consistent format:

```json
{
    "error": {
        "code": 404,
        "message": "Patient with ID \"123e4567-e89b-12d3-a456-426614174000\" not found."
    }
}
```
