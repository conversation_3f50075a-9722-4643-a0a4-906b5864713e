---
description: Testing practices and conventions
globs: ["tests/**/*.php"]
alwaysApply: false
---

# Testing Practices

## Test Structure

Tests should be organized according to the following schema:

```bash
tests/
├── Account/              # Tests for the Account module
│   ├── Unit/             # Unit tests
│   ├── Integration/      # Integration tests
│   └── Http/             # API tests
├── Auth/                 # Tests for the Auth module
├── Hospital/             # Tests for the Hospital module
└── Patient/              # Tests for the Patient module
```

## Test Types

- **Unit Tests**: Test individual classes in isolation
- **Integration Tests**: Test cooperation between components
- **HTTP Tests**: Test the API from a client perspective

## Test Conventions

- Test class names should end with `Test` (e.g., `PatientTest`)
- Test method names should start with `test` (e.g., `testCreatePatient`)
- Use assertions from static methods `self::assert` (e.g., `self::assertEquals`)
- Use factory methods to create test objects

## Test Examples

### Unit Test

```php
class PeselTest extends TestCase
{
    public function testCreate(): void
    {
        $pesel = Pesel::create('***********');
        self::assertInstanceOf(Pesel::class, $pesel);
    }

    /** @dataProvider validPeselProvider */
    public function testValidPesels(string $peselNumber): void
    {
        $pesel = Pesel::create($peselNumber);
        self::assertInstanceOf(Pesel::class, $pesel);
    }

    /** @dataProvider invalidPeselProvider */
    public function testInvalidPesels(string $peselNumber): void
    {
        $this->expectException(\InvalidArgumentException::class);
        Pesel::create($peselNumber);
    }

    /** @return array<array<string>> */
    public static function validPeselProvider(): array
    {
        return [
            ['***********'],
            ['***********'],
            ['***********'],
        ];
    }
}
```

### Integration Test

```php
class PatientFacadeTest extends KernelTestCase
{
    private PatientFacade $facade;

    protected function setUp(): void
    {
        self::bootKernel();
        $this->facade = self::getContainer()->get(PatientFacade::class);
    }

    public function testCreatePatient(): void
    {
        // Prepare test data
        $patientId = Uuid::generate()->toString();
        $dto = new CreatePatientDto();
        $dto->firstName = 'John';
        $dto->lastName = 'Doe';
        // ... other fields

        // Execute test
        $this->facade->create($patientId, $dto);

        // Verify result
        $patientView = $this->facade->findByPatientId($patientId);
        self::assertNotNull($patientView);
        self::assertEquals('John', $patientView->firstName());
        self::assertEquals('Doe', $patientView->lastName());
    }
}
```

## Running Tests

Tests can be run using the following commands:

- All tests: `composer tests`
- Unit tests: `composer tests:unit`
- Integration tests: `composer tests:integration`
- HTTP tests: `composer tests:http`
