---
description: Database structure and ORM mapping conventions
globs: ["src/**/Infrastructure/Doctrine/**/*.xml", "migrations/*.php"]
alwaysApply: false
---

# Database Structure

## Database Naming Conventions

- Tables are named according to the convention `[context]_[entity]` (e.g., `patient_patient`, `hospital_hospital`)
- Foreign keys are named according to the convention `[table_name]_[column_name]_fk`
- Indexes are named according to the convention `[table_name]_[column_name]_idx`
- Unique constraints are named according to the convention `[table_name]_[column_name]_uniq`

## ORM Mapping

- Use Doctrine ORM with XML mapping
- Mapping files should be located in the `Infrastructure/Doctrine/config/mapping` directory
- Each entity should have its own mapping file
- Use custom types for UUID and other complex types

### XML Mapping Example

```xml
<?xml version="1.0" encoding="UTF-8"?>
<doctrine-mapping xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping
                  http://doctrine-project.org/schemas/orm/doctrine-mapping.xsd">

    <entity name="App\Hospital\Domain\Hospital" table="hospital_hospital">
        <id name="hospitalId" type="uuid_symfony">
            <generator strategy="NONE"/>
        </id>

        <field name="shortName" length="20" unique="true"/>
        <field name="fullName" length="255"/>
        <field name="streetWithNumber" length="100"/>
        <field name="postCode" length="6"/>
        <field name="city" length="40"/>
        <field name="isActive" type="boolean"/>
        <field name="deactivationReason" length="1000" nullable="true"/>
        <field name="createdAt" type="datetime_immutable"/>
        <field name="updatedAt" type="datetime_immutable" nullable="true"/>
        <field name="version" type="integer" version="true"/>
    </entity>

</doctrine-mapping>
```

## Entity Relationships

- Relationships should be defined in XML mapping files
- Prefer unidirectional relationships when possible
- For one-to-many relationships, use collections from Doctrine\Common\Collections
- For many-to-many relationships, use JSON arrays when possible (e.g., for simple identifiers)

### One-to-Many Relationship Example

```xml
<one-to-many field="protocols" target-entity="App\Patient\Domain\PatientProtocol" mapped-by="patient" fetch="EAGER"
             orphan-removal="true">
    <cascade>
        <cascade-persist/>
        <cascade-remove/>
    </cascade>
</one-to-many>
```

## Migrations

- Migrations should be managed manually by the development team
- Do not use automatic migration generation
- Migrations should be located in the `migrations` directory
- To recreate the database from scratch, use the `bin/rebuild-db.sh` script

## Database Schema Documentation

- The database schema is continuously updated and documented in `.cursor/rules/schemat_bazy_danych.sql`
- This file serves as a reference for the current state of the database
- When making changes to the database structure, update this file accordingly
- The schema documentation includes tables, columns, relationships, and indexes
- Use comments to explain the purpose of tables and columns

### Schema Update Process

1. When creating a new entity or modifying an existing one, update the XML mapping files
2. Create a migration script to apply the changes to the database
3. Update the `.cursor/rules/schemat_bazy_danych.sql` file to reflect the new schema
4. Include comments in the schema file to explain the purpose of the changes

### Schema Documentation Example

```sql
-- Patient table stores basic patient information
CREATE TABLE patient_patient (
    id BINARY(16) NOT NULL COMMENT 'UUID primary key',
    public_id VARCHAR(20) NOT NULL COMMENT 'Human-readable patient identifier',
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    pesel VARCHAR(11) DEFAULT NULL COMMENT 'Polish national identification number',
    birth_date DATE NOT NULL,
    gender VARCHAR(1) NOT NULL COMMENT 'M - male, F - female',
    created_at DATETIME NOT NULL COMMENT 'Record creation timestamp',
    updated_at DATETIME DEFAULT NULL COMMENT 'Last update timestamp',
    version INT NOT NULL COMMENT 'Optimistic locking version',
    PRIMARY KEY (id),
    UNIQUE INDEX patient_patient_public_id_uniq (public_id),
    UNIQUE INDEX patient_patient_pesel_uniq (pesel)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```
