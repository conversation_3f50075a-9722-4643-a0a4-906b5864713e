---
description: Code organization conventions
globs: ["src/**/*.php"]
alwaysApply: false
---

# Code Organization Conventions

## Module Structure

Each business module (bounded context) should be organized according to this schema:

1. **Domain** - contains:
   - Entities and value objects
   - Repository interfaces
   - Domain exceptions
   - Domain services

2. **Application** - contains:
   - Facades - entry points to the module
   - Application services
   - Command handlers
   - Query interfaces
   - Data Transfer Objects (DTOs)

3. **Infrastructure** - contains:
   - Repository implementations
   - Query implementations
   - ORM configurations (Doctrine mappings)
   - External adapters

4. **BackendForFrontend** - contains:
   - REST API controllers
   - DTOs for API
   - Validators
   - Access control mechanisms (Voters)

## Responsibility Division

- **Controllers**: Handle HTTP requests, validate input data, and pass them to facades
- **Facades**: Coordinate data flow between controllers and business logic
- **Application Services**: Implement use cases, coordinate the work of entities and repositories
- **Repositories**: Responsible for data access and persistence
- **Entities**: Contain business logic and validation rules

## Implementation Examples

### Facades

```php
class PatientFacade
{
    public function __construct(
        private PatientService $patientService,
        private PeselService $peselService,
        private PatientQueryInterface $patientQuery
    ) {}

    public function create(string $patientId, CreatePatientDto $dto): void
    {
        $this->patientService->create($patientId, $dto);
    }

    public function update(string $patientId, UpdatePatientDto $dto): void
    {
        $this->patientService->update($patientId, $dto);
    }

    public function findByPatientId(string $patientId): ?PatientView
    {
        return $this->patientQuery->findByPatientId($patientId);
    }
}
```

### Application Services

```php
class PatientService
{
    public function __construct(
        private PatientRepositoryInterface $patientRepository,
        private PatientPublicIdGenerator $patientPublicIdGenerator,
        private HospitalQueryInterface $hospitalQuery
    ) {}

    public function create(string $patientId, CreatePatientDto $dto): void
    {
        $patientPublicId = $this->patientPublicIdGenerator->generate($dto->firstName, $dto->lastName);
        
        $patient = Patient::create(
            Uuid::fromString($patientId),
            $patientPublicId,
            $dto->firstName,
            $dto->lastName,
            // ... other parameters
        );

        $this->patientRepository->add($patient);
    }
}
```

### Controllers

```php
#[AsController]
#[Route(path: '/patient', name: 'patient_')]
class PatientApiController extends AbstractController
{
    public function __construct(
        private PatientFacade $patientFacade,
        private UserSecurity $userSecurity
    ) {}

    #[Route(path: '', name: 'create', methods: ['POST'])]
    public function create(CreatePatientDto $dto): JsonResponse
    {
        $patientId = Uuid::generate()->toString();
        $this->patientFacade->create($patientId, $dto);

        return new JsonResponse(['patientId' => $patientId], Response::HTTP_CREATED);
    }
}
```
