<?php

declare(strict_types=1);

namespace App\Hospital\Domain;

use App\Common\Attribute\TrackEntityChange;
use App\Common\Uuid;
use Symfony\Component\Clock;

#[TrackEntityChange(objectLogDetailsFunction: 'logDetails')]
class Hospital
{
    private \DateTimeImmutable $createdAt;
    private ?\DateTimeImmutable $updatedAt = null;
    private int $version;

    private function __construct(
        private Uuid $hospitalId,
        private string $shortName,
        private string $fullName,
        private string $streetWithNumber,
        private string $postCode,
        private string $city,
        private bool $isActive,
        private ?string $deactivationReason = null,
    ) {
        $this->createdAt = Clock\now();
    }

    public static function create(
        Uuid $hospitalId,
        string $shortName,
        string $fullName,
        string $streetWithNumber,
        string $postCode,
        string $city,
    ): self {
        return new self($hospitalId, $shortName, $fullName, $streetWithNumber,
            $postCode, $city, true);
    }

    public function update(
        string $shortName,
        string $fullName,
        string $streetWithNumber,
        string $postCode,
        string $city,
    ): void {
        $this->shortName = $shortName;
        $this->fullName = $fullName;
        $this->streetWithNumber = $streetWithNumber;
        $this->postCode = $postCode;
        $this->city = $city;
        $this->updatedAt = Clock\now();
    }

    public function activate(): void
    {
        $this->isActive = true;
        $this->deactivationReason = null;
        $this->updatedAt = Clock\now();
    }

    public function deactivate(string $reason): void
    {
        $this->isActive = false;
        $this->deactivationReason = $reason;
        $this->updatedAt = Clock\now();
    }

    public function hospitalId(): Uuid
    {
        return $this->hospitalId;
    }

    /** @return array{shortName: string, fullName: string} */
    public function logDetails(): array
    {
        return [
            'shortName' => $this->shortName,
            'fullName' => $this->fullName,
        ];
    }

    public function shortName(): string
    {
        return $this->shortName;
    }

    public function isActive(): bool
    {
        return $this->isActive;
    }
}
