<?xml version="1.0" encoding="UTF-8"?>
<doctrine-mapping xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping
                  http://doctrine-project.org/schemas/orm/doctrine-mapping.xsd">

    <entity name="App\Hospital\Domain\Hospital" table="hospital_hospital">
        <id name="hospitalId" type="uuid_symfony">
            <generator strategy="NONE"/>
        </id>

        <field name="shortName" length="20" unique="true"/>
        <field name="fullName" length="255"/>
        <field name="streetWithNumber" length="100"/>
        <field name="postCode" length="6"/>
        <field name="city" length="40"/>
        <field name="isActive" type="boolean"/>
        <field name="deactivationReason" length="1000" nullable="true"/>
        <field name="createdAt" type="datetime_immutable"/>
        <field name="updatedAt" type="datetime_immutable" nullable="true"/>
        <field name="version" type="integer" version="true"/>
    </entity>

</doctrine-mapping>
