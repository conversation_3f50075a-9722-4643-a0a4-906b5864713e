<?php

declare(strict_types=1);

namespace App\Hospital\Infrastructure\Persistence;

use App\Common\Uuid;
use App\Hospital\Domain\Hospital;
use App\Hospital\Domain\HospitalRepositoryInterface;
use App\Hospital\Infrastructure\Exception\HospitalNotExist;
use Doctrine\ORM\EntityManagerInterface;

readonly class DoctrineHospitalRepository implements HospitalRepositoryInterface
{
    public function __construct(
        private EntityManagerInterface $em,
    ) {
    }

    public function add(Hospital $hospital): void
    {
        $this->em->persist($hospital);
        $this->em->flush();
    }

    public function update(Hospital $hospital): void
    {
        $this->em->flush();
    }

    public function getByHospitalId(Uuid $hospitalId): Hospital
    {
        $hospital = $this->em->getRepository(Hospital::class)->findOneBy([
            'hospitalId' => $hospitalId->valueString(),
        ]);

        if (!$hospital) {
            throw HospitalNotExist::becauseNotExistInDatabase($hospitalId->valueString());
        }

        return $hospital;
    }

    public function findByShortName(string $shortName, ?Uuid $excludeHospitalId = null): ?Hospital
    {
        $queryBuilder = $this->em->createQueryBuilder()
            ->select('h')
            ->from(Hospital::class, 'h')
            ->where('h.shortName = :shortName')
            ->setParameter('shortName', $shortName);

        if ($excludeHospitalId) {
            $queryBuilder->andWhere('h.hospitalId != :hospitalId')
                ->setParameter('hospitalId', $excludeHospitalId->valueString());
        }

        return $queryBuilder->getQuery()->getOneOrNullResult();
    }
}
