<?php

declare(strict_types=1);

namespace App\Hospital\Infrastructure\Query;

use App\Hospital\Application\Query\PatientQueryInterface;
use App\Patient\Application\PatientHospitalFacade;

readonly class PatientModulePatientQuery implements PatientQueryInterface
{
    public function __construct(private PatientHospitalFacade $patientHospitalFacade)
    {
    }

    public function hospitalIsUsedInPatient(string $hospitalId): bool
    {
        return $this->patientHospitalFacade->hospitalIsUsed($hospitalId);
    }
}
