<?php

declare(strict_types=1);

namespace App\Hospital\Infrastructure\Query;

use App\Common\Query\GridConfiguration;
use App\Common\Query\GridResult;
use App\Hospital\Application\Query\HospitalQueryInterface;
use App\Hospital\Application\Query\HospitalSelectView;
use App\Hospital\Application\Query\HospitalView;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Query\QueryBuilder;

readonly class DbalHospitalQuery implements HospitalQueryInterface
{
    private const string TABLE_NAME = 'hospital_hospital';

    public function __construct(
        private Connection $connection,
    ) {
    }

    public function findByHospitalId(string $hospitalId): ?HospitalView
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('hospitalId', 'shortName', 'fullName', 'streetWithNumber', 'postCode', 'city', 'isActive', 'deactivationReason', 'createdAt', 'updatedAt')
            ->from(self::TABLE_NAME)
            ->where('hospitalId = :hospitalId')
            ->setParameter('hospitalId', $hospitalId)
        ;

        $data = $this->connection->fetchAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        if (false === $data) {
            return null;
        }

        /* @var array{hospitalId: string, shortName: string, fullName: string, streetWithNumber: string, postCode: string, city: string, isActive: bool|int|string, deactivationReason: ?string, createdAt: string, updatedAt: ?string} $data */
        return HospitalView::deserialize([
            'hospitalId' => (string) $data['hospitalId'],
            'shortName' => (string) $data['shortName'],
            'fullName' => (string) $data['fullName'],
            'streetWithNumber' => (string) $data['streetWithNumber'],
            'postCode' => (string) $data['postCode'],
            'city' => (string) $data['city'],
            'isActive' => $data['isActive'],
            'deactivationReason' => isset($data['deactivationReason']) ? (string) $data['deactivationReason'] : null,
            'createdAt' => $data['createdAt'],
            'updatedAt' => $data['updatedAt'],
        ]);
    }

    /** @return array<HospitalView> */
    public function findAll(?\DateTimeImmutable $changedSince = null): array
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('hospitalId', 'shortName', 'fullName', 'streetWithNumber', 'postCode', 'city', 'isActive', 'deactivationReason', 'createdAt', 'updatedAt')
            ->from(self::TABLE_NAME);

        if ($changedSince !== null) {
            $queryBuilder
                ->andWhere('createdAt >= :changedSince OR updatedAt >= :changedSince')
                ->setParameter('changedSince', $changedSince->format('Y-m-d H:i:s'));
        }

        $data = $this->connection->fetchAllAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        $hospitals = [];
        foreach ($data as $row) {
            /* @var array{hospitalId: string, shortName: string, fullName: string, streetWithNumber: string, postCode: string, city: string, isActive: bool|int|string, deactivationReason: ?string, createdAt: string, updatedAt: ?string} $row */
            $hospitals[] = HospitalView::deserialize([
                'hospitalId' => (string) $row['hospitalId'],
                'shortName' => (string) $row['shortName'],
                'fullName' => (string) $row['fullName'],
                'streetWithNumber' => (string) $row['streetWithNumber'],
                'postCode' => (string) $row['postCode'],
                'city' => (string) $row['city'],
                'isActive' => $row['isActive'],
                'deactivationReason' => isset($row['deactivationReason']) ? (string) $row['deactivationReason'] : null,
                'createdAt' => $row['createdAt'],
                'updatedAt' => $row['updatedAt'],
            ]);
        }

        return $hospitals;
    }

    /**
     * @param array<int, string> $hospitalIds
     *
     * @return Collection<string, HospitalSelectView>
     */
    public function findAllActiveHospitalSelect(array $hospitalIds = []): Collection
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('hospitalId', 'shortName', 'fullName')
            ->from(self::TABLE_NAME)
            ->where('isActive = :isActive')
            ->setParameter('isActive', true)
            ->orderBy('fullName', 'ASC')
        ;

        if (count($hospitalIds) > 0) {
            $hospitalIds = array_map([$this->connection, 'quote'], $hospitalIds);
            $queryBuilder
                ->andWhere($queryBuilder->expr()->in('hospitalId', $hospitalIds));
        }

        $data = $this->connection->fetchAllAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        $collection = new ArrayCollection();

        foreach ($data as $key => $row) {
            /* @var array{hospitalId: string, shortName: string, fullName: string} $row */
            $collection->set($row['hospitalId'], HospitalSelectView::deserialize([
                'hospitalId' => (string) $row['hospitalId'],
                'shortName' => (string) $row['shortName'],
                'fullName' => (string) $row['fullName'],
            ]));
        }

        return $collection;
    }

    /** @return GridResult<string, HospitalView> */
    public function findForGrid(GridConfiguration $gridConfiguration): GridResult
    {
        $baseQueryBuilder = $this->prepareQueryForGrid($gridConfiguration);

        // Count total items for pagination
        $queryBuilderTotalItems = clone $baseQueryBuilder;
        $queryBuilderTotalItems->resetOrderBy()
            ->select('COUNT(DISTINCT hospitalId)');
        $totalItems = (int) $this->connection->fetchOne($queryBuilderTotalItems->getSQL(), $queryBuilderTotalItems->getParameters());

        // Add pagination
        $queryBuilder = clone $baseQueryBuilder;
        $queryBuilder->setFirstResult(($gridConfiguration->page() - 1) * $gridConfiguration->itemsPerPage())
            ->setMaxResults($gridConfiguration->itemsPerPage());

        $data = $this->connection->fetchAllAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        $collection = new ArrayCollection();

        foreach ($data as $key => $row) {
            /* @var array{hospitalId: string, shortName: string, fullName: string, streetWithNumber: string, postCode: string, city: string, isActive: bool|int|string, deactivationReason: ?string, createdAt: string, updatedAt: ?string} $row */
            $collection->set($row['hospitalId'], HospitalView::deserialize([
                'hospitalId' => (string) $row['hospitalId'],
                'shortName' => (string) $row['shortName'],
                'fullName' => (string) $row['fullName'],
                'streetWithNumber' => (string) $row['streetWithNumber'],
                'postCode' => (string) $row['postCode'],
                'city' => (string) $row['city'],
                'isActive' => $row['isActive'],
                'deactivationReason' => isset($row['deactivationReason']) ? (string) $row['deactivationReason'] : null,
                'createdAt' => $row['createdAt'],
                'updatedAt' => $row['updatedAt'],
            ]));
        }

        return new GridResult($collection,
            $totalItems,
            $gridConfiguration->page(),
            $gridConfiguration->itemsPerPage()
        );
    }

    /** @return string[] */
    public function findAllActiveHospitalIds(): array
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('hospitalId')
            ->from(self::TABLE_NAME)
            ->where('isActive = :isActive')
            ->setParameter('isActive', true);

        return $this->connection->fetchFirstColumn($queryBuilder->getSQL(), $queryBuilder->getParameters());
    }

    private function prepareQueryForGrid(GridConfiguration $gridConfiguration): QueryBuilder
    {
        $queryBuilder = $this->connection->createQueryBuilder();

        $queryBuilder
            ->select('hospitalId', 'shortName', 'fullName', 'streetWithNumber', 'postCode', 'city', 'isActive', 'deactivationReason', 'createdAt', 'updatedAt')
            ->from(self::TABLE_NAME, 'h');

        $gridConfiguration->addSortAndFilterToDbalQueryBuilder($queryBuilder);

        if (count($gridConfiguration->sorts()) === 0) {
            $queryBuilder->addOrderBy('h.createdAt', 'ASC');
        }

        return $queryBuilder;
    }
}
