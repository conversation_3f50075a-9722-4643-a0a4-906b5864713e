<?php

declare(strict_types=1);

namespace App\Hospital\Infrastructure\Query;

use App\Account\Application\AccountHospitalFacade;
use App\Hospital\Application\Query\AccountQueryInterface;

readonly class AccountModuleAccountQuery implements AccountQueryInterface
{
    public function __construct(private AccountHospitalFacade $accountHospitalFacade)
    {
    }

    public function hospitalIsUsedInAccount(string $hospitalId): bool
    {
        return $this->accountHospitalFacade->hospitalIsUsed($hospitalId);
    }
}
