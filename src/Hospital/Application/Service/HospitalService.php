<?php

declare(strict_types=1);

namespace App\Hospital\Application\Service;

use App\Common\Uuid;
use App\Hospital\Application\Exception\HospitalCannotBeActivated;
use App\Hospital\Application\Exception\HospitalCannotBeCreated;
use App\Hospital\Application\Exception\HospitalCannotBeDeactivated;
use App\Hospital\Application\Query\AccountQueryInterface;
use App\Hospital\Application\Query\PatientQueryInterface;
use App\Hospital\Domain\Hospital;
use App\Hospital\Domain\HospitalRepositoryInterface;

readonly class HospitalService
{
    public function __construct(
        private HospitalRepositoryInterface $hospitalRepository,
        private AccountQueryInterface $accountQuery,
        private PatientQueryInterface $patientQuery,
    ) {
    }

    public function create(string $hospitalId, string $shortName, string $fullName, string $streetWithNumber,
        string $postCode, string $city): void
    {
        if ($this->shortNameAlreadyExist($shortName)) {
            throw HospitalCannotBeCreated::becauseShortNameAlreadyExist();
        }

        $hospital = Hospital::create(
            Uuid::fromString($hospitalId),
            $shortName,
            $fullName,
            $streetWithNumber,
            $postCode,
            $city,
        );

        $this->hospitalRepository->add($hospital);
    }

    public function update(string $hospitalId, string $shortName, string $fullName, string $streetWithNumber,
        string $postCode, string $city): void
    {
        $hospital = $this->hospitalRepository->getByHospitalId(Uuid::fromString($hospitalId));

        if ($this->shortNameAlreadyExist($shortName, $hospital->hospitalId())) {
            throw HospitalCannotBeCreated::becauseShortNameAlreadyExist();
        }

        $hospital->update(
            $shortName,
            $fullName,
            $streetWithNumber,
            $postCode,
            $city,
        );

        $this->hospitalRepository->update($hospital);
    }

    public function activate(string $hospitalId): void
    {
        $hospital = $this->hospitalRepository->getByHospitalId(Uuid::fromString($hospitalId));

        if ($hospital->isActive()) {
            throw HospitalCannotBeActivated::becauseIsAlreadyActive();
        }

        $hospital->activate();
        $this->hospitalRepository->update($hospital);
    }

    public function deactivate(string $hospitalId, string $reason): void
    {
        $hospital = $this->hospitalRepository->getByHospitalId(Uuid::fromString($hospitalId));

        if (!$hospital->isActive()) {
            throw HospitalCannotBeDeactivated::becauseIsAlreadyInactive();
        }

        if ($this->accountQuery->hospitalIsUsedInAccount($hospitalId)) {
            throw HospitalCannotBeDeactivated::becauseHospitalIsUsedInAccount();
        }

        if ($this->patientQuery->hospitalIsUsedInPatient($hospitalId)) {
            throw HospitalCannotBeDeactivated::becauseHospitalIsUsedInPatient();
        }

        $hospital->deactivate($reason);
        $this->hospitalRepository->update($hospital);
    }

    private function shortNameAlreadyExist(string $shortName, ?Uuid $excludeHospitalId = null): bool
    {
        return $this->hospitalRepository->findByShortName($shortName, $excludeHospitalId) !== null;
    }
}
