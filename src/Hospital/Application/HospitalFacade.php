<?php

declare(strict_types=1);

namespace App\Hospital\Application;

use App\Common\Query\GridConfiguration;
use App\Common\Query\GridResult;
use App\Hospital\Application\Query\HospitalQueryInterface;
use App\Hospital\Application\Query\HospitalSelectView;
use App\Hospital\Application\Query\HospitalView;
use App\Hospital\Application\Service\HospitalService;
use Doctrine\Common\Collections\Collection;

readonly class HospitalFacade
{
    public function __construct(
        private HospitalService $hospitalService,
        private HospitalQueryInterface $hospitalQuery,
    ) {
    }

    public function create(string $hospitalId, string $shortName, string $fullName, string $streetWithNumber, string $postCode, string $city): void
    {
        $this->hospitalService->create($hospitalId, $shortName, $fullName, $streetWithNumber, $postCode, $city);
    }

    public function update(string $hospitalId, string $shortName, string $fullName, string $streetWithNumber, string $postCode, string $city): void
    {
        $this->hospitalService->update($hospitalId, $shortName, $fullName, $streetWithNumber, $postCode, $city);
    }

    public function activate(string $hospitalId): void
    {
        $this->hospitalService->activate($hospitalId);
    }

    public function deactivate(string $hospitalId, string $reason): void
    {
        $this->hospitalService->deactivate($hospitalId, $reason);
    }

    public function findByHospitalId(string $hospitalId): ?HospitalView
    {
        return $this->hospitalQuery->findByHospitalId($hospitalId);
    }

    /** @return array<HospitalView> */
    public function findAll(?\DateTimeImmutable $changedSince = null): array
    {
        return $this->hospitalQuery->findAll($changedSince);
    }

    /**
     * @param array<string> $hospitalIds
     *
     * @return Collection<string, HospitalSelectView>
     */
    public function findAllActiveHospitalSelect(array $hospitalIds = []): Collection
    {
        return $this->hospitalQuery->findAllActiveHospitalSelect($hospitalIds);
    }

    /** @return array<string> */
    public function findAllActiveHospitalIds(): array
    {
        return $this->hospitalQuery->findAllActiveHospitalIds();
    }

    /** @return GridResult<string, HospitalView> */
    public function findForGrid(GridConfiguration $gridConfiguration): GridResult
    {
        return $this->hospitalQuery->findForGrid($gridConfiguration);
    }
}
