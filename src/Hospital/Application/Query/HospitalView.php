<?php

declare(strict_types=1);

namespace App\Hospital\Application\Query;

use App\Common\QueryView;

readonly class HospitalView implements QueryView
{
    public function __construct(
        private string $hospitalId,
        private string $shortName,
        private string $fullName,
        private string $streetWithNumber,
        private string $postCode,
        private string $city,
        private bool $isActive,
        private ?string $deactivationReason,
        private \DateTimeImmutable $createdAt,
        private ?\DateTimeImmutable $updatedAt,
    ) {
    }

    /** @param array{hospitalId: string, shortName: string, fullName: string, streetWithNumber: string, postCode: string, city: string, isActive: bool|int|string, deactivationReason: ?string, createdAt: string, updatedAt: ?string} $data */
    public static function deserialize(array $data): self
    {
        return new self(
            $data['hospitalId'],
            $data['shortName'],
            $data['fullName'],
            $data['streetWithNumber'],
            $data['postCode'],
            $data['city'],
            (bool) $data['isActive'],
            $data['deactivationReason'],
            new \DateTimeImmutable($data['createdAt']),
            isset($data['updatedAt']) ? new \DateTimeImmutable($data['updatedAt']) : null,
        );
    }

    public function jsonSerialize(): mixed
    {
        return [
            'hospitalId' => $this->hospitalId,
            'shortName' => $this->shortName,
            'fullName' => $this->fullName,
            'streetWithNumber' => $this->streetWithNumber,
            'postCode' => $this->postCode,
            'city' => $this->city,
            'isActive' => $this->isActive,
            'deactivationReason' => $this->deactivationReason,
            'createdAt' => $this->createdAt->format('Y-m-d H:i:s'),
            'updatedAt' => $this->updatedAt?->format('Y-m-d H:i:s'),
        ];
    }

    public function hospitalId(): string
    {
        return $this->hospitalId;
    }

    public function shortName(): string
    {
        return $this->shortName;
    }

    public function fullName(): string
    {
        return $this->fullName;
    }

    public function streetWithNumber(): string
    {
        return $this->streetWithNumber;
    }

    public function postCode(): string
    {
        return $this->postCode;
    }

    public function city(): string
    {
        return $this->city;
    }

    public function isActive(): bool
    {
        return $this->isActive;
    }

    public function deactivationReason(): ?string
    {
        return $this->deactivationReason;
    }

    public function createdAt(): \DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function updatedAt(): ?\DateTimeImmutable
    {
        return $this->updatedAt;
    }
}
