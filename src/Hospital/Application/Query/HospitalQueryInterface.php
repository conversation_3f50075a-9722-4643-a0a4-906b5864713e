<?php

declare(strict_types=1);

namespace App\Hospital\Application\Query;

use App\Common\Query\GridConfiguration;
use App\Common\Query\GridResult;
use Doctrine\Common\Collections\Collection;

interface HospitalQueryInterface
{
    public function findByHospitalId(string $hospitalId): ?HospitalView;

    /** @return array<HospitalView> */
    public function findAll(?\DateTimeImmutable $changedSince = null): array;

    /**
     * @param array<string> $hospitalIds
     *
     * @return Collection<string, HospitalSelectView>
     */
    public function findAllActiveHospitalSelect(array $hospitalIds = []): Collection;

    /** @return string[] */
    public function findAllActiveHospitalIds(): array;

    /** @return GridResult<string, HospitalView> */
    public function findForGrid(GridConfiguration $gridConfiguration): GridResult;
}
