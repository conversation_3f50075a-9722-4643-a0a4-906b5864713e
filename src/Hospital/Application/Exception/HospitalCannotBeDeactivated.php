<?php

declare(strict_types=1);

namespace App\Hospital\Application\Exception;

use App\Common\Exception\ApplicationException;

class HospitalCannotBeDeactivated extends ApplicationException
{
    public static function becauseIsAlreadyInactive(): self
    {
        return new self('Szpital nie może zost<PERSON>tywowany, poni<PERSON><PERSON><PERSON> jest już nieaktywny.', 422);
    }

    public static function becauseHospitalIsUsedInAccount(): self
    {
        return new self('Szpital nie może zost<PERSON>wany, ponieważ jest przypisany do konta użytkownika.', 422);
    }

    public static function becauseHospitalIsUsedInPatient(): self
    {
        return new self('Szpital nie może zosta<PERSON> dezaktywowany, ponieważ jest przypisany do pacjenta.', 422);
    }
}
