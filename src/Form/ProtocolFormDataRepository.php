<?php

declare(strict_types=1);

namespace App\Form;

use App\Form\View\ProtocolFormDataView;
use Doctrine\ORM\EntityManagerInterface;

class ProtocolFormDataRepository
{
    public const string TABLE_NAME = 'form_protocol_form_data';

    public function __construct(
        private EntityManagerInterface $em)
    {
    }

    public function add(ProtocolFormData $formData): void
    {
        $this->em->persist($formData);
    }

    public function update(ProtocolFormData $formData): void
    {
        $this->em->flush();
    }

    public function findByPatientProtocolIdAndFormId(string $patientProtocolId, string $formId): ?ProtocolFormData
    {
        $qb = $this->em->createQueryBuilder();

        $qb->select('p')
            ->from(ProtocolFormData::class, 'p')
            ->where('p.patientProtocolId = :patientProtocolId')
            ->andWhere('p.formId = :formId')
            ->setParameter('formId', $formId)
            ->setParameter('patientProtocolId', $patientProtocolId);

        return $qb->getQuery()->getOneOrNullResult();
    }

    public function findAll(?\DateTimeImmutable $changedSince = null): \Generator
    {
        $conn = $this->em->getConnection();
        $qb = $conn->createQueryBuilder();

        $qb->select('*')
            ->from(self::TABLE_NAME);

        if ($changedSince !== null) {
            $qb->andWhere('createdAt >= :changedSince OR updatedAt >= :changedSince')
                ->setParameter('changedSince', $changedSince->format('Y-m-d H:i:s'));
        }

        $stmt = $qb->executeQuery();

        while ($result = $stmt->fetchAssociative()) {
            yield ProtocolFormDataView::create(
                $result['protocolFormDataId'],
                $result['patientProtocolId'],
                $result['formId'],
                $result['patientId'],
                $result['protocol'],
                $result['data'],
                new \DateTimeImmutable($result['createdAt']),
                isset($result['updatedAt']) ? new \DateTimeImmutable($result['updatedAt']) : null
            );
        }
    }
}
