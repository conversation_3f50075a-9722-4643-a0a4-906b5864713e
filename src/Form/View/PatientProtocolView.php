<?php

declare(strict_types=1);

namespace App\Form\View;

use App\Common\QueryView;

class PatientProtocolView implements QueryView
{
    private string $patientId;
    private string $patientFullName;
    private string $patientProtocolId;
    private string $protocol;
    private ?string $icd10;
    private ?\DateTimeImmutable $dateOfDiagnosis;
    private bool $isActive;

    private function __construct(
        string $patientId,
        string $patientFullName,
        string $patientProtocolId,
        string $protocol,
        ?string $icd10,
        ?\DateTimeImmutable $dateOfDiagnosis,
        bool $isActive,
    ) {
        $this->patientId = $patientId;
        $this->patientFullName = $patientFullName;
        $this->patientProtocolId = $patientProtocolId;
        $this->protocol = $protocol;
        $this->icd10 = $icd10;
        $this->dateOfDiagnosis = $dateOfDiagnosis;
        $this->isActive = $isActive;
    }

    public static function create(string $patientId, string $patientFullName, string $patientProtocolId,
        string $protocol, ?string $icd10, ?\DateTimeImmutable $dateOfDiagnosis, bool $isActive): self
    {
        return new self(
            $patientId,
            $patientFullName,
            $patientProtocolId,
            $protocol,
            $icd10,
            $dateOfDiagnosis,
            $isActive
        );
    }

    /**
     * @param array{
     *     patientId: string,
     *     patientFullName: string,
     *     patientProtocolId: string,
     *     protocol: string,
     *     icd10: ?string,
     *     dateOfDiagnosis: ?\DateTimeImmutable,
     *     isActive: bool
     * } $data
     */
    public static function deserialize(array $data): self
    {
        return new self(
            $data['patientId'],
            $data['patientFullName'],
            $data['patientProtocolId'],
            $data['protocol'],
            $data['icd10'],
            $data['dateOfDiagnosis'],
            (bool) $data['isActive']
        );
    }

    /**
     * @return array{
     *     patientId: string,
     *     patientFullName: string,
     *     patientProtocolId: string,
     *     protocol: string,
     *     icd10: ?string,
     *     dateOfDiagnosis: ?\DateTimeImmutable,
     *     isActive: bool
     * }
     */
    public function jsonSerialize(): array
    {
        return [
            'patientId' => $this->patientId,
            'patientFullName' => $this->patientFullName,
            'patientProtocolId' => $this->patientProtocolId,
            'protocol' => $this->protocol,
            'icd10' => $this->icd10,
            'dateOfDiagnosis' => $this->dateOfDiagnosis,
            'isActive' => $this->isActive,
        ];
    }

    public function patientId(): string
    {
        return $this->patientId;
    }

    public function patientFullName(): string
    {
        return $this->patientFullName;
    }

    public function patientProtocolId(): string
    {
        return $this->patientProtocolId;
    }

    public function protocol(): string
    {
        return $this->protocol;
    }

    public function icd10(): ?string
    {
        return $this->icd10;
    }

    public function dateOfDiagnosis(): ?\DateTimeImmutable
    {
        return $this->dateOfDiagnosis;
    }

    public function isActive(): bool
    {
        return $this->isActive;
    }
}
