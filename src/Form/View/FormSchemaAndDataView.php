<?php

declare(strict_types=1);

namespace App\Form\View;

class FormSchemaAndDataView implements \JsonSerializable
{
    private string $jsonSchema;
    private ?string $formData;

    private function __construct(string $jsonSchema, ?string $formData)
    {
        $this->jsonSchema = $jsonSchema;
        $this->formData = $formData;
    }

    public static function create(string $jsonSchema, ?string $formData): FormSchemaAndDataView
    {
        return new FormSchemaAndDataView($jsonSchema, $formData);
    }

    /** @return array{jsonSchema: string, formData: ?string} */
    public function jsonSerialize(): array
    {
        return [
            'jsonSchema' => $this->jsonSchema,
            'formData' => $this->formData,
        ];
    }
}
