<?php

declare(strict_types=1);

namespace App\Form\View;

use App\Common\QueryView;

class ProtocolFormDataView implements QueryView
{
    private string $protocolFormDataId;
    private string $patientProtocolId;
    private string $formId;
    private string $patientId;
    private string $protocol;
    private string $data;
    private \DateTimeImmutable $createdAt;
    private ?\DateTimeImmutable $updatedAt;

    private function __construct(
        string $protocolFormDataId,
        string $patientProtocolId,
        string $formId,
        string $patientId,
        string $protocol,
        string $data,
        \DateTimeImmutable $createdAt,
        ?\DateTimeImmutable $updatedAt,
    ) {
        $this->protocolFormDataId = $protocolFormDataId;
        $this->patientProtocolId = $patientProtocolId;
        $this->formId = $formId;
        $this->patientId = $patientId;
        $this->protocol = $protocol;
        $this->data = $data;
        $this->createdAt = $createdAt;
        $this->updatedAt = $updatedAt;
    }

    public static function create(
        string $protocolFormDataId,
        string $patientProtocolId,
        string $formId,
        string $patientId,
        string $protocol,
        string $data,
        \DateTimeImmutable $createdAt,
        ?\DateTimeImmutable $updatedAt,
    ): self {
        return new self(
            $protocolFormDataId,
            $patientProtocolId,
            $formId,
            $patientId,
            $protocol,
            $data,
            $createdAt,
            $updatedAt
        );
    }

    /** @param array<string, mixed> $data */
    public static function deserialize(array $data): self
    {
        return new self(
            $data['protocolFormDataId'],
            $data['patientProtocolId'],
            $data['formId'],
            $data['patientId'],
            $data['protocol'],
            $data['data'],
            new \DateTimeImmutable($data['createdAt']),
            isset($data['updatedAt']) ? new \DateTimeImmutable($data['updatedAt']) : null
        );
    }

    /** @return array<string, mixed> */
    public function jsonSerialize(): array
    {
        return [
            'protocolFormDataId' => $this->protocolFormDataId,
            'patientProtocolId' => $this->patientProtocolId,
            'formId' => $this->formId,
            'patientId' => $this->patientId,
            'protocol' => $this->protocol,
            'data' => json_decode($this->data),
            'createdAt' => $this->createdAt->format('Y-m-d H:i:s'),
            'updatedAt' => $this->updatedAt ? $this->updatedAt->format('Y-m-d H:i:s') : null,
        ];
    }

    public function protocolFormDataId(): string
    {
        return $this->protocolFormDataId;
    }

    public function patientProtocolId(): string
    {
        return $this->patientProtocolId;
    }

    public function formId(): string
    {
        return $this->formId;
    }

    public function patientId(): string
    {
        return $this->patientId;
    }

    public function protocol(): string
    {
        return $this->protocol;
    }

    public function data(): string
    {
        return $this->data;
    }

    public function createdAt(): \DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function updatedAt(): ?\DateTimeImmutable
    {
        return $this->updatedAt;
    }
}
