<?php

declare(strict_types=1);

namespace App\Form;

use App\Form\Validator\FieldsSchemaConstraint;
use App\Form\Validator\FormDataConstraint;
use App\SharedKernel\FormFieldsType;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

readonly class ValidationService
{
    public function __construct(
        private ValidatorInterface $validator)
    {
    }

    /**
     * @param array<string, mixed> $data
     * @param array<string, mixed> $schema
     */
    public function tryValidateSchemaAndData(array $data, array $schema, string $patientId): void
    {
        $flattenFieldsSchema = $this->extractFieldSchemaToRoot($schema);

        $fieldsSchemaConstraint = new FieldsSchemaConstraint($flattenFieldsSchema);
        $fieldsSchemaValidatorErrors = $this->validator->validate([], $fieldsSchemaConstraint);

        if ($fieldsSchemaValidatorErrors->count() > 0) {
            $this->throwExceptionErrorSchema($fieldsSchemaValidatorErrors);
        }

        $dataConstraint = ['flattenFieldsSchema' => $flattenFieldsSchema, 'patientId' => $patientId];
        $formDataConstraint = new FormDataConstraint($dataConstraint);
        $formDataValidateErrors = $this->validator->validate($data, $formDataConstraint);

        if ($formDataValidateErrors->count() > 0) {
            $this->throwExceptionErrorFormData($formDataValidateErrors);
        }
    }

    private function throwExceptionErrorSchema(ConstraintViolationListInterface $errors): void
    {
        $arrayErrors = ['message' => 'Schema jest nieprawidłowy.'];
        $arrayErrors['details'] = [];

        /** @var ConstraintViolation $error */
        foreach ($errors as $error) {
            $arrayErrors['details'][$error->getPropertyPath()][] = $error->getMessage();
        }

        throw new \InvalidArgumentException((string) json_encode($arrayErrors, JSON_THROW_ON_ERROR), 422);
    }

    private function throwExceptionErrorFormData(ConstraintViolationListInterface $errors): void
    {
        $arrayErrors = ['message' => 'Przesłane dane są nieprawidłowe.'];
        $arrayErrors['details'] = [];

        /** @var ConstraintViolation $error */
        foreach ($errors as $error) {
            $arrayErrors['details'][$error->getPropertyPath()][] = $error->getMessage();
        }

        throw new \InvalidArgumentException((string) json_encode($arrayErrors, JSON_THROW_ON_ERROR), 422);
    }

    /**
     * @param array<string, mixed> $schema
     *
     * @return array<int, array<string, mixed>>
     */
    private function extractFieldSchemaToRoot(array $schema): array
    {
        $fieldsWithType = [];

        $allowedTypes = FormFieldsType::ALLOW_ROOT;

        $processFields = static function ($fields) use (&$fieldsWithType, &$processFields, $allowedTypes): void {
            foreach ($fields as $field) {
                if (isset($field['type']) && in_array($field['type'], $allowedTypes, true)) {
                    $fieldsWithType[] = $field;
                }
                if (isset($field['children']) && is_array($field['children'])) {
                    $processFields($field['children']);
                }
                if (isset($field['fieldsGroupItems']) && is_array($field['fieldsGroupItems'])) {
                    $processFields($field['fieldsGroupItems']);
                }
                if (isset($field['fieldsGroupRowItems']) && is_array($field['fieldsGroupRowItems'])) {
                    $processFields($field['fieldsGroupRowItems']);
                }
                if (isset($field['fieldsSetItems']) && is_array($field['fieldsSetItems'])) {
                    $processFields($field['fieldsSetItems']);
                }
            }
        };

        if (isset($schema['fields']) && is_array($schema['fields'])) {
            $processFields($schema['fields']);
        } else {
            throw new \InvalidArgumentException('Schema nie zawiera pola "fields" lub nie jest tablicą.', 422);
        }

        return $fieldsWithType;
    }
}
