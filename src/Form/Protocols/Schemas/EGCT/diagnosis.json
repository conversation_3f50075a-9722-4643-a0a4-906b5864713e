{"formTitle": "", "sections": [{"title": "CHOROBY WSPÓŁISTNIEJĄCE", "rootFieldsIds": ["predispositionToCancer", "assessmentOfThePatientsCondition"]}, {"title": "OPIS NOWOTWORU", "rootFieldsIds": ["dateOfDiagnosis", "descriptionAndLocationOfTheTumor", "tumorLocation", "markersAtDiagnosis", "imagingAtDiagnosis", "surgeryAtDiagnosis", "metastatic", "cancerStage"]}, {"title": "DIAGNOZA HISTOPATOLOGICZNA", "rootFieldsIds": ["pathologicalDiagnosis", "clinicalStageOfCancer", "riskGroup", "histPatDiagnosis"]}, {"title": "KLASYFIKACJA", "rootFieldsIds": ["icd10", "iccc3"]}, {"title": "KOMENTARZ", "rootFieldsIds": ["comment"]}], "fields": [{"fieldId": "predispositionToCancer", "label": "Predyspozycje do nowotworzenia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "predispositionToCancerSet", "label": "", "errors": [], "type": "fields-set", "disabled": false, "parentId": "predispositionToCancer", "fieldsSetItems": [{"fieldId": "predispositionToCancerDysGonads", "label": "Dysgenezja gonad", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "predispositionToCancerSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "predispositionToCancer"}]}, {"fieldId": "predisposition<PERSON>o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "Zespół Turnera", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "predispositionToCancerSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "predispositionToCancer"}]}, {"fieldId": "predispositionTo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON>ł <PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "predispositionToCancerSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "predispositionToCancer"}]}, {"fieldId": "predispositionToCancer<PERSON><PERSON><PERSON><PERSON>", "label": "Zespół Peutza-Jeghersa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "predispositionToCancerSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "predispositionToCancer"}]}, {"fieldId": "predispositionToCancer<PERSON><PERSON>", "label": "<PERSON><PERSON>", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": "predispositionToCancerSet", "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "predispositionToCancer"}], "fieldWidth": 500}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "predispositionToCancer"}]}]}, {"fieldId": "assessmentOfThePatientsCondition", "label": "<PERSON><PERSON>na stanu pacjenta wg s<PERSON><PERSON>/<PERSON>nsky", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "10", "value": "10"}, {"label": "20", "value": "20"}, {"label": "30", "value": "30"}, {"label": "40", "value": "40"}, {"label": "50", "value": "50"}, {"label": "60", "value": "60"}, {"label": "70", "value": "70"}, {"label": "80", "value": "80"}, {"label": "90", "value": "90"}, {"label": "100", "value": "100"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": []}, {"fieldId": "dateOfDiagnosis", "label": "Data rozpoznania", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": []}, {"fieldId": "descriptionAndLocationOfTheTumor", "label": "Opis i lokalizacja nowotworu", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/14Dj_AWsJGAHFDF6ef9KpzYE8aGoX1zfMfbYq1qykRUU/edit?tab=t.0#heading=h.vslv4z6b7mar", "tooltipWidth": 170}, {"fieldId": "tumorLocation", "label": "Lokalizacja guza", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Jądr<PERSON>", "value": "Jądr<PERSON>"}, {"label": "Jajnik", "value": "Jajnik"}, {"label": "Pozagonadalna - śródpiersie", "value": "Pozagonadalna - śródpiersie"}, {"label": "Pozagonadalna - krzyżowo-ogonowa", "value": "Pozagonadalna - krzyżowo-ogonowa"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "fieldWidth": 400}, {"fieldId": "markersAtDiagnosis", "label": "<PERSON><PERSON> now<PERSON>e", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "markersAtDiagnosisSet", "label": "", "errors": [], "type": "fields-set", "disabled": false, "parentId": "markersAtDiagnosis", "fieldsSetItems": [{"fieldId": "markersAtDiagnosisAFP", "label": "AFP", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "markersAtDiagnosisSet", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Podwyższony", "value": "Podwyższony"}, {"label": "W normie", "value": "W normie"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "markersAtDiagnosis", "isInherited": true}]}, {"fieldId": "markersAtDiagnosisBHCG", "label": "βHCG", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "markersAtDiagnosisSet", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Podwyższony", "value": "Podwyższony"}, {"label": "W normie", "value": "W normie"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "markersAtDiagnosis", "isInherited": true}]}, {"fieldId": "markersAtDiagnosisLDH", "label": "LDH", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "markersAtDiagnosisSet", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Podwyższony", "value": "Podwyższony"}, {"label": "W normie", "value": "W normie"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "markersAtDiagnosis", "isInherited": true}]}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "markersAtDiagnosis"}]}]}, {"fieldId": "imagingAtDiagnosis", "label": "Diagnostyka obrazowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "imagingAtDiagnosisSet", "label": "Metoda obrazo<PERSON>ia", "errors": [], "type": "fields-set", "disabled": false, "parentId": "markersAtDiagnosis", "fieldsSetItems": [{"fieldId": "imagingAtDiagnosisMethodAbdominalCavity", "label": "USG/MRI jamy brzusznej", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "imagingAtDiagnosisSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "imagingAtDiagnosis", "isInherited": true}]}, {"fieldId": "imagingAtDiagnosisMethodChest", "label": "TK klatki piersiowej", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "imagingAtDiagnosisSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "imagingAtDiagnosis", "isInherited": true}]}, {"fieldId": "imagingAtDiagnosisMethodBrain", "label": "PET CT/TK/MRI mózgowia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "imagingAtDiagnosisSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "imagingAtDiagnosis", "isInherited": true}]}, {"fieldId": "imagingAtDiagnosisMethodBoneScintigraphy", "label": "Scyntografia kości", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "imagingAtDiagnosisSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "imagingAtDiagnosis", "isInherited": true}]}, {"fieldId": "imagingAtDiagnosisMethodBoneMarrowBiopsy", "label": "Biopsja szpiku", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "imagingAtDiagnosisSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "imagingAtDiagnosis", "isInherited": true}]}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "imagingAtDiagnosis"}]}]}, {"fieldId": "surgeryAtDiagnosis", "label": "Zabieg operacyjny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "surgeryAtDiagnosisSet", "label": "", "errors": [], "type": "fields-set", "disabled": false, "parentId": "surgeryAtDiagnosis", "fieldsSetItems": [{"fieldId": "surgeryAtDiagnosisDate", "label": "Data zabiegu", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "surgeryAtDiagnosisSet", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgeryAtDiagnosis", "isInherited": true}]}, {"fieldId": "surgeryAtDiagnosisType", "label": "Metoda", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "surgeryAtDiagnosisSet", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Biopsja otwarta", "value": "Biopsja otwarta"}, {"label": "Biopsja igłowa", "value": "Biopsja igłowa"}, {"label": "Resekcja częściowa", "value": "Resekcja częściowa"}, {"label": "Resekcja całkowita", "value": "Resekcja całkowita"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgeryAtDiagnosis", "isInherited": true}]}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgeryAtDiagnosis"}]}]}, {"fieldId": "metastatic", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "metastaticSet", "label": "Lokalizacja przerzutów", "errors": [], "type": "fields-set", "disabled": false, "parentId": "metastatic", "fieldsSetItems": [{"fieldId": "metastaticSitesLymphNodes", "label": "Węzły chłonne", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastatic"}]}, {"fieldId": "metastaticSitesLung", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastatic"}]}, {"fieldId": "metastaticSitesLiver", "label": "Wątroba", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastatic"}]}, {"fieldId": "metastaticSitesBrain", "label": "Mózgowie", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastatic"}]}, {"fieldId": "metastaticSitesBones", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastatic"}]}, {"fieldId": "metastaticSites<PERSON><PERSON><PERSON>", "label": "Szpik", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastatic"}]}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastatic"}]}]}, {"fieldId": "cancerStage", "label": "Stopień zaawansowania nowotworu zgodnie z klasyfikacjami TNM", "errors": [], "type": "fields-group", "disabled": false, "parentId": null, "fieldsGroupItems": [{"fieldId": "cancerStageClassificationsT", "label": "T", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "cancerStage", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "T0", "value": "T0"}, {"label": "T1", "value": "T1"}, {"label": "T1a", "value": "T1a"}, {"label": "T1b", "value": "T1b"}, {"label": "T2", "value": "T2"}, {"label": "T2a", "value": "T2a"}, {"label": "T2b", "value": "T2b"}, {"label": "TX", "value": "TX"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "cancerStageClassificationsN", "label": "N", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "cancerStage", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "N0", "value": "N0"}, {"label": "N1", "value": "N1"}, {"label": "NX", "value": "NX"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "cancerStageClassificationsM", "label": "M", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "cancerStage", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "M0", "value": "M0"}, {"label": "M1", "value": "M1"}, {"label": "MX", "value": "MX"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [], "fieldWidth": 545}, {"fieldId": "pathologicalDiagnosis", "label": "Rozpoznanie patomorfologiczne", "errors": [], "type": "ckeditor-input-field", "disabled": false, "parentId": null, "value": "", "maxTextLength": 1000, "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/14Dj_AWsJGAHFDF6ef9KpzYE8aGoX1zfMfbYq1qykRUU/edit?tab=t.0#heading=h.9u349rljagbb", "tooltipWidth": 170}, {"fieldId": "clinicalStageOfCancer", "label": "Stadium kliniczne zaawansowania nowotworu", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "I", "value": "I"}, {"label": "II", "value": "II"}, {"label": "IIIa", "value": "IIIa"}, {"label": "IIIb", "value": "IIIb"}, {"label": "IV", "value": "IV"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/14Dj_AWsJGAHFDF6ef9KpzYE8aGoX1zfMfbYq1qykRUU/edit?tab=t.0#heading=h.xxiv9zvjcdbq", "tooltipWidth": 170}, {"fieldId": "riskGroup", "label": "Grupa ryzyka", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "SR", "value": "SR"}, {"label": "HR", "value": "HR"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/14Dj_AWsJGAHFDF6ef9KpzYE8aGoX1zfMfbYq1qykRUU/edit?tab=t.0#heading=h.bdvpipims54", "tooltipWidth": 170}, {"fieldId": "histPatDiagnosis", "label": "Diagnoza histopatologiczna", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Dojrzały potworniak", "value": "Dojrzały potworniak"}, {"label": "Niedojrzały potworniak", "value": "Niedojrzały potworniak"}, {"label": "Dojrzały lub niedojrzały potworniak z dodatkowym komponentem złośliwym", "value": "Dojrzały lub niedojrzały potworniak z dodatkowym komponentem złośliwym"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"label": "Guz pęcherzyka żółtkowego", "value": "Guz pęcherzyka żółtkowego"}, {"label": "Rak z<PERSON>", "value": "Rak z<PERSON>"}, {"label": "Kosmówczak złośliwy", "value": "Kosmówczak złośliwy"}, {"label": "Mieszany guz germinalny", "value": "Mieszany guz germinalny"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "fieldWidth": 400, "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/14Dj_AWsJGAHFDF6ef9KpzYE8aGoX1zfMfbYq1qykRUU/edit?tab=t.0#heading=h.mnx0mk7k8re", "tooltipWidth": 170}, {"fieldId": "icd10", "label": "ICD-10", "errors": [], "type": "live-select-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "ICD10", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/14Dj_AWsJGAHFDF6ef9KpzYE8aGoX1zfMfbYq1qykRUU/edit?tab=t.0#heading=h.dlicra4yopsj", "tooltipWidth": 170}, {"fieldId": "iccc3", "label": "ICCC-3", "errors": [], "type": "live-select-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "ICCC", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/14Dj_AWsJGAHFDF6ef9KpzYE8aGoX1zfMfbYq1qykRUU/edit?tab=t.0#heading=h.vif1jzl94lae", "tooltipWidth": 170}, {"fieldId": "comment", "label": "Komentarz", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": []}]}