{"formTitle": "", "sections": [{"title": "WZNOWY", "rootFieldsIds": ["firstRelapse", "secondRelapse", "thirdRelapse", "fourthRelapse", "fifthRelapse"]}, {"title": "KOMENTARZE", "rootFieldsIds": ["relapseComment"]}], "fields": [{"fieldId": "firstRelapse", "label": "Pierwsza wznowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "firstRelapseGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "firstRelapse", "fieldsGroupItems": [{"fieldId": "firstRelapseDate", "label": "Data rozpoznania wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "firstRelapseGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "firstRelapseType", "label": "Typ wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "firstRelapseGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "Lokalna"}, {"label": "Odległa", "value": "Odległa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}, {"fieldId": "firstRelapseTreatmentStartDate", "label": "Data rozpoczęcia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "firstRelapse", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}]}, {"fieldId": "firstRelapseRetrobulbar", "label": "Leczenie wznowy pozagałkowej", "errors": [], "type": "fields-set", "disabled": false, "parentId": "firstRelapse", "fieldsSetItems": [{"fieldId": "progressionSurgery", "label": "Zabieg operacyjny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "firstRelapseRetrobulbar", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse", "isInherited": true}]}, {"fieldId": "progressionChemotherapySystemic", "label": "Chemioterapia systemowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "firstRelapseRetrobulbar", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse", "isInherited": true}]}, {"fieldId": "progressionRadiotherapyEBRT", "label": "Radioterapia EBRT", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "firstRelapseRetrobulbar", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse", "isInherited": true}]}, {"fieldId": "progressionAutoHSCT", "label": "Autoprzeszczep", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "firstRelapseRetrobulbar", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse", "isInherited": true}]}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}]}, {"fieldId": "firstRelapseLocal", "label": "Leczenie wznowy miejscowej", "errors": [], "type": "fields-set", "disabled": false, "parentId": "firstRelapse", "fieldsSetItems": [{"fieldId": "firstRelapseOAC", "label": "Chemiochirurgia tętnicy ocznej", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "firstRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "firstRelapse", "conditionalValue": "yes", "isInherited": true}]}, {"fieldId": "firstRelapseTTT", "label": "Przeźrenicza termoterapia (TTT)", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "firstRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "firstRelapse", "conditionalValue": "yes", "isInherited": true}]}, {"fieldId": "firstRelapseCryotherapy", "label": "Krioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "firstRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "firstRelapse", "conditionalValue": "yes", "isInherited": true}]}, {"fieldId": "firstRelapseBrachytherapy", "label": "Brachyterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "firstRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "firstRelapse", "conditionalValue": "yes", "isInherited": true}]}, {"fieldId": "firstRelapseChemotherapyIntravitreal", "label": "Chemioterapia doszklistkowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "firstRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "firstRelapse", "conditionalValue": "yes", "isInherited": true}]}, {"fieldId": "firstRelapseEnucleation", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "firstRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "firstRelapse", "conditionalValue": "yes", "isInherited": true}]}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "firstRelapse", "conditionalValue": "yes"}]}, {"fieldId": "firstRelapseGroup2", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "firstRelapse", "fieldsGroupItems": [{"fieldId": "firstRelapseTreatmentEndDate", "label": "Data zakończenia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "firstRelapseGroup2", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "firstRelapseTreatmentResponse", "label": "Odpowiedź na leczenie wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "firstRelapseGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "CR", "value": "CR"}, {"label": "Progresja", "value": "Progresja"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}]}, {"fieldId": "secondRelapse", "label": "Druga wznowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}], "children": [{"fieldId": "secondRelapseGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "secondRelapse", "fieldsGroupItems": [{"fieldId": "secondRelapseDate", "label": "Data rozpoznania wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "secondRelapseGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "secondRelapseType", "label": "Typ wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "secondRelapseGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "Lokalna"}, {"label": "Odległa", "value": "Odległa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}, {"fieldId": "secondRelapseTreatmentStartDate", "label": "Data rozpoczęcia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "secondRelapse", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}]}, {"fieldId": "secondRelapseRetrobulbar", "label": "Leczenie wznowy pozagałkowej", "errors": [], "type": "fields-set", "disabled": false, "parentId": "secondRelapse", "fieldsSetItems": [{"fieldId": "secondRelapseSurgery", "label": "Zabieg operacyjny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "secondRelapseRetrobulbar", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse", "isInherited": true}]}, {"fieldId": "secondRelapseChemotherapySystemic", "label": "Chemioterapia systemowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "secondRelapseRetrobulbar", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse", "isInherited": true}]}, {"fieldId": "secondRelapseRadiotherapyEBRT", "label": "Radioterapia EBRT", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "secondRelapseRetrobulbar", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse", "isInherited": true}]}, {"fieldId": "secondRelapseAutoHSCT", "label": "Autoprzeszczep", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "secondRelapseRetrobulbar", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse", "isInherited": true}]}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}]}, {"fieldId": "secondRelapseLocal", "label": "Leczenie wznowy miejscowej", "errors": [], "type": "fields-set", "disabled": false, "parentId": "secondRelapse", "fieldsSetItems": [{"fieldId": "secondRelapseOAC", "label": "Chemiochirurgia tętnicy ocznej", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "secondRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "secondRelapse", "conditionalValue": "yes", "isInherited": true}]}, {"fieldId": "secondRelapseTTT", "label": "Przeźrenicza termoterapia (TTT)", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "secondRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "secondRelapse", "conditionalValue": "yes", "isInherited": true}]}, {"fieldId": "secondRelapseCryotherapy", "label": "Krioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "secondRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "secondRelapse", "conditionalValue": "yes", "isInherited": true}]}, {"fieldId": "secondRelapseBrachytherapy", "label": "Brachyterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "secondRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "secondRelapse", "conditionalValue": "yes", "isInherited": true}]}, {"fieldId": "secondRelapseChemotherapyIntravitreal", "label": "Chemioterapia doszklistkowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "secondRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "secondRelapse", "conditionalValue": "yes", "isInherited": true}]}, {"fieldId": "secondRelapseEnucleation", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "secondRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "secondRelapse", "conditionalValue": "yes", "isInherited": true}]}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "secondRelapse", "conditionalValue": "yes"}]}, {"fieldId": "secondRelapseGroup2", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "secondRelapse", "fieldsGroupItems": [{"fieldId": "secondRelapseTreatmentEndDate", "label": "Data zakończenia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "secondRelapseGroup2", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "secondRelapseTreatmentResponse", "label": "Odpowiedź na leczenie wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "secondRelapseGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "CR", "value": "CR"}, {"label": "Progresja", "value": "Progresja"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}]}, {"fieldId": "thirdRelapse", "label": "Trzecia wznowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}], "children": [{"fieldId": "thirdRelapseGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "thirdRelapse", "fieldsGroupItems": [{"fieldId": "thirdRelapseDate", "label": "Data rozpoznania wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "thirdRelapseGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "thirdRelapseType", "label": "Typ wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "thirdRelapseGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "Lokalna"}, {"label": "Odległa", "value": "Odległa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}, {"fieldId": "thirdRelapseTreatmentStartDate", "label": "Data rozpoczęcia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "thirdRelapse", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}]}, {"fieldId": "thirdRelapseRetrobulbar", "label": "Leczenie wznowy pozagałkowej", "errors": [], "type": "fields-set", "disabled": false, "parentId": "thirdRelapse", "fieldsSetItems": [{"fieldId": "thirdRelapseSurgery", "label": "Zabieg operacyjny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "thirdRelapseRetrobulbar", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse", "isInherited": true}]}, {"fieldId": "thirdRelapseChemotherapySystemic", "label": "Chemioterapia systemowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "thirdRelapseRetrobulbar", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse", "isInherited": true}]}, {"fieldId": "thirdRelapseRadiotherapyEBRT", "label": "Radioterapia EBRT", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "thirdRelapseRetrobulbar", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse", "isInherited": true}]}, {"fieldId": "thirdRelapseAutoHSCT", "label": "Autoprzeszczep", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "thirdRelapseRetrobulbar", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse", "isInherited": true}]}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}]}, {"fieldId": "thirdRelapseLocal", "label": "Leczenie wznowy miejscowej", "errors": [], "type": "fields-set", "disabled": false, "parentId": "thirdRelapse", "fieldsSetItems": [{"fieldId": "thirdRelapseOAC", "label": "Chemiochirurgia tętnicy ocznej", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "thirdRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "thirdRelapse", "conditionalValue": "yes", "isInherited": true}]}, {"fieldId": "thirdRelapseTTT", "label": "Przeźrenicza termoterapia (TTT)", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "thirdRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "thirdRelapse", "conditionalValue": "yes", "isInherited": true}]}, {"fieldId": "thirdRelapseCryotherapy", "label": "Krioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "thirdRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "thirdRelapse", "conditionalValue": "yes", "isInherited": true}]}, {"fieldId": "thirdRelapseBrachytherapy", "label": "Brachyterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "thirdRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "thirdRelapse", "conditionalValue": "yes", "isInherited": true}]}, {"fieldId": "thirdRelapseChemotherapyIntravitreal", "label": "Chemioterapia doszklistkowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "thirdRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "thirdRelapse", "conditionalValue": "yes", "isInherited": true}]}, {"fieldId": "thirdRelapseEnucleation", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "thirdRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "thirdRelapse", "conditionalValue": "yes", "isInherited": true}]}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "thirdRelapse", "conditionalValue": "yes"}]}, {"fieldId": "thirdRelapseGroup2", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "thirdRelapse", "fieldsGroupItems": [{"fieldId": "thirdRelapseTreatmentEndDate", "label": "Data zakończenia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "thirdRelapseGroup2", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "thirdRelapseTreatmentResponse", "label": "Odpowiedź na leczenie wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "thirdRelapseGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "CR", "value": "CR"}, {"label": "Progresja", "value": "Progresja"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}]}, {"fieldId": "fourthRelapse", "label": "Czwarta wznowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}], "children": [{"fieldId": "fourthRelapseGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "fourthRelapse", "fieldsGroupItems": [{"fieldId": "fourthRelapseDate", "label": "Data rozpoznania wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "fourthRelapseGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "fourthRelapseType", "label": "Typ wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "fourthRelapseGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "Lokalna"}, {"label": "Odległa", "value": "Odległa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}, {"fieldId": "fourthRelapseTreatmentStartDate", "label": "Data rozpoczęcia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "fourthRelapse", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}]}, {"fieldId": "fourthRelapseRetrobulbar", "label": "Leczenie wznowy pozagałkowej", "errors": [], "type": "fields-set", "disabled": false, "parentId": "fourthRelapse", "fieldsSetItems": [{"fieldId": "fourthRelapseSurgery", "label": "Zabieg operacyjny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fourthRelapseRetrobulbar", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse", "isInherited": true}]}, {"fieldId": "fourthRelapseChemotherapySystemic", "label": "Chemioterapia systemowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fourthRelapseRetrobulbar", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse", "isInherited": true}]}, {"fieldId": "fourthRelapseRadiotherapyEBRT", "label": "Radioterapia EBRT", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fourthRelapseRetrobulbar", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse", "isInherited": true}]}, {"fieldId": "fourthRelapseAutoHSCT", "label": "Autoprzeszczep", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fourthRelapseRetrobulbar", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse", "isInherited": true}]}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}]}, {"fieldId": "fourthRelapseLocal", "label": "Leczenie wznowy miejscowej", "errors": [], "type": "fields-set", "disabled": false, "parentId": "fourthRelapse", "fieldsSetItems": [{"fieldId": "fourthRelapseOAC", "label": "Chemiochirurgia tętnicy ocznej", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fourthRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "fourthRelapse", "conditionalValue": "yes", "isInherited": true}]}, {"fieldId": "fourthRelapseTTT", "label": "Przeźrenicza termoterapia (TTT)", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fourthRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "fourthRelapse", "conditionalValue": "yes", "isInherited": true}]}, {"fieldId": "fourthRelapseCryotherapy", "label": "Krioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fourthRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "fourthRelapse", "conditionalValue": "yes", "isInherited": true}]}, {"fieldId": "fourthRelapseBrachytherapy", "label": "Brachyterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fourthRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "fourthRelapse", "conditionalValue": "yes", "isInherited": true}]}, {"fieldId": "fourthRelapseChemotherapyIntravitreal", "label": "Chemioterapia doszklistkowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fourthRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "fourthRelapse", "conditionalValue": "yes", "isInherited": true}]}, {"fieldId": "fourthRelapseEnucleation", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fourthRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "fourthRelapse", "conditionalValue": "yes", "isInherited": true}]}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "fourthRelapse", "conditionalValue": "yes"}]}, {"fieldId": "fourthRelapseGroup2", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "fourthRelapse", "fieldsGroupItems": [{"fieldId": "fourthRelapseTreatmentEndDate", "label": "Data zakończenia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "fourthRelapseGroup2", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "fourthRelapseTreatmentResponse", "label": "Odpowiedź na leczenie wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "fourthRelapseGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "CR", "value": "CR"}, {"label": "Progresja", "value": "Progresja"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}]}, {"fieldId": "fifthRelapse", "label": "Piąta wznowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}], "children": [{"fieldId": "fifthRelapseGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "fifthRelapse", "fieldsGroupItems": [{"fieldId": "fifthRelapseDate", "label": "Data rozpoznania wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "fifthRelapseGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "fifthRelapseType", "label": "Typ wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "fifthRelapseGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "Lokalna"}, {"label": "Odległa", "value": "Odległa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}, {"fieldId": "fifthRelapseTreatmentStartDate", "label": "Data rozpoczęcia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "fifthRelapse", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}]}, {"fieldId": "fifthRelapseRetrobulbar", "label": "Leczenie wznowy pozagałkowej", "errors": [], "type": "fields-set", "disabled": false, "parentId": "fifthRelapse", "fieldsSetItems": [{"fieldId": "fifthRelapseSurgery", "label": "Zabieg operacyjny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fifthRelapseRetrobulbar", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse", "isInherited": true}]}, {"fieldId": "fifthRelapseChemotherapySystemic", "label": "Chemioterapia systemowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fifthRelapseRetrobulbar", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse", "isInherited": true}]}, {"fieldId": "fifthRelapseRadiotherapyEBRT", "label": "Radioterapia EBRT", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fifthRelapseRetrobulbar", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse", "isInherited": true}]}, {"fieldId": "fifthRelapseAutoHSCT", "label": "Autoprzeszczep", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fifthRelapseRetrobulbar", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse", "isInherited": true}]}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}]}, {"fieldId": "fifthRelapseLocal", "label": "Leczenie wznowy miejscowej", "errors": [], "type": "fields-set", "disabled": false, "parentId": "fifthRelapse", "fieldsSetItems": [{"fieldId": "fifthRelapseOAC", "label": "Chemiochirurgia tętnicy ocznej", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fifthRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "fifthRelapse", "conditionalValue": "yes", "isInherited": true}]}, {"fieldId": "fifthRelapseTTT", "label": "Przeźrenicza termoterapia (TTT)", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fifthRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "fifthRelapse", "conditionalValue": "yes", "isInherited": true}]}, {"fieldId": "fifthRelapseCryotherapy", "label": "Krioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fifthRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "fifthRelapse", "conditionalValue": "yes", "isInherited": true}]}, {"fieldId": "fifthRelapseBrachytherapy", "label": "Brachyterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fifthRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "fifthRelapse", "conditionalValue": "yes", "isInherited": true}]}, {"fieldId": "fifthRelapseChemotherapyIntravitreal", "label": "Chemioterapia doszklistkowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fifthRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "fifthRelapse", "conditionalValue": "yes", "isInherited": true}]}, {"fieldId": "fifthRelapseEnucleation", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fifthRelapseLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "fifthRelapse", "conditionalValue": "yes", "isInherited": true}]}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "fifthRelapse", "conditionalValue": "yes"}]}, {"fieldId": "fifthRelapseGroup2", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "fifthRelapse", "fieldsGroupItems": [{"fieldId": "fifthRelapseTreatmentEndDate", "label": "Data zakończenia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "fifthRelapseGroup2", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "fifthRelapseTreatmentResponse", "label": "Odpowiedź na leczenie wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "fifthRelapseGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "CR", "value": "CR"}, {"label": "Progresja", "value": "Progresja"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}]}, {"fieldId": "relapseComment", "label": "", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": []}]}