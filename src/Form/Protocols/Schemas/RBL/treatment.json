{"formTitle": "", "sections": [{"title": "LECZENIE", "rootFieldsIds": ["treatmentStartDate", "treatmentProtocol", "chemotherapy", "radiotherapy", "surgery", "hsct", "hormoneTherapy", "immunotherapy", "targetedTherapy", "radiotherapyBrachytherapy", "surgeryCryotherapy", "otherTherapyTTT", "otherTherapy", "treatmentProtocolModification", "treatmentEndDate", "reasonForEndingTreatment"]}, {"title": "TOKSYCZNOŚCI", "rootFieldsIds": ["toxicity"]}, {"title": "ODPOWIEDŹ NA LECZENIE", "rootFieldsIds": ["patientStatus"]}, {"title": "KOMENTARZE", "rootFieldsIds": ["comment"]}], "fields": [{"fieldId": "treatmentStartDate", "label": "Data rozpoczęcia leczenia", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": []}, {"fieldId": "treatmentProtocol", "label": "<PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "live-select-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "RBL-PROTOCOL", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1WZCYg5KdN2GTp9k40Rix7RIXdUpYJc86EChsKPDyDDg/edit?tab=t.0#heading=h.ghz84lmcl3qy", "tooltipWidth": 170}, {"fieldId": "chemotherapy", "label": "Chemioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "chemotherapySystemic", "label": "Chemioterapia systemowa", "errors": [], "type": "fields-set", "disabled": false, "parentId": "chemotherapy", "fieldsSetItems": [{"fieldId": "chemotherapySystemicVincristine", "label": "Vinkrystyna", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "chemotherapySystemic", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy", "isInherited": true}]}, {"fieldId": "chemotherapySystemicEtoposide", "label": "Etopozyd", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "chemotherapySystemic", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy", "isInherited": true}]}, {"fieldId": "chemotherapySystemicCarboplatin", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "chemotherapySystemic", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy", "isInherited": true}]}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}]}, {"fieldId": "chemotherapyIntraArterial", "label": "Chemioterapia dotętnicza", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "chemotherapy", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}], "children": [{"fieldId": "intraArterialLocation ", "label": "Miejsce podania", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "chemotherapyIntraArterial", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "<PERSON>o prawe", "value": "<PERSON>o prawe"}, {"label": "<PERSON>o lewe", "value": "<PERSON>o lewe"}, {"label": "Oko prawe i lewe", "value": "Oko prawe i lewe"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyIntraArterial"}]}, {"fieldId": "intraArterialMelphalan", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "chemotherapyIntraArterial", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyIntraArterial"}]}, {"fieldId": "intraArterialTopotecan", "label": "Topotekan", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "chemotherapyIntraArterial", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyIntraArterial"}]}, {"fieldId": "intraArterialCarboplatin", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "chemotherapyIntraArterial", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyIntraArterial"}]}]}, {"fieldId": "chemotherapyIntravitreal", "label": "Chemioterapia doszklistkowa", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "chemotherapy", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "<PERSON>o prawe", "value": "<PERSON>o prawe"}, {"label": "<PERSON>o lewe", "value": "<PERSON>o lewe"}, {"label": "Oko prawe i lewe", "value": "Oko prawe i lewe"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}]}]}, {"fieldId": "radiotherapy", "label": "Radioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "radiotherapyEbrt", "label": "Radioterapia EBRT", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "radiotherapy", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "radiotherapy"}]}]}, {"fieldId": "surgery", "label": "Chirurgia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "surgeryEnucleation", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "surgery", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "<PERSON>o prawe", "value": "<PERSON>o prawe"}, {"label": "<PERSON>o lewe", "value": "<PERSON>o lewe"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery"}]}]}, {"fieldId": "hsct", "label": "Przeszczep", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": []}, {"fieldId": "hormoneTherapy", "label": "Hormonoterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "immunotherapy", "label": "Immunoterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "targetedTherapy", "label": "<PERSON><PERSON><PERSON> celowana", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "radiotherapyBrachytherapy", "label": "Brachyterapia", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "<PERSON>o prawe", "value": "<PERSON>o prawe"}, {"label": "<PERSON>o lewe", "value": "<PERSON>o lewe"}, {"label": "Oko prawe i lewe", "value": "Oko prawe i lewe"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": []}, {"fieldId": "surgeryCryotherapy", "label": "Krioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "otherTherapyTTT", "label": "Przeźrenicza termoterapia (TTT)", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "otherTherapy", "label": "<PERSON><PERSON> metody <PERSON>", "errors": [], "type": "text-input-field", "disabled": false, "parentId": null, "value": "", "maxTextLength": 255, "placeholder": "", "renderConditions": [], "fieldWidth": 500}, {"fieldId": "treatmentProtocolModification", "label": "Odstępstwa od protokołu leczenia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "treatmentEndDate", "label": "Data zakończenia leczenia", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": []}, {"fieldId": "reasonForEndingTreatment", "label": "Przyczyna zakończenia leczenia", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Wyleczenie", "value": "PZL1"}, {"label": "Toksyczności i działania niepożądane", "value": "PZL2"}, {"label": "Wyczerpanie możliwości terapeutycznych", "value": "PZL3"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "PZL4"}, {"label": "Decyzja rodzica", "value": "PZL5"}, {"label": "Zgon", "value": "PZL6"}, {"label": "<PERSON><PERSON>", "value": "PZL7"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "fieldWidth": 400}, {"fieldId": "toxicity", "label": "Toksyczności leczenia", "errors": [], "type": "fields-set", "disabled": false, "parentId": null, "fieldsSetItems": [{"fieldId": "toxicityWBC", "label": "WBC", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityGranulocyte", "label": "Granulocyty", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityHaemoglobin", "label": "Hemoglobina", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityThrombocytes", "label": "Płytki krwi", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityHepato", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityNefro", "label": "Nefrotok<PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityCardio", "label": "Kardioto<PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityEnteritis", "label": "Zapalenie j<PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityNeuro", "label": "Neurotoks<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityPulmo", "label": "Pul<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityInfection", "label": "Infekcje", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityAnaphylacticReaction", "label": "Reakcja anafilaktyczna", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}], "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1WZCYg5KdN2GTp9k40Rix7RIXdUpYJc86EChsKPDyDDg/edit?tab=t.0#heading=h.hns4l24c3mb0", "tooltipWidth": 170}, {"fieldId": "patientStatus", "label": "Status pacjenta ", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "SD", "value": "SD"}, {"label": "CR", "value": "CR"}, {"label": "Progresja", "value": "Progresja"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "children": [{"fieldId": "sdDate", "label": "Data potwierdzenia stabilnej choroby", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "patientStatus", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "SD", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "crDate", "label": "Data remisji całkowitej", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "patientStatus", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "CR", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "progressionGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "patientStatus", "fieldsGroupItems": [{"fieldId": "progressionDate", "label": "Data progresji", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "progressionGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "progressionType", "label": "<PERSON><PERSON> prog<PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "progressionGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "Lokalna"}, {"label": "Odległa", "value": "Odległa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "Progresja", "conditionType": "equal", "dependentFieldId": "patientStatus"}], "fieldWidth": 580, "fieldsGroupColumns": 2}, {"fieldId": "progressionRetrobulbar", "label": "Leczenie progresji pozagałkowej", "errors": [], "type": "fields-set", "disabled": false, "parentId": "patientStatus", "fieldsSetItems": [{"fieldId": "progressionSurgery", "label": "Zabieg operacyjny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "progressionRetrobulbar", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "Progresja", "conditionType": "equal", "dependentFieldId": "patientStatus", "isInherited": true}]}, {"fieldId": "progressionChemotherapySystemic", "label": "Chemioterapia systemowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "progressionRetrobulbar", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "Progresja", "conditionType": "equal", "dependentFieldId": "patientStatus", "isInherited": true}]}, {"fieldId": "progressionRadiotherapyEBRT", "label": "Radioterapia EBRT", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "progressionRetrobulbar", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "Progresja", "conditionType": "equal", "dependentFieldId": "patientStatus", "isInherited": true}]}, {"fieldId": "progressionAutoHSCT", "label": "Autoprzeszczep", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "progressionRetrobulbar", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "Progresja", "conditionType": "equal", "dependentFieldId": "patientStatus", "isInherited": true}]}], "renderConditions": [{"conditionalValue": "Progresja", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "progressionLocal", "label": "Leczenie progresji miejscowej", "errors": [], "type": "fields-set", "disabled": false, "parentId": "patientStatus", "fieldsSetItems": [{"fieldId": "progressionOAC", "label": "Chemiochirurgia tętnicy ocznej", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "progressionLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "Progresja", "conditionType": "equal", "dependentFieldId": "patientStatus", "isInherited": true}]}, {"fieldId": "progressionTTT", "label": "Przeźrenicza termoterapia (TTT)", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "progressionLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "Progresja", "conditionType": "equal", "dependentFieldId": "patientStatus", "isInherited": true}]}, {"fieldId": "progressionCryotherapy", "label": "Krioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "progressionLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "Progresja", "conditionType": "equal", "dependentFieldId": "patientStatus", "isInherited": true}]}, {"fieldId": "progressionBrachytherapy", "label": "Brachyterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "progressionLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "Progresja", "conditionType": "equal", "dependentFieldId": "patientStatus", "isInherited": true}]}, {"fieldId": "progressionChemotherapyIntravitreal", "label": "Chemioterapia doszklistkowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "progressionLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "Progresja", "conditionType": "equal", "dependentFieldId": "patientStatus", "isInherited": true}]}, {"fieldId": "progressionEnucleation", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "progressionLocal", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "Progresja", "conditionType": "equal", "dependentFieldId": "patientStatus", "isInherited": true}]}], "renderConditions": [{"conditionalValue": "Progresja", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "progressionGroup2", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "patientStatus", "fieldsGroupItems": [{"fieldId": "progressionTreatmentEndDate", "label": "Data zakończenia leczenia progresji", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "progressionGroup2", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "progressionTreatmentResponse", "label": "Odpowiedź na leczenie progresji", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "progressionGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "CR", "value": "CR"}, {"label": "Progresja", "value": "Progresja"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "Progresja", "conditionType": "equal", "dependentFieldId": "patientStatus"}], "fieldWidth": 580, "fieldsGroupColumns": 2}]}, {"fieldId": "comment", "label": "", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": []}]}