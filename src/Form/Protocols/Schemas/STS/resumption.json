{"formTitle": "", "sections": [{"title": "WZNOWY", "rootFieldsIds": ["firstRelapse", "secondRelapse", "thirdRelapse", "fourthRelapse", "fifthRelapse"]}, {"title": "KOMENTARZE", "rootFieldsIds": ["relapseComment"]}], "fields": [{"fieldId": "firstRelapse", "label": "Pierwsza wznowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "firstRelapseGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "firstRelapse", "fieldsGroupItems": [{"fieldId": "firstRelapseDate", "label": "Data rozpoznania wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "firstRelapseGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "firstRelapseType", "label": "Typ wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "firstRelapseGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "Lokalna"}, {"label": "Odległa", "value": "Odległa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}], "fieldWidth": 850}, {"fieldId": "firstRelapseTherapeuticArm", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "firstRelapse", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "STS-THERAPY-ARM", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}], "fieldWidth": 240}, {"fieldId": "firstRelapseRadiotherapy", "label": "Radioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "firstRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}]}, {"fieldId": "firstRelapseSurgery", "label": "Zabieg operacyjny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "firstRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}], "children": [{"fieldId": "firstRelapseSurgeryDate", "label": "Data zabiegu operacyjnego", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "firstRelapseSurgery", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapseSurgery"}]}, {"fieldId": "firstRelapseSurgeryLocation", "label": "Lokalizacja", "errors": [], "type": "fields-set", "disabled": false, "parentId": "firstRelapseSurgery", "fieldsSetItems": [{"fieldId": "firstRelapseSurgeryPrimaryTumor", "label": "Guz pierwotny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "firstRelapseSurgeryLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapseSurgery"}]}, {"fieldId": "firstRelapseSurgeryLymphNodes", "label": "Węzły chłonne", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "firstRelapseSurgeryLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapseSurgery"}]}, {"fieldId": "firstRelapseSurgeryMetastases", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "firstRelapseSurgeryLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapseSurgery"}]}, {"fieldId": "firstRelapseSurgeryType", "label": "<PERSON><PERSON><PERSON>abi<PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "firstRelapseSurgeryLocation", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Biopsja", "value": "Biopsja"}, {"label": "R0 - Guz usunięty w całości makroskopowo i mikroskopowo", "value": "R0"}, {"label": "R1 - Guz usunięty ma<PERSON>roskopowo, obecne pozostałości mikroskopowe", "value": "R1"}, {"label": "R2 - Guz usunięty niekompletnie, obecne pozostałości makroskopowe", "value": "R2"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapseSurgery"}], "fieldWidth": 600}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapseSurgery"}]}]}, {"fieldId": "firstRelapseEndTreatmentDate", "label": "Data zakończenia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "firstRelapse", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}]}, {"fieldId": "firstRelapseEndTreatmentStatus", "label": "Status po leczeniu wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "firstRelapse", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Całkowita remisja", "value": "Całkowita remisja"}, {"label": "Częściowa remisja", "value": "Częściowa remisja"}, {"label": "Stabilna choroba", "value": "Stabilna choroba"}, {"label": "Progresja", "value": "Progresja"}, {"label": "Wznowa", "value": "Wznowa"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}]}]}, {"fieldId": "secondRelapse", "label": "Druga wznowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}], "children": [{"fieldId": "secondRelapseGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "secondRelapse", "fieldsGroupItems": [{"fieldId": "secondRelapseDate", "label": "Data rozpoznania wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "secondRelapseGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "secondRelapseType", "label": "Typ wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "secondRelapseGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "Lokalna"}, {"label": "Odległa", "value": "Odległa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}], "fieldWidth": 850}, {"fieldId": "secondRelapseTherapeuticArm", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "secondRelapse", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "STS-THERAPY-ARM", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}], "fieldWidth": 240}, {"fieldId": "secondRelapseRadiotherapy", "label": "Radioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "secondRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}]}, {"fieldId": "secondRelapseSurgery", "label": "Zabieg operacyjny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "secondRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}], "children": [{"fieldId": "secondRelapseSurgeryDate", "label": "Data zabiegu operacyjnego", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "secondRelapseSurgery", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapseSurgery"}]}, {"fieldId": "secondRelapseSurgeryLocation", "label": "Lokalizacja", "errors": [], "type": "fields-set", "disabled": false, "parentId": "secondRelapseSurgery", "fieldsSetItems": [{"fieldId": "secondRelapseSurgeryPrimaryTumor", "label": "Guz pierwotny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "secondRelapseSurgeryLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapseSurgery"}]}, {"fieldId": "secondRelapseSurgeryLymphNodes", "label": "Węzły chłonne", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "secondRelapseSurgeryLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapseSurgery"}]}, {"fieldId": "secondRelapseSurgeryMetastases", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "secondRelapseSurgeryLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapseSurgery"}]}, {"fieldId": "secondRelapseSurgeryType", "label": "<PERSON><PERSON><PERSON>abi<PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "secondRelapseSurgeryLocation", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Biopsja", "value": "Biopsja"}, {"label": "R0 - Guz usunięty w całości makroskopowo i mikroskopowo", "value": "R0"}, {"label": "R1 - Guz usunięty ma<PERSON>roskopowo, obecne pozostałości mikroskopowe", "value": "R1"}, {"label": "R2 - Guz usunięty niekompletnie, obecne pozostałości makroskopowe", "value": "R2"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapseSurgery"}], "fieldWidth": 600}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapseSurgery"}]}]}, {"fieldId": "secondRelapseEndTreatmentDate", "label": "Data zakończenia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "secondRelapse", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}]}, {"fieldId": "secondRelapseEndTreatmentStatus", "label": "Status po leczeniu wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "secondRelapse", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Całkowita remisja", "value": "Całkowita remisja"}, {"label": "Częściowa remisja", "value": "Częściowa remisja"}, {"label": "Stabilna choroba", "value": "Stabilna choroba"}, {"label": "Progresja", "value": "Progresja"}, {"label": "Wznowa", "value": "Wznowa"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}]}]}, {"fieldId": "thirdRelapse", "label": "Trzecia wznowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}], "children": [{"fieldId": "thirdRelapseGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "thirdRelapse", "fieldsGroupItems": [{"fieldId": "thirdRelapseDate", "label": "Data rozpoznania wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "thirdRelapseGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "thirdRelapseType", "label": "Typ wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "thirdRelapseGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "Lokalna"}, {"label": "Odległa", "value": "Odległa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}], "fieldWidth": 850}, {"fieldId": "thirdRelapseTherapeuticArm", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "thirdRelapse", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "STS-THERAPY-ARM", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}], "fieldWidth": 240}, {"fieldId": "thirdRelapseRadiotherapy", "label": "Radioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "thirdRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}]}, {"fieldId": "thirdRelapseSurgery", "label": "Zabieg operacyjny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "thirdRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}], "children": [{"fieldId": "thirdRelapseSurgeryDate", "label": "Data zabiegu operacyjnego", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "thirdRelapseSurgery", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapseSurgery"}]}, {"fieldId": "thirdRelapseSurgeryLocation", "label": "Lokalizacja", "errors": [], "type": "fields-set", "disabled": false, "parentId": "thirdRelapseSurgery", "fieldsSetItems": [{"fieldId": "thirdRelapseSurgeryPrimaryTumor", "label": "Guz pierwotny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "thirdRelapseSurgeryLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapseSurgery"}]}, {"fieldId": "thirdRelapseSurgeryLymphNodes", "label": "Węzły chłonne", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "thirdRelapseSurgeryLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapseSurgery"}]}, {"fieldId": "thirdRelapseSurgeryMetastases", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "thirdRelapseSurgeryLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapseSurgery"}]}, {"fieldId": "thirdRelapseSurgeryType", "label": "<PERSON><PERSON><PERSON>abi<PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "thirdRelapseSurgeryLocation", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Biopsja", "value": "Biopsja"}, {"label": "R0 - Guz usunięty w całości makroskopowo i mikroskopowo", "value": "R0"}, {"label": "R1 - Guz usunięty ma<PERSON>roskopowo, obecne pozostałości mikroskopowe", "value": "R1"}, {"label": "R2 - Guz usunięty niekompletnie, obecne pozostałości makroskopowe", "value": "R2"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapseSurgery"}], "fieldWidth": 600}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapseSurgery"}]}]}, {"fieldId": "thirdRelapseEndTreatmentDate", "label": "Data zakończenia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "thirdRelapse", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}]}, {"fieldId": "thirdRelapseEndTreatmentStatus", "label": "Status po leczeniu wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "thirdRelapse", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Całkowita remisja", "value": "Całkowita remisja"}, {"label": "Częściowa remisja", "value": "Częściowa remisja"}, {"label": "Stabilna choroba", "value": "Stabilna choroba"}, {"label": "Progresja", "value": "Progresja"}, {"label": "Wznowa", "value": "Wznowa"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}]}]}, {"fieldId": "fourthRelapse", "label": "Czwarta wznowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}], "children": [{"fieldId": "fourthRelapseGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "fourthRelapse", "fieldsGroupItems": [{"fieldId": "fourthRelapseDate", "label": "Data rozpoznania wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "fourthRelapseGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "fourthRelapseType", "label": "Typ wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "fourthRelapseGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "Lokalna"}, {"label": "Odległa", "value": "Odległa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}], "fieldWidth": 850}, {"fieldId": "fourthRelapseTherapeuticArm", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "fourthRelapse", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "STS-THERAPY-ARM", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}], "fieldWidth": 240}, {"fieldId": "fourthRelapseRadiotherapy", "label": "Radioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fourthRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}]}, {"fieldId": "fourthRelapseSurgery", "label": "Zabieg operacyjny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fourthRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}], "children": [{"fieldId": "fourthRelapseSurgeryDate", "label": "Data zabiegu operacyjnego", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "fourthRelapseSurgery", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapseSurgery"}]}, {"fieldId": "fourthRelapseSurgeryLocation", "label": "Lokalizacja", "errors": [], "type": "fields-set", "disabled": false, "parentId": "fourthRelapseSurgery", "fieldsSetItems": [{"fieldId": "fourthRelapseSurgeryPrimaryTumor", "label": "Guz pierwotny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fourthRelapseSurgeryLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapseSurgery"}]}, {"fieldId": "fourthRelapseSurgeryLymphNodes", "label": "Węzły chłonne", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fourthRelapseSurgeryLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapseSurgery"}]}, {"fieldId": "fourthRelapseSurgeryMetastases", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fourthRelapseSurgeryLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapseSurgery"}]}, {"fieldId": "fourthRelapseSurgeryType", "label": "<PERSON><PERSON><PERSON>abi<PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "fourthRelapseSurgeryLocation", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Biopsja", "value": "Biopsja"}, {"label": "R0 - Guz usunięty w całości makroskopowo i mikroskopowo", "value": "R0"}, {"label": "R1 - Guz usunięty ma<PERSON>roskopowo, obecne pozostałości mikroskopowe", "value": "R1"}, {"label": "R2 - Guz usunięty niekompletnie, obecne pozostałości makroskopowe", "value": "R2"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapseSurgery"}], "fieldWidth": 600}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapseSurgery"}]}]}, {"fieldId": "fourthRelapseEndTreatmentDate", "label": "Data zakończenia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "fourthRelapse", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}]}, {"fieldId": "fourthRelapseEndTreatmentStatus", "label": "Status po leczeniu wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "fourthRelapse", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Całkowita remisja", "value": "Całkowita remisja"}, {"label": "Częściowa remisja", "value": "Częściowa remisja"}, {"label": "Stabilna choroba", "value": "Stabilna choroba"}, {"label": "Progresja", "value": "Progresja"}, {"label": "Wznowa", "value": "Wznowa"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}]}]}, {"fieldId": "fifthRelapse", "label": "Piąta wznowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}], "children": [{"fieldId": "fifthRelapseGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "fifthRelapse", "fieldsGroupItems": [{"fieldId": "fifthRelapseDate", "label": "Data rozpoznania wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "fifthRelapseGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "fifthRelapseType", "label": "Typ wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "fifthRelapseGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "Lokalna"}, {"label": "Odległa", "value": "Odległa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}], "fieldWidth": 850}, {"fieldId": "fifthRelapseTherapeuticArm", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "fifthRelapse", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "STS-THERAPY-ARM", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}], "fieldWidth": 240}, {"fieldId": "fifthRelapseRadiotherapy", "label": "Radioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fifthRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}]}, {"fieldId": "fifthRelapseSurgery", "label": "Zabieg operacyjny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fifthRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}], "children": [{"fieldId": "fifthRelapseSurgeryDate", "label": "Data zabiegu operacyjnego", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "fifthRelapseSurgery", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapseSurgery"}]}, {"fieldId": "fifthRelapseSurgeryLocation", "label": "Lokalizacja", "errors": [], "type": "fields-set", "disabled": false, "parentId": "fifthRelapseSurgery", "fieldsSetItems": [{"fieldId": "fifthRelapseSurgeryPrimaryTumor", "label": "Guz pierwotny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fifthRelapseSurgeryLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapseSurgery"}]}, {"fieldId": "fifthRelapseSurgeryLymphNodes", "label": "Węzły chłonne", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fifthRelapseSurgeryLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapseSurgery"}]}, {"fieldId": "fifthRelapseSurgeryMetastases", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fifthRelapseSurgeryLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapseSurgery"}]}, {"fieldId": "fifthRelapseSurgeryType", "label": "<PERSON><PERSON><PERSON>abi<PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "fifthRelapseSurgeryLocation", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Biopsja", "value": "Biopsja"}, {"label": "R0 - Guz usunięty w całości makroskopowo i mikroskopowo", "value": "R0"}, {"label": "R1 - Guz usunięty ma<PERSON>roskopowo, obecne pozostałości mikroskopowe", "value": "R1"}, {"label": "R2 - Guz usunięty niekompletnie, obecne pozostałości makroskopowe", "value": "R2"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapseSurgery"}], "fieldWidth": 600}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapseSurgery"}]}]}, {"fieldId": "fifthRelapseEndTreatmentDate", "label": "Data zakończenia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "fifthRelapse", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}]}, {"fieldId": "fifthRelapseEndTreatmentStatus", "label": "Status po leczeniu wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "fifthRelapse", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Całkowita remisja", "value": "Całkowita remisja"}, {"label": "Częściowa remisja", "value": "Częściowa remisja"}, {"label": "Stabilna choroba", "value": "Stabilna choroba"}, {"label": "Progresja", "value": "Progresja"}, {"label": "Wznowa", "value": "Wznowa"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}]}]}, {"fieldId": "relapseComment", "label": "", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": []}]}