{"formTitle": "Diag<PERSON>za", "sections": [{"title": "CHOROBY WSPÓŁISTNIEJĄCE", "rootFieldsIds": ["predispositionToCancer", "immuneDeficiency", "cancerInTheFamily", "otherComorbiditiesOrTherapies", "assessmentOfThePatientsCondition"]}, {"title": "CHARAKTERY<PERSON>Y<PERSON> NOWOTWORU", "rootFieldsIds": ["dateOfDiagnosis", "descriptionAndLocationOfTheTumor", "bodySide", "primaryLocation", "cancerStage"]}, {"title": "ZABIEG OPERACYJNY", "rootFieldsIds": ["dateOfSurgery", "typeOfSurgery", "locationOfSurgery", "cancerStageClassificationsAfterSurgery"]}, {"title": "DIAGNOZA HISTOPATOLOGICZNA", "rootFieldsIds": ["dateOfHistPatDiagnosis", "pathologicalDiagnosis", "clinicalStageOfCancerAdvancement", "histPatDiagnosis"]}, {"title": "DODATKOWE BADANIA", "rootFieldsIds": ["geneticAnalysis", "immunohistochemistry"]}, {"title": "KLASYFIKACJA", "rootFieldsIds": ["icd10", "iccc3"]}, {"title": "KOMENTARZE", "rootFieldsIds": ["comment"]}], "fields": [{"fieldId": "predispositionToCancer", "label": "Predyspozycje do nowotworzenia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "predispositionToCancerFieldSet", "label": "", "errors": [], "type": "fields-set", "disabled": false, "parentId": "predispositionToCancer", "fieldsSetItems": [{"fieldId": "predispositionToCancerLiFraumeniSyndrome", "label": "Zespół Li-Fraumeni", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "predispositionToCancerFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "predispositionToCancer"}]}, {"fieldId": "predispositionToCancerCostelloSyndrome", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "predispositionToCancerFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "predispositionToCancer"}]}, {"fieldId": "predispositionToCancerDicer1", "label": "Zespół DICER1 ", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "predispositionToCancerFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "predispositionToCancer"}]}, {"fieldId": "predispositionToCancerNeurofibromatosisType1", "label": "Neurofibromatoza typu 1", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "predispositionToCancerFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "predispositionToCancer"}]}, {"fieldId": "predispositionToCancer<PERSON><PERSON>", "label": "<PERSON><PERSON>", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": "predispositionToCancerFieldSet", "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "predispositionToCancer"}]}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "predispositionToCancer"}]}]}, {"fieldId": "immuneDeficiency", "label": "Niedobór <PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "cancerInTheFamily", "label": "Choroba nowotworowa w rodzinie", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "otherComorbiditiesOrTherapies", "label": "Inne choroby współistniejące lub terapie", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": []}, {"fieldId": "assessmentOfThePatientsCondition", "label": "<PERSON><PERSON>na stanu pacjenta wg s<PERSON><PERSON>/<PERSON>nsky", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "0", "value": "0"}, {"label": "10", "value": "10"}, {"label": "20", "value": "20"}, {"label": "30", "value": "30"}, {"label": "40", "value": "40"}, {"label": "50", "value": "50"}, {"label": "60", "value": "60"}, {"label": "70", "value": "70"}, {"label": "80", "value": "80"}, {"label": "90", "value": "90"}, {"label": "100", "value": "100"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": []}, {"fieldId": "dateOfDiagnosis", "label": "Data rozpoznania", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": []}, {"fieldId": "descriptionAndLocationOfTheTumor", "label": "Opis i lokalizacja nowotworu", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1GB1m5mS2nS8kg6Ve6cNKCuXibFYwR61T_O8q2iuQExo/edit?tab=t.0#heading=h.vslv4z6b7mar", "tooltipWidth": 170}, {"fieldId": "bodySide", "label": "Strona ciała", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "<PERSON><PERSON><PERSON> strona", "value": "<PERSON><PERSON><PERSON> strona"}, {"label": "<PERSON><PERSON> strona", "value": "<PERSON><PERSON> strona"}, {"label": "<PERSON><PERSON> strony", "value": "<PERSON><PERSON> strony"}, {"label": "Linia środkowa ciała", "value": "Linia środkowa ciała"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": []}, {"fieldId": "primaryLocation", "label": "Lokalizacja pierwotna", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "eyeSocket"}, {"label": "Głowa/szyja <PERSON>wo", "value": "headNeckPerioptically"}, {"label": "Głowa/szyja <PERSON>eo<PERSON>wo", "value": "headNeckNonPerioptically"}, {"label": "Pęcherz moczowy/prostata", "value": "bladderProstate"}, {"label": "Układ <PERSON>zo<PERSON>-płciowy", "value": "urogenitalSystem"}, {"label": "Kończyny", "value": "limbs"}, {"label": "Pozostałe", "value": "primaryLocationOthers"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "children": [{"fieldId": "headNeckPeriopticallyFieldSet", "label": "", "errors": [], "type": "fields-set", "disabled": false, "parentId": "primaryLocation", "fieldsSetItems": [{"fieldId": "headNeckPeriopticallyParanasalSinuses", "label": "Zatoki oboczne nosa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "headNeckPeriopticallyFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "headNeckPerioptically", "conditionType": "equal", "dependentFieldId": "primaryLocation"}], "fieldLabelWidth": 266}, {"fieldId": "headNeckPeriopticallyMiddleEarMastoidProcess", "label": "<PERSON>cho <PERSON>, wyrostek sutkowaty", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "headNeckPeriopticallyFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "headNeckPerioptically", "conditionType": "equal", "dependentFieldId": "primaryLocation"}], "fieldLabelWidth": 266}, {"fieldId": "headNeckPeriopticallyPterygopalatineFossa", "label": "D<PERSON>ł skrzydłowo-podniebienny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "headNeckPeriopticallyFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "headNeckPerioptically", "conditionType": "equal", "dependentFieldId": "primaryLocation"}], "fieldLabelWidth": 266}, {"fieldId": "headNeckPeriopticallyUpperThroat", "label": "G<PERSON><PERSON><PERSON> g<PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "headNeckPeriopticallyFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "headNeckPerioptically", "conditionType": "equal", "dependentFieldId": "primaryLocation"}], "fieldLabelWidth": 266}, {"fieldId": "headNeckPeriopticallyNasopharynx", "label": "Nosogardziel", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "headNeckPeriopticallyFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "headNeckPerioptically", "conditionType": "equal", "dependentFieldId": "primaryLocation"}], "fieldLabelWidth": 266}, {"fieldId": "headNeckPeriopticallyBaseOfTheSkull", "label": "Podstawa czaszki", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "headNeckPeriopticallyFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "headNeckPerioptically", "conditionType": "equal", "dependentFieldId": "primaryLocation"}], "fieldLabelWidth": 266}, {"fieldId": "headNeckPeriopticallyNeurocranium", "label": "Mózgoczaszka", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "headNeckPeriopticallyFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "headNeckPerioptically", "conditionType": "equal", "dependentFieldId": "primaryLocation"}], "fieldLabelWidth": 266}, {"fieldId": "headNeckPeriopticallyOrbitalInvasionOfBone", "label": "Oczodół z naciekaniem kości", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "headNeckPeriopticallyFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "headNeckPerioptically", "conditionType": "equal", "dependentFieldId": "primaryLocation"}], "fieldLabelWidth": 266}, {"fieldId": "headNeckPeriopticallyOtherLocations", "label": "Inne lokalizacje", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "headNeckPeriopticallyFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "headNeckPerioptically", "conditionType": "equal", "dependentFieldId": "primaryLocation"}], "fieldLabelWidth": 266}], "renderConditions": [{"conditionalValue": "headNeckPerioptically", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "headNeckNonPeriopticallyFieldSet", "label": "", "errors": [], "type": "fields-set", "disabled": false, "parentId": "primaryLocation", "fieldsSetItems": [{"fieldId": "headNeckNonPeriopticallyNose", "label": "Nos", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "headNeckNonPeriopticallyFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "headNeckNonPerioptically", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "headNeckNonPeriopticallyCheek", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "headNeckNonPeriopticallyFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "headNeckNonPerioptically", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "headNeckNonPeriopticallyTongue", "label": "Język", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "headNeckNonPeriopticallyFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "headNeckNonPerioptically", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "headNeckNonPeriopticallyLip", "label": "Warga", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "headNeckNonPeriopticallyFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "headNeckNonPerioptically", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "headNeckNonPeriopticallySoftTissuesOfTheNeck", "label": "Tkanki miękkie szyi", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "headNeckNonPeriopticallyFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "headNeckNonPerioptically", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "headNeckNonPeriopticallyOtherLocations", "label": "Inne lokalizacje", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "headNeckNonPeriopticallyFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "headNeckNonPerioptically", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}], "renderConditions": [{"conditionalValue": "headNeckNonPerioptically", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "bladderProstateFieldSet", "label": "", "errors": [], "type": "fields-set", "disabled": false, "parentId": "primaryLocation", "fieldsSetItems": [{"fieldId": "bladderProstateUrinaryBladder", "label": "Pęcherz moczowy", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "bladderProstateFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "bladderProstate", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "bladderProstateProstate", "label": "Prostata", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "bladderProstateFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "bladderProstate", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}], "renderConditions": [{"conditionalValue": "bladderProstate", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "urogenitalSystemFieldSet", "label": "", "errors": [], "type": "fields-set", "disabled": false, "parentId": "primaryLocation", "fieldsSetItems": [{"fieldId": "urogenitalSystemPeritesticularLocation", "label": "Lokalizacja okołojądrowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "urogenitalSystemFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "urogenitalSystem", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "urogenitalSystemUterus", "label": "Macica", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "urogenitalSystemFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "urogenitalSystem", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "urogenitalSystemVagina", "label": "Pochwa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "urogenitalSystemFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "urogenitalSystem", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "urogenitalSystemOtherLocations", "label": "Inna lokalizacja", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "urogenitalSystemFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "urogenitalSystem", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}], "renderConditions": [{"conditionalValue": "urogenitalSystem", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "limbsFieldSet", "label": "", "errors": [], "type": "fields-set", "disabled": false, "parentId": "primaryLocation", "fieldsSetItems": [{"fieldId": "limbsLimb", "label": "Kończyna", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "limbsFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "limbs", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "limbsShoulderGirdle", "label": "Obręcz barkowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "limbsFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "limbs", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "limbsArm", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "limbsFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "limbs", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "limbsForearm", "label": "Przedramię", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "limbsFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "limbs", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "limbsHand", "label": "Dłoń", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "limbsFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "limbs", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "limbsGroinButtocks", "label": "<PERSON><PERSON><PERSON>a, pośladki", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "limbsFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "limbs", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "limbsThigh", "label": "<PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "limbsFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "limbs", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "limbsLowerLeg", "label": "<PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "limbsFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "limbs", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "limbsFoot", "label": "Stopa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "limbsFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "limbs", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "limbsOtherLocations", "label": "Inna lokalizacja", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "limbsFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "limbs", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}], "renderConditions": [{"conditionalValue": "limbs", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "primaryLocationOthersFieldSet", "label": "", "errors": [], "type": "fields-set", "disabled": false, "parentId": "primaryLocation", "fieldsSetItems": [{"fieldId": "primaryLocationOthersAbdominalCavity", "label": "<PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "primaryLocationOthersFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "primaryLocationOthers", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "primaryLocationOthersRetroperitoneal", "label": "Zaotrzewnowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "primaryLocationOthersFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "primaryLocationOthers", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "primaryLocationOthersLesserPelvis", "label": "Miednica mniejsza", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "primaryLocationOthersFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "primaryLocationOthers", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "primaryLocationOthersTrunk", "label": "Tułów", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "primaryLocationOthersFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "primaryLocationOthers", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "primaryLocationOthersInsideTheThoracicCage", "label": "Wewnątrz klatki piersiowej", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "primaryLocationOthersFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "primaryLocationOthers", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "primaryLocationOthersMediastinum", "label": "Śródpiersie", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "primaryLocationOthersFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "primaryLocationOthers", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}, {"fieldId": "primaryLocationOthersOtherLocations", "label": "Pozostałe lokalizacje", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "primaryLocationOthersFieldSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "primaryLocationOthers", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}], "renderConditions": [{"conditionalValue": "primaryLocationOthers", "conditionType": "equal", "dependentFieldId": "primaryLocation"}]}], "fieldWidth": 300}, {"fieldId": "cancerStage", "label": "Stopień zaawansowania nowotworu zgodnie z klasyfikacjami TNM", "errors": [], "type": "fields-group", "disabled": false, "parentId": null, "fieldsGroupItems": [{"fieldId": "cancerStageClassificationsT", "label": "T", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "cancerStage", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "T0", "value": "T0"}, {"label": "T1", "value": "T1"}, {"label": "T1a", "value": "T1a"}, {"label": "T1b", "value": "T1b"}, {"label": "T2", "value": "T2"}, {"label": "T2a", "value": "T2a"}, {"label": "T2b", "value": "T2b"}, {"label": "TX", "value": "TX"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "cancerStageClassificationsN", "label": "N", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "cancerStage", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "N0", "value": "N0"}, {"label": "N1", "value": "N1"}, {"label": "NX", "value": "NX"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "cancerStageClassificationsM", "label": "M", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "cancerStage", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "M0", "value": "M0"}, {"label": "M1", "value": "M1"}, {"label": "MX", "value": "MX"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [], "fieldWidth": 545, "children": [{"fieldId": "metastaticSites", "label": "Lokalizacja przerzutów", "errors": [], "type": "fields-set", "disabled": false, "parentId": "cancerStage", "fieldsSetItems": [{"fieldId": "metastaticSitesOun", "label": "OUN", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "cancerStageClassificationsM", "conditionalValue": "M1"}], "fieldLabelWidth": 210}, {"fieldId": "metastaticSitesLiver", "label": "Wątroba", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "cancerStageClassificationsM", "conditionalValue": "M1"}], "fieldLabelWidth": 210}, {"fieldId": "metastaticSitesBoneMarrow", "label": "Szpik kostny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "cancerStageClassificationsM", "conditionalValue": "M1"}], "fieldLabelWidth": 210}, {"fieldId": "metastaticSitesLungs", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "cancerStageClassificationsM", "conditionalValue": "M1"}], "fieldLabelWidth": 210}, {"fieldId": "metastaticSitesPleura", "label": "Opłucna", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "cancerStageClassificationsM", "conditionalValue": "M1"}], "fieldLabelWidth": 210}, {"fieldId": "metastaticSitesSkeleton", "label": "S<PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "cancerStageClassificationsM", "conditionalValue": "M1"}], "fieldLabelWidth": 210}, {"fieldId": "metastaticSitesSubcutaneousTissue", "label": "Tkanka podskórna", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "cancerStageClassificationsM", "conditionalValue": "M1"}], "fieldLabelWidth": 210}, {"fieldId": "metastaticSitesDistantLymphNodes", "label": "Nieregionalne węzły chłonne", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "cancerStageClassificationsM", "conditionalValue": "M1"}], "fieldLabelWidth": 210}, {"fieldId": "metastaticSitesOther", "label": "<PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "cancerStageClassificationsM", "conditionalValue": "M1"}], "fieldLabelWidth": 210}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "cancerStageClassificationsM", "conditionalValue": "M1"}]}], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1GB1m5mS2nS8kg6Ve6cNKCuXibFYwR61T_O8q2iuQExo/edit?tab=t.0#heading=h.c17uivignleu", "tooltipWidth": 170}, {"fieldId": "dateOfSurgery", "label": "Data zabiegu operacyjnego", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": []}, {"fieldId": "typeOfSurgery", "label": "<PERSON><PERSON><PERSON>abi<PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Biopsja", "value": "BI"}, {"label": "R0 - Guz usunięty w całości makroskopowo i mikroskopowo", "value": "R0"}, {"label": "R1 - Guz usunięty ma<PERSON>roskopowo, obecne pozostałości mikroskopowe", "value": "R1"}, {"label": "R2 - Guz usunięty niekompletnie, obecne pozostałości makroskopowe", "value": "R2"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "fieldWidth": 550}, {"fieldId": "locationOfSurgery", "label": "Lokalizacja zabiegu operacyjnego", "errors": [], "type": "fields-set", "disabled": false, "parentId": null, "fieldsSetItems": [{"fieldId": "locationOfSurgeryTumor", "label": "Guz", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "locationOfSurgery", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "locationOfSurgeryLymphNodes", "label": "Węzły chłonne", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "locationOfSurgery", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "locationOfSurgeryMetastatic", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "locationOfSurgery", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}], "renderConditions": []}, {"fieldId": "cancerStageClassificationsAfterSurgery", "label": "Stopień zaawansowania nowotworu zgodnie z klasyfikacjami TNM po zabiegu", "errors": [], "type": "fields-group", "disabled": false, "parentId": null, "fieldsGroupItems": [{"fieldId": "cancerStageClassificationsAfterSurgeryT", "label": "T", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "cancerStageClassificationsAfterSurgery", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "pT0", "value": "pT0"}, {"label": "pT1", "value": "pT1"}, {"label": "pT2", "value": "pT2"}, {"label": "pT3", "value": "pT3"}, {"label": "pT3a", "value": "pT3a"}, {"label": "pT3b", "value": "pT3b"}, {"label": "pT3c", "value": "pT3c"}, {"label": "pT4", "value": "pT4"}, {"label": "pTX", "value": "pTX"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "cancerStageClassificationsAfterSurgeryN", "label": "N", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "cancerStageClassificationsAfterSurgery", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "pN0", "value": "pN0"}, {"label": "pN1", "value": "pN1"}, {"label": "pN1a", "value": "pN1a"}, {"label": "pN1b", "value": "pN1b"}, {"label": "pN2", "value": "pN2"}, {"label": "pNX", "value": "pNX"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "cancerStageClassificationsAfterSurgeryM", "label": "M", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "cancerStageClassificationsAfterSurgery", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "pM0", "value": "pM0"}, {"label": "pM1", "value": "pM1"}, {"label": "pMX", "value": "pMX"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [], "fieldWidth": 545}, {"fieldId": "dateOfHistPatDiagnosis", "label": "Data diagnozy histopatologicznej", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": []}, {"fieldId": "pathologicalDiagnosis", "label": "Rozpoznanie patomorfologiczne", "errors": [], "type": "ckeditor-input-field", "disabled": false, "parentId": null, "value": "", "maxTextLength": 1000, "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1GB1m5mS2nS8kg6Ve6cNKCuXibFYwR61T_O8q2iuQExo/edit?tab=t.0#heading=h.9u349rljagbb", "tooltipWidth": 170}, {"fieldId": "clinicalStageOfCancerAdvancement", "label": "Stadium kliniczne zaawansowania nowotworu", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "I", "value": "I"}, {"label": "IA", "value": "IA"}, {"label": "IB", "value": "IB"}, {"label": "II", "value": "II"}, {"label": "IIA", "value": "IIA"}, {"label": "IIB", "value": "IIB"}, {"label": "IIC", "value": "IIC"}, {"label": "III", "value": "III"}, {"label": "IV", "value": "IV"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1GB1m5mS2nS8kg6Ve6cNKCuXibFYwR61T_O8q2iuQExo/edit?tab=t.0#heading=h.xxiv9zvjcdbq", "tooltipWidth": 170}, {"fieldId": "histPatDiagnosis", "label": "Diagnoza histopatologiczna", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "RMS", "value": "rms"}, {"label": "RMS-like", "value": "rmsLike"}, {"label": "Non-RMS-like", "value": "nonRmsLike"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "children": [{"fieldId": "rmsHistPatDiagnosisRiskGroup", "label": "Grupa ryzyka", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "histPatDiagnosis", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "LR", "value": "STS-GR-01"}, {"label": "SR", "value": "STS-GR-02"}, {"label": "HR", "value": "STS-GR-03"}, {"label": "VHR", "value": "STS-GR-04"}, {"label": "STAGE IV", "value": "STS-GR-05"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "rms", "conditionType": "equal", "dependentFieldId": "histPatDiagnosis"}], "fieldWidth": 300}, {"fieldId": "rmsHistPatDiagnosisDetailed", "label": "Diag<PERSON>za", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "histPatDiagnosis", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "STS-DIAGNOSIS-01", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [{"conditionalValue": "rms", "conditionType": "equal", "dependentFieldId": "histPatDiagnosis"}], "fieldWidth": 300}, {"fieldId": "rmsHistPatDiagnosisSubgroup", "label": "Subgrupa", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "histPatDiagnosis", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "nie dot<PERSON>y", "value": "nie dot<PERSON>y"}, {"label": "A", "value": "A"}, {"label": "B", "value": "B"}, {"label": "C", "value": "C"}, {"label": "D", "value": "D"}, {"label": "E", "value": "E"}, {"label": "F", "value": "F"}, {"label": "G", "value": "G"}, {"label": "H", "value": "H"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "rms", "conditionType": "equal", "dependentFieldId": "histPatDiagnosis"}], "fieldWidth": 300}, {"fieldId": "rmsLikeHistPatDiagnosisRiskGroup", "label": "Grupa ryzyka", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "histPatDiagnosis", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "SySa Stad. I-II&T1,T2a", "value": "STS-GR-06"}, {"label": "SySa Stad.I-II&T2b, III", "value": "STS-GR-07"}, {"label": "EES-, pPNET-, UDS-Stad.I-III", "value": "STS-GR-08"}, {"label": "STAGE IV", "value": "STS-GR-05"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionType": "equal", "dependentFieldId": "histPatDiagnosis", "conditionalValue": "rmsLike"}], "fieldWidth": 300}, {"fieldId": "rmsLikeHistPatDiagnosisDetailed", "label": "Diag<PERSON>za", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "histPatDiagnosis", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "STS-DIAGNOSIS-02", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionType": "equal", "dependentFieldId": "histPatDiagnosis", "conditionalValue": "rmsLike"}], "fieldWidth": 300}, {"fieldId": "nonRmsLikeHistPatDiagnosisRiskGroup", "label": "Grupa ryzyka", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "histPatDiagnosis", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "LR", "value": "STS-GR-01"}, {"label": "SR", "value": "STS-GR-02"}, {"label": "HR", "value": "STS-GR-03"}, {"label": "VHR", "value": "STS-GR-04"}, {"label": "STAGE IV", "value": "STS-GR-05"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionType": "equal", "dependentFieldId": "histPatDiagnosis", "conditionalValue": "nonRmsLike"}], "fieldWidth": 300}, {"fieldId": "nonRmsLikeHistPatDiagnosisDetailed", "label": "Diag<PERSON>za", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "histPatDiagnosis", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "STS-DIAGNOSIS-03", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "nonRmsLike", "conditionType": "equal", "dependentFieldId": "histPatDiagnosis"}], "fieldWidth": 300}], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1GB1m5mS2nS8kg6Ve6cNKCuXibFYwR61T_O8q2iuQExo/edit?tab=t.0#heading=h.dnku885c44pj", "tooltipWidth": 170}, {"fieldId": "geneticAnalysis", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "geneticAnalysisFusion", "label": "", "errors": [], "type": "fields-group-table", "disabled": false, "parentId": "geneticAnalysis", "value": [{"geneticAnalysisFusionName1": null, "geneticAnalysisFusionResult1": null}], "fieldsGroupRowItems": [{"fieldId": "geneticAnalysisFusionName1", "label": "Fuzja genu", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "geneticAnalysisFusion", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "STS-GENFUZ", "liveSelectItemsPerPage": 100, "placeholder": ""}, {"fieldId": "geneticAnalysisFusionResult1", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "geneticAnalysisFusion", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "pos (+)", "value": "POS"}, {"label": "neg (-)", "value": "NEG"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "fieldsGroupTableUniqueValuesFromSelect": [], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "geneticAnalysis"}], "fieldWidth": 600}]}, {"fieldId": "immunohistochemistry", "label": "Immunohistochemia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "immunohistochemistry<PERSON><PERSON><PERSON>", "label": "", "errors": [], "type": "fields-group-table", "disabled": false, "parentId": "immunohistochemistry", "value": [{"immunohistochemistryMarkerName1": null, "immunohistochemistryMarkerResult1": null}], "fieldsGroupRowItems": [{"fieldId": "immunohistochemistryMarkerName1", "label": "<PERSON><PERSON>", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "immunohistochemistry<PERSON><PERSON><PERSON>", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "STS-MARKER", "liveSelectItemsPerPage": 100, "placeholder": ""}, {"fieldId": "immunohistochemistryMarkerResult1", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "immunohistochemistry<PERSON><PERSON><PERSON>", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "pos (+)", "value": "POS"}, {"label": "neg (-)", "value": "NEG"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "fieldsGroupTableUniqueValuesFromSelect": [], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "immunohistochemistry"}], "fieldWidth": 600}]}, {"fieldId": "icd10", "label": "ICD-10", "errors": [], "type": "live-select-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "ICD10", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1GB1m5mS2nS8kg6Ve6cNKCuXibFYwR61T_O8q2iuQExo/edit?tab=t.0#heading=h.dlicra4yopsj", "tooltipWidth": 170}, {"fieldId": "iccc3", "label": "ICCC-3", "errors": [], "type": "live-select-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "ICCC", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1GB1m5mS2nS8kg6Ve6cNKCuXibFYwR61T_O8q2iuQExo/edit?tab=t.0#heading=h.vif1jzl94lae", "tooltipWidth": 170}, {"fieldId": "comment", "label": "", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": []}]}