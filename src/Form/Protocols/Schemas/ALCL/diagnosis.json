{"formTitle": "Diag<PERSON>za", "sections": [{"title": "CHOROBY WSPÓŁISTNIEJĄCE", "rootFieldsIds": ["predispositionToCancer", "immunodeficiency", "hlh", "otherComorbiditiesOrTherapies", "assessmentOfThePatientsCondition"]}, {"title": "OPIS NOWOTWORU", "rootFieldsIds": ["dateOfDiagnosis", "descriptionAndLocationOfTheTumor", "primaryTumour", "cnsInvolvement", "boneMarrowInvolvement", "metastases", "cancerStage"]}, {"title": "DIAGNOZA HISTOPATOLOGICZNA", "rootFieldsIds": ["dateOfHistPatDiagnosis", "pathologicalDiagnosis", "clinicalStageOfCancer", "riskGroup", "histPatDiagnosis"]}, {"title": "KLASYFIKACJA", "rootFieldsIds": ["icd10", "iccc3"]}, {"title": "KOMENTARZ", "rootFieldsIds": ["comment"]}], "fields": [{"fieldId": "predispositionToCancer", "label": "Predyspozycje do nowotworzenia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "immunodeficiency", "label": "Niedobór <PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "hlh", "label": "Limfohistiocytoza hemofagocytarna (HLH)", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "otherComorbiditiesOrTherapies", "label": "Inne choroby współistniejące lub terapie", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": []}, {"fieldId": "assessmentOfThePatientsCondition", "label": "<PERSON><PERSON>na stanu pacjenta wg s<PERSON><PERSON>/<PERSON>nsky", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "0", "value": "0"}, {"label": "10", "value": "10"}, {"label": "20", "value": "20"}, {"label": "30", "value": "30"}, {"label": "40", "value": "40"}, {"label": "50", "value": "50"}, {"label": "60", "value": "60"}, {"label": "70", "value": "70"}, {"label": "80", "value": "80"}, {"label": "90", "value": "90"}, {"label": "100", "value": "100"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": []}, {"fieldId": "dateOfDiagnosis", "label": "Data rozpoznania", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": []}, {"fieldId": "descriptionAndLocationOfTheTumor", "label": "Opis i lokalizacja nowotworu", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1pymdlY7XVT5tse4DV-F0Mqdxee-bjYaG0H3GNk7hGCM/edit?tab=t.0#heading=h.vslv4z6b7mar", "tooltipWidth": 170}, {"fieldId": "primaryTumour", "label": "Guz pierwotny ", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Śródpiersie", "value": "Śródpiersie"}, {"label": "Skóra i/lub tkanki mi<PERSON>kie", "value": "Skóra i/lub tkanki mi<PERSON>kie"}, {"label": "Obwodowe węzły chłonne", "value": "Obwodowe węzły chłonne"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"label": "OUN", "value": "OUN"}, {"label": "<PERSON><PERSON>/nieo<PERSON>", "value": "<PERSON><PERSON>/nieo<PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "fieldWidth": 400}, {"fieldId": "cnsInvolvement", "label": "Zajęcie OUN", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "cnsInvolvementCFS", "label": "Zajęcie płynu mózgowo-rdzeniowego (CFS+)", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "cnsInvolvement", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "cnsInvolvement"}]}, {"fieldId": "cnsInvolvementImaging", "label": "Zmiany w obrazowaniu OUN", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "cnsInvolvement", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "cnsInvolvement"}]}, {"fieldId": "cnsInvolvementCranialNervePalsy", "label": "Porażenie nerwu czaszkowego", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "cnsInvolvement", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "cnsInvolvement"}]}]}, {"fieldId": "boneMarrowInvolvement", "label": "Zajęcie szpiku kostnego", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "metastases", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "metastaticSites", "label": "Lokalizacja przerzutów", "errors": [], "type": "fields-set", "disabled": false, "parentId": "metastases", "fieldsSetItems": [{"fieldId": "metastaticSitesBones", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastases"}]}, {"fieldId": "metastaticSitesPeripheralLymphNodes ", "label": "Obwodowe węzły chłonne", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastases"}]}, {"fieldId": "metastaticSitesSkinSofTissues", "label": "Skóra i/lub tkanki mi<PERSON>kie", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastases"}]}, {"fieldId": "metastaticSitesMediastinum", "label": "Śródpiersie", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastases"}]}, {"fieldId": "metastaticSitesLung", "label": "Płuco", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastases"}]}, {"fieldId": "metastaticSiteLiverSpleen", "label": "Wątroba i/lub ś<PERSON>ziona", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastases"}]}, {"fieldId": "metastaticSitesAbdominalLymphNodes", "label": "Węzły chłonne jamy brzusznej", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastases"}]}, {"fieldId": "metastaticSitesVisceral", "label": "Trzewne (płuca, wątroba, śledziona)", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastases"}]}, {"fieldId": "metastaticSitesEpiduralSpace", "label": "Przestrzeń zewnątrzoponowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastases"}]}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastases"}]}]}, {"fieldId": "cancerStage", "label": "Stopień zaawansowania nowotworu zgodnie z klasyfikacjami TNM", "errors": [], "type": "fields-group", "disabled": false, "parentId": null, "fieldsGroupItems": [{"fieldId": "cancerStageClassificationsT", "label": "T", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "cancerStage", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "T0", "value": "T0"}, {"label": "T1", "value": "T1"}, {"label": "T1a", "value": "T1a"}, {"label": "T1b", "value": "T1b"}, {"label": "T2", "value": "T2"}, {"label": "T2a", "value": "T2a"}, {"label": "T2b", "value": "T2b"}, {"label": "TX", "value": "TX"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "cancerStageClassificationsN", "label": "N", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "cancerStage", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "N0", "value": "N0"}, {"label": "N1", "value": "N1"}, {"label": "NX", "value": "NX"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "cancerStageClassificationsM", "label": "M", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "cancerStage", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "M0", "value": "M0"}, {"label": "M1", "value": "M1"}, {"label": "MX", "value": "MX"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [], "fieldWidth": 545}, {"fieldId": "dateOfHistPatDiagnosis", "label": "Data diagnozy histopatologicznej", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": []}, {"fieldId": "pathologicalDiagnosis", "label": "Rozpoznanie patomorfologiczne", "errors": [], "type": "ckeditor-input-field", "disabled": false, "parentId": null, "value": "", "maxTextLength": 1000, "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1pymdlY7XVT5tse4DV-F0Mqdxee-bjYaG0H3GNk7hGCM/edit?tab=t.0#heading=h.9u349rljagbb", "tooltipWidth": 170}, {"fieldId": "clinicalStageOfCancer", "label": "Stadium kliniczne zaawansowania nowotworu", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": false, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "I", "value": "I"}, {"label": "II", "value": "II"}, {"label": "III", "value": "III"}, {"label": "IV", "value": "IV"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1pymdlY7XVT5tse4DV-F0Mqdxee-bjYaG0H3GNk7hGCM/edit?tab=t.0#heading=h.czkho6lu9w2p", "tooltipWidth": 170}, {"fieldId": "riskGroup", "label": "Grupa ryzyka", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": false, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "SR", "value": "SR"}, {"label": "HR", "value": "HR"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1pymdlY7XVT5tse4DV-F0Mqdxee-bjYaG0H3GNk7hGCM/edit?tab=t.0#heading=h.3fql01ziespn", "tooltipWidth": 170}, {"fieldId": "histPatDiagnosis", "label": "Diagnoza histopatologiczna", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": false, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "ALCL ALK (+)", "value": "ALCL ALK (+)"}, {"label": "ALCL ALK (-)", "value": "ALCL ALK (-)"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1pymdlY7XVT5tse4DV-F0Mqdxee-bjYaG0H3GNk7hGCM/edit?tab=t.0#heading=h.jqi5vxn2nez2", "tooltipWidth": 170}, {"fieldId": "icd10", "label": "ICD-10", "errors": [], "type": "live-select-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "ICD10", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1pymdlY7XVT5tse4DV-F0Mqdxee-bjYaG0H3GNk7hGCM/edit?tab=t.0#heading=h.dlicra4yopsj", "tooltipWidth": 170}, {"fieldId": "iccc3", "label": "ICCC-3", "errors": [], "type": "live-select-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "ICCC", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1pymdlY7XVT5tse4DV-F0Mqdxee-bjYaG0H3GNk7hGCM/edit?tab=t.0#heading=h.vif1jzl94lae", "tooltipWidth": 170}, {"fieldId": "comment", "label": "Komentarz", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": []}]}