{"formTitle": "", "sections": [{"title": "WZNOWY", "rootFieldsIds": ["firstRelapse", "secondRelapse", "thirdRelapse", "fourthRelapse", "fifthRelapse"]}, {"title": "KOMENTARZE", "rootFieldsIds": ["relapseComment"]}], "fields": [{"fieldId": "firstRelapse", "label": "Pierwsza wznowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "firstRelapseGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "firstRelapse", "fieldsGroupItems": [{"fieldId": "firstRelapseDate", "label": "Data rozpoznania wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "firstRelapseGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "firstRelapseType", "label": "Typ wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "firstRelapseGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "Lokalna"}, {"label": "Odległa", "value": "Odległa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}, {"fieldId": "firstRelapseLocalization", "label": "Lokalizacja wznowy", "errors": [], "type": "fields-set", "disabled": false, "parentId": "firstRelapse", "fieldsSetItems": [{"fieldId": "firstRelapseLocalizationPrimaryLesions", "label": "Guz pierwotny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "firstRelapseLocalization", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse", "isInherited": true}]}, {"fieldId": "firstRelapseLocalizationNonPrimaryLesions", "label": "Poza guzem pierwotnym", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "firstRelapseLocalization", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse", "isInherited": true}]}, {"fieldId": "firstRelapseLocalizationCns", "label": "OUN", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "firstRelapseLocalization", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse", "isInherited": true}]}, {"fieldId": "firstRelapseLocalizationBm", "label": "Szpik kostny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "firstRelapseLocalization", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse", "isInherited": true}]}, {"fieldId": "firstRelapseLocalizationBone", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "firstRelapseLocalization", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse", "isInherited": true}]}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}]}, {"fieldId": "firstRelapseProtocol", "label": "Protokół leczenia wznowy", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "firstRelapse", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "ALCL-SECOND-LINE", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}], "fieldWidth": 240}, {"fieldId": "firstRelapseGroup2", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "firstRelapse", "fieldsGroupItems": [{"fieldId": "firstRelapseTreatmentEndDate", "label": "Data zakończenia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "firstRelapseGroup2", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "firstRelapseTreatmentResponse", "label": "Odpowiedź na leczenie wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "firstRelapseGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "CR", "value": "CR"}, {"label": "Progresja", "value": "Progresja"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}]}, {"fieldId": "secondRelapse", "label": "Druga wznowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}], "children": [{"fieldId": "secondRelapseGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "secondRelapse", "fieldsGroupItems": [{"fieldId": "secondRelapseDate", "label": "Data rozpoznania wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "secondRelapseGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "secondRelapseType", "label": "Typ wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "secondRelapseGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "Lokalna"}, {"label": "Odległa", "value": "Odległa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}, {"fieldId": "secondRelapseLocalization", "label": "Lokalizacja wznowy", "errors": [], "type": "fields-set", "disabled": false, "parentId": "secondRelapse", "fieldsSetItems": [{"fieldId": "secondRelapseLocalizationPrimaryLesions", "label": "Guz pierwotny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "secondRelapseLocalization", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse", "isInherited": true}]}, {"fieldId": "secondRelapseLocalizationNonPrimaryLesions", "label": "Poza guzem pierwotnym", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "secondRelapseLocalization", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse", "isInherited": true}]}, {"fieldId": "secondRelapseLocalizationCns", "label": "OUN", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "secondRelapseLocalization", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse", "isInherited": true}]}, {"fieldId": "secondRelapseLocalizationBm", "label": "Szpik kostny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "secondRelapseLocalization", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse", "isInherited": true}]}, {"fieldId": "secondRelapseLocalizationBone", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "secondRelapseLocalization", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse", "isInherited": true}]}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}]}, {"fieldId": "secondRelapseProtocol", "label": "Protokół leczenia wznowy", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "secondRelapse", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "ALCL-SECOND-LINE", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}], "fieldWidth": 240}, {"fieldId": "secondRelapseGroup2", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "secondRelapse", "fieldsGroupItems": [{"fieldId": "secondRelapseTreatmentEndDate", "label": "Data zakończenia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "secondRelapseGroup2", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "secondRelapseTreatmentResponse", "label": "Odpowiedź na leczenie wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "secondRelapseGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "CR", "value": "CR"}, {"label": "Progresja", "value": "Progresja"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}]}, {"fieldId": "thirdRelapse", "label": "Trzecia wznowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}], "children": [{"fieldId": "thirdRelapseGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "thirdRelapse", "fieldsGroupItems": [{"fieldId": "thirdRelapseDate", "label": "Data rozpoznania wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "thirdRelapseGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "thirdRelapseType", "label": "Typ wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "thirdRelapseGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "Lokalna"}, {"label": "Odległa", "value": "Odległa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}, {"fieldId": "thirdRelapseLocalization", "label": "Lokalizacja wznowy", "errors": [], "type": "fields-set", "disabled": false, "parentId": "thirdRelapse", "fieldsSetItems": [{"fieldId": "thirdRelapseLocalizationPrimaryLesions", "label": "Guz pierwotny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "thirdRelapseLocalization", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse", "isInherited": true}]}, {"fieldId": "thirdRelapseLocalizationNonPrimaryLesions", "label": "Poza guzem pierwotnym", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "thirdRelapseLocalization", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse", "isInherited": true}]}, {"fieldId": "thirdRelapseLocalizationCns", "label": "OUN", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "thirdRelapseLocalization", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse", "isInherited": true}]}, {"fieldId": "thirdRelapseLocalizationBm", "label": "Szpik kostny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "thirdRelapseLocalization", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse", "isInherited": true}]}, {"fieldId": "thirdRelapseLocalizationBone", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "thirdRelapseLocalization", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse", "isInherited": true}]}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}]}, {"fieldId": "thirdRelapseProtocol", "label": "Protokół leczenia wznowy", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "thirdRelapse", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "ALCL-SECOND-LINE", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}], "fieldWidth": 240}, {"fieldId": "thirdRelapseGroup2", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "thirdRelapse", "fieldsGroupItems": [{"fieldId": "thirdRelapseTreatmentEndDate", "label": "Data zakończenia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "thirdRelapseGroup2", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "thirdRelapseTreatmentResponse", "label": "Odpowiedź na leczenie wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "thirdRelapseGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "CR", "value": "CR"}, {"label": "Progresja", "value": "Progresja"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}]}, {"fieldId": "fourthRelapse", "label": "Czwarta wznowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}], "children": [{"fieldId": "fourthRelapseGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "fourthRelapse", "fieldsGroupItems": [{"fieldId": "fourthRelapseDate", "label": "Data rozpoznania wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "fourthRelapseGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "fourthRelapseType", "label": "Typ wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "fourthRelapseGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "Lokalna"}, {"label": "Odległa", "value": "Odległa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}, {"fieldId": "fourthRelapseLocalization", "label": "Lokalizacja wznowy", "errors": [], "type": "fields-set", "disabled": false, "parentId": "fourthRelapse", "fieldsSetItems": [{"fieldId": "fourthRelapseLocalizationPrimaryLesions", "label": "Guz pierwotny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fourthRelapseLocalization", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse", "isInherited": true}]}, {"fieldId": "fourthRelapseLocalizationNonPrimaryLesions", "label": "Poza guzem pierwotnym", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fourthRelapseLocalization", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse", "isInherited": true}]}, {"fieldId": "fourthRelapseLocalizationCns", "label": "OUN", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fourthRelapseLocalization", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse", "isInherited": true}]}, {"fieldId": "fourthRelapseLocalizationBm", "label": "Szpik kostny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fourthRelapseLocalization", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse", "isInherited": true}]}, {"fieldId": "fourthRelapseLocalizationBone", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fourthRelapseLocalization", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse", "isInherited": true}]}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}]}, {"fieldId": "fourthRelapseProtocol", "label": "Protokół leczenia wznowy", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "fourthRelapse", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "ALCL-SECOND-LINE", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}], "fieldWidth": 240}, {"fieldId": "fourthRelapseGroup2", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "fourthRelapse", "fieldsGroupItems": [{"fieldId": "fourthRelapseTreatmentEndDate", "label": "Data zakończenia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "fourthRelapseGroup2", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "fourthRelapseTreatmentResponse", "label": "Odpowiedź na leczenie wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "fourthRelapseGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "CR", "value": "CR"}, {"label": "Progresja", "value": "Progresja"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}]}, {"fieldId": "fifthRelapse", "label": "Piąta wznowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}], "children": [{"fieldId": "fifthRelapseGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "fifthRelapse", "fieldsGroupItems": [{"fieldId": "fifthRelapseDate", "label": "Data rozpoznania wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "fifthRelapseGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "fifthRelapseType", "label": "Typ wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "fifthRelapseGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "Lokalna"}, {"label": "Odległa", "value": "Odległa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}, {"fieldId": "fifthRelapseLocalization", "label": "Lokalizacja wznowy", "errors": [], "type": "fields-set", "disabled": false, "parentId": "fifthRelapse", "fieldsSetItems": [{"fieldId": "fifthRelapseLocalizationPrimaryLesions", "label": "Guz pierwotny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fifthRelapseLocalization", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse", "isInherited": true}]}, {"fieldId": "fifthRelapseLocalizationNonPrimaryLesions", "label": "Poza guzem pierwotnym", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fifthRelapseLocalization", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse", "isInherited": true}]}, {"fieldId": "fifthRelapseLocalizationCns", "label": "OUN", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fifthRelapseLocalization", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse", "isInherited": true}]}, {"fieldId": "fifthRelapseLocalizationBm", "label": "Szpik kostny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fifthRelapseLocalization", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse", "isInherited": true}]}, {"fieldId": "fifthRelapseeLocalizationBone", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fifthRelapseLocalization", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse", "isInherited": true}]}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}]}, {"fieldId": "fifthRelapseProtocol", "label": "Protokół leczenia wznowy", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "fifthRelapse", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "ALCL-SECOND-LINE", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}], "fieldWidth": 240}, {"fieldId": "fifthRelapseGroup2", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "fifthRelapse", "fieldsGroupItems": [{"fieldId": "fifthRelapseTreatmentEndDate", "label": "Data zakończenia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "fifthRelapseGroup2", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "fifthRelapseTreatmentResponse", "label": "Odpowiedź na leczenie wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "fifthRelapseGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "CR", "value": "CR"}, {"label": "Progresja", "value": "Progresja"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}]}, {"fieldId": "relapseComment", "label": "", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": []}]}