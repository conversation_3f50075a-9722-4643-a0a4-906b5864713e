{"formTitle": "HL - Chłoniak Hodgkina", "sections": [{"title": "DIAGNOZA", "rootFieldsIds": ["dateOfDiagnosis", "predispositionToCancer", "descriptionAndLocationOfTheTumor", "pathologicalDiagnosis", "icd10", "icd3", "assessmentOfThePatientsCondition", "cancerStage", "clinicalStageOfCancerAdvancement"]}, {"title": "LECZENIE", "rootFieldsIds": ["treatmentStartDate", "treatmentMethods", "nameOfTreatmentProtocol", "treatmentEndDate", "reasonForEndingTreatment"]}, {"title": "WZNOWA", "rootFieldsIds": ["firstRelapse", "firstRelapseDateOfDiagnosis", "firstRelapseType", "firstRelapseTreatmentEndDate", "secondRelapse", "secondRelapseDateOfDiagnosis", "secondRelapseType", "secondRelapseTreatmentEndDate", "thirdRelapse", "thirdRelapseDateOfDiagnosis", "thirdRelapseType", "thirdRelapseTreatmentEndDate", "fourthRelapse", "fourthRelapseDateOfDiagnosis", "fourthRelapseType", "fourthRelapseTreatmentEndDate", "fifthRelapse", "fifthRelapseDateOfDiagnosis", "fifthRelapseType", "fifthRelapseTreatmentEndDate"]}, {"title": "KOMENTARZ", "rootFieldsIds": ["comment"]}], "fields": [{"fieldId": "dateOfDiagnosis", "label": "Data rozpoznania", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "renderConditions": [], "datePickerMaxNowDate": true}, {"fieldId": "predispositionToCancer", "label": "Predyspozycje do nowotworzenia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1FXEtlpI_Q8c754wkk6mvMPPXybijxRQwynqBd-g1DJg/edit?tab=t.0#heading=h.6jv882u4vut5", "tooltipWidth": 170}, {"fieldId": "descriptionAndLocationOfTheTumor", "label": "Opis i lokalizacja nowotworu", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1FXEtlpI_Q8c754wkk6mvMPPXybijxRQwynqBd-g1DJg/edit?tab=t.0#heading=h.tq90n0ki4shn", "tooltipWidth": 170}, {"fieldId": "pathologicalDiagnosis", "label": "Rozpoznanie patomorfologiczne", "errors": [], "type": "ckeditor-input-field", "disabled": false, "parentId": null, "value": "", "maxTextLength": 1000, "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1FXEtlpI_Q8c754wkk6mvMPPXybijxRQwynqBd-g1DJg/edit?tab=t.0#heading=h.5p19sndggayv", "tooltipWidth": 170}, {"fieldId": "icd10", "label": "ICD-10", "errors": [], "type": "live-select-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "ICD10", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1FXEtlpI_Q8c754wkk6mvMPPXybijxRQwynqBd-g1DJg/edit?tab=t.0#heading=h.n696e6ov3sd6", "tooltipWidth": 170}, {"fieldId": "icd3", "label": "ICCC-3", "errors": [], "type": "live-select-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "ICCC", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1FXEtlpI_Q8c754wkk6mvMPPXybijxRQwynqBd-g1DJg/edit?tab=t.0#heading=h.rdrwp8lt9zfv", "tooltipWidth": 170}, {"fieldId": "assessmentOfThePatientsCondition", "label": "<PERSON><PERSON>na stanu pacjenta wg s<PERSON><PERSON>/<PERSON>nsky", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "0", "value": "0"}, {"label": "10", "value": "10"}, {"label": "20", "value": "20"}, {"label": "30", "value": "30"}, {"label": "40", "value": "40"}, {"label": "50", "value": "50"}, {"label": "60", "value": "60"}, {"label": "70", "value": "70"}, {"label": "80", "value": "80"}, {"label": "90", "value": "90"}, {"label": "100", "value": "100"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1FXEtlpI_Q8c754wkk6mvMPPXybijxRQwynqBd-g1DJg/edit?tab=t.0#heading=h.ansy6ciyl0n1", "tooltipWidth": 170}, {"fieldId": "cancerStage", "label": "Stopień zaawansowania nowotworu zgodnie z klasyfikacjami TNM", "errors": [], "type": "fields-group", "disabled": false, "parentId": null, "fieldsGroupItems": [{"fieldId": "cancerStageClassificationsT", "label": "T", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "cancerStage", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "T0", "value": "T0"}, {"label": "T1", "value": "T1"}, {"label": "T1a", "value": "T1a"}, {"label": "T1b", "value": "T1b"}, {"label": "T2", "value": "T2"}, {"label": "T2a", "value": "T2a"}, {"label": "T2b", "value": "T2b"}, {"label": "TX", "value": "TX"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "cancerStageClassificationsN", "label": "N", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "cancerStage", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "N0", "value": "N0"}, {"label": "N1", "value": "N1"}, {"label": "NX", "value": "NX"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "cancerStageClassificationsM", "label": "M", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "cancerStage", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "M0", "value": "M0"}, {"label": "M1", "value": "M1"}, {"label": "MX", "value": "MX"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [], "fieldWidth": 1000}, {"fieldId": "clinicalStageOfCancerAdvancement", "label": "Stadium kliniczne zaawansowania nowotworu", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "nie dot<PERSON>y", "value": "nie dot<PERSON>y"}, {"label": "I", "value": "I"}, {"label": "IA", "value": "IA"}, {"label": "IB", "value": "IB"}, {"label": "II", "value": "II"}, {"label": "IIA", "value": "IIA"}, {"label": "IIB", "value": "IIB"}, {"label": "IIC", "value": "IIC"}, {"label": "III", "value": "III"}, {"label": "IIIA", "value": "IIIA"}, {"label": "IIIB", "value": "IIIB"}, {"label": "IV", "value": "IV"}, {"label": "IVA", "value": "IVA"}, {"label": "IVB", "value": "IVB"}, {"label": "IVS", "value": "IVS"}, {"label": "V", "value": "V"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": []}, {"fieldId": "treatmentStartDate", "label": "Data rozpoczęcia leczenia", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "renderConditions": [], "datePickerMaxNowDate": true}, {"fieldId": "treatmentMethods", "label": "", "errors": [], "type": "fields-set", "disabled": false, "parentId": null, "fieldsSetItems": [{"fieldId": "chemotherapy", "label": "Chemioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "treatmentMethods", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "radiotherapy", "label": "Radioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "treatmentMethods", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "transplant", "label": "Przeszczep", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "treatmentMethods", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "surgery", "label": "Chirurgia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "treatmentMethods", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "hormonetherapy", "label": "Hormonoterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "treatmentMethods", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "immunotherapy", "label": "Immunoterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "treatmentMethods", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "targetedTherapy", "label": "<PERSON><PERSON><PERSON> celowana", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "treatmentMethods", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "other", "label": "<PERSON><PERSON>", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "treatmentMethods", "value": "", "maxTextLength": 1000, "placeholder": "", "renderConditions": []}], "renderConditions": []}, {"fieldId": "nameOfTreatmentProtocol", "label": "<PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "live-select-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "HL-PROTOCOL", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1FXEtlpI_Q8c754wkk6mvMPPXybijxRQwynqBd-g1DJg/edit?tab=t.0#heading=h.u56d204d82kc", "tooltipWidth": 170}, {"fieldId": "treatmentEndDate", "label": "Data zakończenia leczenia", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "renderConditions": [], "datePickerMaxNowDate": true}, {"fieldId": "reasonForEndingTreatment", "label": "Przyczyna zakończenia leczenia", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Wyleczenie", "value": "PZL1"}, {"label": "Toksyczności i działania niepożądane", "value": "PZL2"}, {"label": "Wyczerpanie możliwości terapeutycznych", "value": "PZL3"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "PZL4"}, {"label": "Decyzja rodzica", "value": "PZL5"}, {"label": "Zgon", "value": "PZL6"}, {"label": "<PERSON><PERSON>", "value": "PZL7"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": []}, {"fieldId": "firstRelapse", "label": "Pierwsza wznowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "firstRelapseDateOfDiagnosis", "label": "Data rozpoznania wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "renderConditions": [{"conditionType": "equal", "dependentFieldId": "firstRelapse", "conditionalValue": "yes"}], "datePickerMaxNowDate": true}, {"fieldId": "firstRelapseType", "label": "Typ wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "TW-01"}, {"label": "Odległa", "value": "TW-02"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "TW-03"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}]}, {"fieldId": "firstRelapseTreatmentEndDate", "label": "Data zakończenia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}], "datePickerMaxNowDate": true}, {"fieldId": "secondRelapse", "label": "Druga wznowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "firstRelapse", "conditionalValue": "yes"}]}, {"fieldId": "secondRelapseDateOfDiagnosis", "label": "Data rozpoznania wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "renderConditions": [{"conditionType": "equal", "dependentFieldId": "secondRelapse", "conditionalValue": "yes"}], "datePickerMaxNowDate": true}, {"fieldId": "secondRelapseType", "label": "Typ wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "TW-01"}, {"label": "Odległa", "value": "TW-02"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "TW-03"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}]}, {"fieldId": "secondRelapseTreatmentEndDate", "label": "Data zakończenia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": "", "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}], "datePickerMaxNowDate": true}, {"fieldId": "thirdRelapse", "label": "Trzecia wznowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}]}, {"fieldId": "thirdRelapseDateOfDiagnosis", "label": "Data rozpoznania wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": "", "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "renderConditions": [{"conditionType": "equal", "dependentFieldId": "thirdRelapse", "conditionalValue": "yes"}], "datePickerMaxNowDate": true}, {"fieldId": "thirdRelapseType", "label": "Typ wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "TW-01"}, {"label": "Odległa", "value": "TW-02"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "TW-03"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}]}, {"fieldId": "thirdRelapseTreatmentEndDate", "label": "Data zakończenia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": "", "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}], "datePickerMaxNowDate": true}, {"fieldId": "fourthRelapse", "label": "Czwarta wznowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}]}, {"fieldId": "fourthRelapseDateOfDiagnosis", "label": "Data rozpoznania wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": "", "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "renderConditions": [{"conditionType": "equal", "dependentFieldId": "fourthRelapse", "conditionalValue": "yes"}], "datePickerMaxNowDate": true}, {"fieldId": "fourthRelapseType", "label": "Typ wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "TW-01"}, {"label": "Odległa", "value": "TW-02"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "TW-03"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}]}, {"fieldId": "fourthRelapseTreatmentEndDate", "label": "Data zakończenia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": "", "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}], "datePickerMaxNowDate": true}, {"fieldId": "fifthRelapse", "label": "Piąta wznowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}]}, {"fieldId": "fifthRelapseDateOfDiagnosis", "label": "Data rozpoznania wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": "", "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "renderConditions": [{"conditionType": "equal", "dependentFieldId": "fifthRelapse", "conditionalValue": "yes"}], "datePickerMaxNowDate": true}, {"fieldId": "fifthRelapseType", "label": "Typ wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "TW-01"}, {"label": "Odległa", "value": "TW-02"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "TW-03"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}]}, {"fieldId": "fifthRelapseTreatmentEndDate", "label": "Data zakończenia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": "", "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}], "datePickerMaxNowDate": true}, {"fieldId": "comment", "label": "", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": []}]}