{"formTitle": "All fields form", "sections": [{"title": "New section", "rootFieldsIds": ["text-field-id", "three-state-level-id", "select-field-id", "fields-group-id", "number-input-id", "datepicker-id", "text-area-input-id", "ckeditor-input-id", "fields-group-table-id", "group-table-2", "live-select", "table_with_computed", "field-set", "field_set_hard", "table_with_unique_select"]}], "fields": [{"type": "text-input-field", "fieldId": "text-field-id", "label": "text-field-label", "errors": [], "value": "", "disabled": false, "placeholder": "text-field-placeholder", "maxTextLength": 1000, "renderConditions": [], "parentId": null}, {"type": "select-input-field", "fieldId": "select-field-id", "label": "select-field-label", "errors": [], "value": null, "disabled": false, "placeholder": "select-field-placeholder", "selectOptions": [{"label": "test", "value": "test"}], "searchable": true, "canCleanSelect": true, "showSelectAll": false, "selectMode": "single", "openSelectDirection": "bottom", "canDeselect": false, "closeOnSelect": true, "renderConditions": [], "parentId": null}, {"type": "three-state-level", "fieldId": "three-state-level-id", "label": "three-state-level-label", "errors": [], "value": "not-selected", "disabled": false, "threeStateLevelOptions": [{"label": "yes", "value": "TAK"}, {"label": "", "value": "not-selected"}, {"label": "no", "value": "NIE"}], "children": [{"type": "text-input-field", "fieldId": "children_1", "label": "children label", "errors": [], "value": "", "disabled": false, "placeholder": "placeholder", "renderConditions": [{"conditionType": "equal", "dependentFieldId": "three-state-level-id", "conditionalValue": "TAK"}], "children": [{"type": "text-input-field", "fieldId": "children_children_1", "label": "test", "errors": [], "value": "", "disabled": false, "placeholder": "", "maxTextLength": 1000, "renderConditions": [], "parentId": "children_1"}], "maxTextLength": 1000, "parentId": "three-state-level-id"}], "renderConditions": [], "parentId": null}, {"type": "fields-group", "fieldId": "fields-group-id", "label": "fields group label", "errors": [], "value": "", "disabled": false, "fieldsGroupItems": [{"type": "text-input-field", "fieldId": "text-field-id-1", "label": "text-field-label", "errors": [], "value": "", "disabled": false, "placeholder": "text-field-placeholder", "maxTextLength": 1000, "parentId": "fields-group-id"}, {"type": "three-state-level", "fieldId": "three-state-level-from-group", "label": "some label", "errors": [], "value": "not-selected", "disabled": false, "threeStateLevelOptions": [{"label": "tak", "value": "yest"}, {"label": "", "value": "not-selected"}, {"label": "nie", "value": "no"}], "parentId": "fields-group-id"}, {"type": "select-input-field", "fieldId": "select-in-group", "label": "select label", "errors": [], "value": null, "disabled": false, "placeholder": "some placeholder", "selectOptions": [{"label": "option 1", "value": "value1"}], "searchable": true, "canCleanSelect": true, "showSelectAll": false, "selectMode": "single", "openSelectDirection": "bottom", "canDeselect": true, "closeOnSelect": true, "parentId": "fields-group-id"}], "renderConditions": [], "parentId": null}, {"type": "date-picker-field", "fieldId": "datepicker-id", "label": "datepicker-label", "errors": [], "value": null, "disabled": false, "placeholder": "datepicker placeholder", "datePickerMaxDate": "", "datePickerMinDate": "", "renderConditions": [], "parentId": null, "datePickerMaxNowDate": true}, {"type": "number-input-field", "fieldId": "number-input-id", "label": "number input label", "errors": [], "value": null, "disabled": false, "placeholder": "number input placeholder", "numberInputControls": false, "numberInputSize": "default", "numberInputFixedValue": 2, "numberInputRounded": false, "renderConditions": [], "parentId": null}, {"type": "textarea-input-field", "fieldId": "text-area-input-id", "label": "text-area-input-label", "errors": [], "value": "", "disabled": false, "placeholder": "text-area-input-placeholder", "textAreaInputRows": 5, "maxTextLength": 1000, "renderConditions": [], "parentId": null}, {"type": "ckeditor-input-field", "fieldId": "ckeditor-input-id", "label": "ckeditor-input-label", "errors": [], "value": "", "disabled": false, "placeholder": "ckeditor-input-placeholder", "maxTextLength": 1000, "renderConditions": [], "parentId": null}, {"type": "fields-group-table", "fieldId": "fields-group-table-id", "label": "fields-group-table-label", "errors": [], "value": [{"first-column": "", "select-column-2": null}], "disabled": false, "fieldsGroupRowItems": [{"type": "text-input-field", "fieldId": "first-column", "label": "name of first column", "errors": [], "value": "", "disabled": false, "placeholder": "some placeholder", "maxTextLength": 1000, "parentId": "fields-group-table-id"}, {"type": "select-input-field", "fieldId": "select-column-2", "label": "name column 2", "errors": [], "value": null, "disabled": false, "placeholder": "some placeholder", "selectOptions": [{"label": "label", "value": "value"}], "searchable": false, "canCleanSelect": false, "showSelectAll": false, "selectMode": "single", "openSelectDirection": "bottom", "canDeselect": false, "closeOnSelect": true, "parentId": "fields-group-table-id"}], "renderConditions": [], "parentId": null}, {"type": "fields-group-table", "fieldId": "group-table-2", "label": "group table 2", "errors": [], "value": [{"some_column_1": "", "some_col_2": "", "select_input_col": null}], "disabled": false, "fieldsGroupRowItems": [{"type": "text-input-field", "fieldId": "some_column_1", "label": "label for col 1", "errors": [], "value": "", "disabled": false, "parentId": "group-table-2"}, {"type": "text-input-field", "fieldId": "some_col_2", "label": "col 2", "errors": [], "value": "", "disabled": false, "parentId": "group-table-2"}, {"type": "select-input-field", "fieldId": "select_input_col", "label": "select col", "errors": [], "value": null, "disabled": false, "placeholder": "some placeholder", "selectOptions": [{"label": "option 1", "value": "option_1"}], "searchable": true, "canCleanSelect": true, "showSelectAll": false, "selectMode": "single", "openSelectDirection": "bottom", "canDeselect": true, "closeOnSelect": true, "parentId": "group-table-2"}], "parentId": null}, {"type": "live-select-field", "fieldId": "live-select", "label": "live select label", "errors": [], "value": null, "disabled": false, "placeholder": "some random placeholder", "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "ICD10", "liveSelectItemsPerPage": 100, "renderConditions": [], "parentId": null}, {"value": [{"text_field_1": "", "length_id": null, "width_id": null, "thickness": null, "volume": null}], "type": "fields-group-table", "fieldId": "table_with_computed", "label": "table with computed field", "errors": [], "fieldsGroupRowItems": [{"value": "", "type": "text-input-field", "fieldId": "text_field_1", "label": "text column 1", "errors": [], "maxTextLength": 1000, "placeholder": "", "disabled": false, "parentId": "table_with_computed"}, {"value": null, "type": "number-input-field", "fieldId": "length_id", "label": "dlugosc [cm]", "errors": [], "numberInputControls": false, "numberInputFixedValue": null, "numberInputRounded": false, "numberInputSize": "default", "placeholder": "", "disabled": false, "parentId": "table_with_computed"}, {"value": null, "type": "number-input-field", "fieldId": "width_id", "label": "szerokosc [id]", "errors": [], "numberInputControls": false, "numberInputFixedValue": null, "numberInputRounded": false, "numberInputSize": "default", "placeholder": "", "disabled": false, "parentId": "table_with_computed"}, {"value": null, "type": "number-input-field", "fieldId": "thickness", "label": "grubosc [cm]", "errors": [], "numberInputControls": false, "numberInputFixedValue": null, "numberInputRounded": false, "numberInputSize": "default", "placeholder": "", "disabled": false, "parentId": "table_with_computed"}, {"value": null, "type": "computed-field", "fieldId": "volume", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "computedFieldDependentFieldsIds": ["length_id", "width_id", "thickness"], "computedFieldFixedValue": null, "disabled": false, "parentId": "table_with_computed"}], "disabled": false, "renderConditions": [], "parentId": null}, {"fieldId": "field-set", "label": "Field set label", "errors": [], "type": "fields-set", "disabled": false, "fieldsSetItems": [{"fieldId": "set_item_1", "label": "set_item_1", "errors": [], "type": "three-state-level", "disabled": false, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "parentId": "field-set"}, {"fieldId": "set_item_2", "label": "set_item_2", "errors": [], "type": "three-state-level", "disabled": false, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "parentId": "field-set"}, {"fieldId": "set_item_3", "label": "set_item_3", "errors": [], "type": "three-state-level", "disabled": false, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "parentId": "field-set"}, {"fieldId": "set_item_4", "label": "set_item_4", "errors": [], "type": "three-state-level", "disabled": false, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "set_item_4_children", "label": "set item 4 children", "errors": [], "type": "textarea-input-field", "disabled": false, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "set_item_4"}], "parentId": "set_item_4"}], "parentId": "field-set"}], "renderConditions": [], "parentId": null}, {"fieldId": "field_set_hard", "label": "field_set_hard", "errors": [], "type": "fields-set", "disabled": false, "fieldsSetItems": [{"fieldId": "field_set_hard_1", "label": "field set hard 1", "errors": [], "type": "three-state-level", "disabled": false, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "field_set_hard_1_1", "label": "field set hard 1 1", "errors": [], "type": "text-input-field", "disabled": false, "value": "", "maxTextLength": 1000, "placeholder": "", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "field_set_hard_1"}], "children": [{"fieldId": "field_set_hard_1_1_1", "label": "field set hard 1 1 1", "errors": [], "type": "text-input-field", "disabled": false, "value": "", "maxTextLength": 1000, "placeholder": "", "renderConditions": [{"conditionType": "equal", "dependentFieldId": "field_set_hard_1_1", "conditionalValue": "test"}], "children": [{"fieldId": "field_set_hard_1_1_1_1", "label": "field set hard 1 1 1 1", "errors": [], "type": "text-input-field", "disabled": false, "value": "", "maxTextLength": 1000, "placeholder": "", "renderConditions": [{"conditionalValue": "test", "conditionType": "equal", "dependentFieldId": "field_set_hard_1_1_1"}], "children": [{"fieldId": "field_set_hard_1_1_1_1_1", "label": "field set hard 1 1 1 1 1", "errors": [], "type": "text-input-field", "disabled": false, "value": "", "maxTextLength": 1000, "placeholder": "", "renderConditions": [{"conditionalValue": "test", "conditionType": "equal", "dependentFieldId": "field_set_hard_1_1_1_1"}], "children": [{"fieldId": "nested_field_set", "label": "nested field set", "errors": [], "type": "fields-set", "disabled": false, "fieldsSetItems": [{"fieldId": "9686cf3b-86e5-4edc-84e7-348eeb52e3c5", "label": "", "errors": [], "type": "three-state-level", "disabled": false, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "parentId": "nested_field_set"}, {"fieldId": "7d5fabf9-bcfb-459a-be5e-16d3c8aed66d", "label": "", "errors": [], "type": "three-state-level", "disabled": false, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "children": [{"fieldId": "57954604-c6d9-455b-bb8f-d890d7fd883f", "label": "text input", "errors": [], "type": "text-input-field", "disabled": false, "value": "", "maxTextLength": 1000, "placeholder": "", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "7d5fabf9-bcfb-459a-be5e-16d3c8aed66d"}], "parentId": "7d5fabf9-bcfb-459a-be5e-16d3c8aed66d"}], "parentId": "nested_field_set"}, {"fieldId": "15640b21-eae6-4064-85a9-3bf3f5c45287", "label": "", "errors": [], "type": "three-state-level", "disabled": false, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "parentId": "nested_field_set"}, {"fieldId": "71ff5735-5204-4c4b-8e3a-315f5084218c", "label": "", "errors": [], "type": "three-state-level", "disabled": false, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "parentId": "nested_field_set"}, {"fieldId": "9fd00729-1dd8-49a0-8a71-f1f31096a674", "label": "", "errors": [], "type": "three-state-level", "disabled": false, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "parentId": "nested_field_set"}, {"fieldId": "ba3d4e9a-f144-44b1-87a2-8f0e71ae50d8", "label": "", "errors": [], "type": "three-state-level", "disabled": false, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "parentId": "nested_field_set"}, {"fieldId": "7d3de50a-579d-4d25-b57b-a5822efedf5d", "label": "", "errors": [], "type": "three-state-level", "disabled": false, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "parentId": "nested_field_set"}], "renderConditions": [], "parentId": "field_set_hard_1_1_1_1_1"}], "parentId": "field_set_hard_1_1_1_1"}], "parentId": "field_set_hard_1_1_1"}], "parentId": "field_set_hard_1_1"}], "parentId": "field_set_hard_1"}], "parentId": "field_set_hard"}], "renderConditions": [], "parentId": null}, {"fieldId": "table_with_unique_select", "label": "table with unique select", "errors": [], "type": "fields-group-table", "disabled": false, "parentId": null, "value": [{"text_field": "", "select_protocol_id": null, "select-2": null}], "fieldsGroupRowItems": [{"fieldId": "text_field", "label": "text col", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "table_with_unique_select", "value": "", "maxTextLength": 1000, "placeholder": ""}, {"fieldId": "select_protocol_id", "label": "patient protocol", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "table_with_unique_select", "value": null, "canCleanSelect": false, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [], "placeholder": "", "selectUsePatientProtocolsAsOptions": true}, {"fieldId": "select-2", "label": "some select", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "table_with_unique_select", "value": null, "canCleanSelect": false, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "option-1", "value": "option-1"}, {"label": "option-2", "value": "option-2"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "fieldsGroupTableUniqueValuesFromSelect": ["select_protocol_id"], "renderConditions": []}]}