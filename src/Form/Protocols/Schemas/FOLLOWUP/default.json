{"formTitle": "", "sections": [{"title": "FOLLOW-UP", "rootFieldsIds": ["dateOfCompletionForm", "dateOfLastContact", "lossOfContactWithPatient", "patientStatus", "death", "date<PERSON><PERSON><PERSON><PERSON><PERSON>", "directCauseOfDeath", "secondaryCauseOfDeath", "initialCauseOfDeath"]}, {"title": "PÓŹNE EFEKTY LECZENIA", "rootFieldsIds": ["lateEffectsOfTreatment"]}], "fields": [{"fieldId": "dateOfCompletionForm", "label": "Data wypełnienia formularza", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "renderConditions": [], "datePickerMaxNowDate": true}, {"fieldId": "dateOfLastContact", "label": "Data ostatniego kontaktu z pacjentem", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "renderConditions": [], "datePickerMaxNowDate": true, "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1FXEtlpI_Q8c754wkk6mvMPPXybijxRQwynqBd-g1DJg/edit?tab=t.lbx7oftlgytq#heading=h.oy8drlrpbeaz", "tooltipWidth": 170}, {"fieldId": "lossOfContactWithPatient", "label": "Utrata kontaktu z pacjentem", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1FXEtlpI_Q8c754wkk6mvMPPXybijxRQwynqBd-g1DJg/edit?tab=t.lbx7oftlgytq#heading=h.nc65ols0na5", "tooltipWidth": 170}, {"fieldId": "patientStatus", "label": "Status pacjenta", "errors": [], "type": "fields-group-table", "disabled": false, "parentId": null, "value": [{"patientStatusJC": null, "patientStatusStatus": null}], "fieldsGroupRowItems": [{"fieldId": "patientStatusJC", "label": "Jednostka chorobowa", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "patientStatus", "value": null, "canCleanSelect": false, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [], "placeholder": "", "selectUsePatientProtocolsAsOptions": true, "fieldWidth": 300, "selectUsePatientProtocolsAsOptionsOnlyActive": true}, {"fieldId": "patientStatusStatus", "label": "Status", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "patientStatus", "value": null, "canCleanSelect": false, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Całkowita remisja", "value": "ST-01"}, {"label": "Częściowa remisja", "value": "ST-02"}, {"label": "Stabilna choroba", "value": "ST-03"}, {"label": "Progresja", "value": "ST-04"}, {"label": "Wznowa", "value": "ST-05"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "fieldWidth": 120}], "fieldsGroupTableUniqueValuesFromSelect": ["patientStatusJC"], "renderConditions": [], "fieldWidth": 980, "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1FXEtlpI_Q8c754wkk6mvMPPXybijxRQwynqBd-g1DJg/edit?tab=t.lbx7oftlgytq#heading=h.7blye1v3veu", "tooltipWidth": 170}, {"fieldId": "lateEffectsOfTreatment", "label": "", "errors": [], "type": "fields-set", "disabled": false, "parentId": null, "fieldsSetItems": [{"fieldId": "cardiovascularSystem", "label": "Układ sercowo-naczyniowy", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "lateEffectsOfTreatment", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "fieldLabelWidth": 250, "children": [{"fieldId": "cardiovascularSystemTableGroup", "label": "", "errors": [], "type": "fields-group-table", "disabled": false, "parentId": "cardiovascularSystem", "value": [{"cardiovascularSystemProblem": null, "cardiovascularSystemProblemDate": null}], "fieldsGroupRowItems": [{"fieldId": "cardiovascularSystemProblem", "label": "Problem zdrowotny", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "cardiovascularSystemTableGroup", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Kardiomiopatia", "value": "cardiovascularSystemProblem1"}, {"label": "Subkliniczna dysfunkcja lewej komory", "value": "cardiovascularSystemProblem2"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> serca", "value": "cardiovascularSystemProblem3"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "cardiovascularSystemProblem4"}, {"label": "Powikłania naczyniowe mózgu (udar, choro<PERSON>oya, zatorowość naczyń mózgowych)", "value": "cardiovascularSystemProblem5"}, {"label": "Choroba tętnicy szyjnej", "value": "cardiovascularSystemProblem6"}, {"label": "Choroba tętnicy podobojczykowej", "value": "cardiovascularSystemProblem7"}, {"label": "Zapalenie osierdzia", "value": "cardiovascularSystemProblem8"}, {"label": "Zwłóknienie osierdzia", "value": "cardiovascularSystemProblem9"}, {"label": "<PERSON><PERSON>ba <PERSON>awek, dysfunkcja", "value": "cardiovascularSystemProblem10"}, {"label": "Miażdżycowa choroba serca", "value": "cardiovascularSystemProblem11"}, {"label": "Z<PERSON>ł mięśnia sercowego", "value": "cardiovascularSystemProblem12"}, {"label": "Powikłania naczyniowe po amputacjach - zakrzepica, niewydolność naczyniowa, infekcje zespół po zakrzepowy", "value": "cardiovascularSystemProblem13"}], "placeholder": "", "fieldWidth": 980, "selectUsePatientProtocolsAsOptions": false, "selectUsePatientProtocolsAsOptionsOnlyActive": false}, {"fieldId": "cardiovascularSystemProblemDate", "label": "Data", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "cardiovascularSystemTableGroup", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "fieldWidth": 350}], "fieldsGroupTableUniqueValuesFromSelect": ["cardiovascularSystemProblem"], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "cardiovascularSystem", "conditionalValue": "yes"}]}]}, {"fieldId": "nervousSystem", "label": "System nerwowy", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "lateEffectsOfTreatment", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "fieldLabelWidth": 250, "children": [{"fieldId": "nervousSystemTableGroup", "label": "", "errors": [], "type": "fields-group-table", "disabled": false, "parentId": "nervousSystem", "value": [{"nervousSystemProblem": null, "nervousSystemProblemDate": null}], "fieldsGroupRowItems": [{"fieldId": "nervousSystemProblem", "label": "Problem zdrowotny", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "nervousSystemTableGroup", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Obwodowa neuropatia czuciowa", "value": "nervousSystemProblem1"}, {"label": "Parestazje/dyzestezje", "value": "nervousSystemProblem2"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "nervousSystemProblem3"}, {"label": "<PERSON><PERSON><PERSON>", "value": "nervousSystemProblem4"}, {"label": "Dyzartria", "value": "nervousSystemProblem5"}, {"label": "Dysfagia", "value": "nervousSystemProblem6"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "nervousSystemProblem7"}, {"label": "Tetrapareza", "value": "nervousSystemProblem8"}, {"label": "<PERSON><PERSON><PERSON>", "value": "nervousSystemProblem9"}, {"label": "Leukoencefalopatia", "value": "nervousSystemProblem10"}, {"label": "Wodogłowie", "value": "nervousSystemProblem11"}, {"label": "Porażanie/Niedowład", "value": "nervousSystemProblem12"}, {"label": "Zab<PERSON><PERSON><PERSON> ruchowe", "value": "nervousSystemProblem13"}], "placeholder": "", "fieldWidth": 980, "selectUsePatientProtocolsAsOptions": false, "selectUsePatientProtocolsAsOptionsOnlyActive": false}, {"fieldId": "nervousSystemProblemDate", "label": "Data", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "nervousSystemTableGroup", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "fieldWidth": 300}], "fieldsGroupTableUniqueValuesFromSelect": ["nervousSystemProblem"], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "nervousSystem", "conditionalValue": "yes"}]}]}, {"fieldId": "endocrineSystem", "label": "System endokrynologiczny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "lateEffectsOfTreatment", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "fieldLabelWidth": 250, "children": [{"fieldId": "endocrineSystemTableGroup", "label": "", "errors": [], "type": "fields-group-table", "disabled": false, "parentId": "endocrineSystem", "value": [{"endocrineSystemProblem": null, "endocrineSystemProblemDate": null}], "fieldsGroupRowItems": [{"fieldId": "endocrineSystemProblem", "label": "Problem zdrowotny", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "endocrineSystemTableGroup", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tarczycy", "value": "endocrineSystemProblem1"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tarczycy", "value": "endocrineSystemProblem2"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "endocrineSystemProblem3"}, {"label": "Niedobór hormonu wzrostu", "value": "endocrineSystemProblem4"}, {"label": "Hiperprolaktynemia", "value": "endocrineSystemProblem5"}, {"label": "Niedobór LH", "value": "endocrineSystemProblem6"}, {"label": "Niedobór FSH", "value": "endocrineSystemProblem7"}, {"label": "Centralna niewydolność nadnerczy", "value": "endocrineSystemProblem8"}, {"label": "Wielohormonalna <PERSON> przysadki", "value": "endocrineSystemProblem9"}, {"label": "Nieprawidłowa glikemia na czczo", "value": "endocrineSystemProblem10"}, {"label": "Cukrzyca", "value": "endocrineSystemProblem11"}, {"label": "Nadwaga", "value": "endocrineSystemProblem12"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "endocrineSystemProblem13"}], "placeholder": "", "fieldWidth": 980, "selectUsePatientProtocolsAsOptions": false, "selectUsePatientProtocolsAsOptionsOnlyActive": false}, {"fieldId": "endocrineSystemProblemDate", "label": "Data", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "endocrineSystemTableGroup", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "fieldWidth": 300}], "fieldsGroupTableUniqueValuesFromSelect": ["endocrineSystemProblem"], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "endocrineSystem", "conditionalValue": "yes"}]}]}, {"fieldId": "respiratorySystem", "label": "<PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "lateEffectsOfTreatment", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "fieldLabelWidth": 250, "children": [{"fieldId": "respiratorySystemTableGroup", "label": "", "errors": [], "type": "fields-group-table", "disabled": false, "parentId": "respiratorySystem", "value": [{"respiratorySystemProblem": null, "respiratorySystemProblemDate": null}], "fieldsGroupRowItems": [{"fieldId": "respiratorySystemProblem", "label": "Problem zdrowotny", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "respiratorySystemTableGroup", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Przewlekłe zapalenie zatok", "value": "respiratorySystemProblem1"}, {"label": "Zwłóknienie płuc", "value": "respiratorySystemProblem2"}, {"label": "Śródmiąższowe zapalenie płuc", "value": "respiratorySystemProblem3"}, {"label": "Zespół ostrej niewydolności oddechowej (ARDS)", "value": "respiratorySystemProblem4"}, {"label": "Choroba restrykcyjna płuc", "value": "respiratorySystemProblem5"}, {"label": "Przewlekła obturacyjna choroba płuc", "value": "respiratorySystemProblem6"}, {"label": "Obliteracyjne zapalenie oskrzelików", "value": "respiratorySystemProblem7"}, {"label": "Przewlekłe zapalenie oskrzeli", "value": "respiratorySystemProblem8"}, {"label": "Rozstrzenie oskrzeli", "value": "respiratorySystemProblem9"}], "placeholder": "", "fieldWidth": 980, "selectUsePatientProtocolsAsOptions": false, "selectUsePatientProtocolsAsOptionsOnlyActive": false}, {"fieldId": "respiratorySystemProblemDate", "label": "Data", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "respiratorySystemTableGroup", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "fieldWidth": 300}], "fieldsGroupTableUniqueValuesFromSelect": ["respiratorySystemProblem"], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "respiratorySystem", "conditionalValue": "yes"}]}]}, {"fieldId": "digestiveSystem", "label": "<PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "lateEffectsOfTreatment", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "fieldLabelWidth": 250, "children": [{"fieldId": "digestiveSystemTableGroup", "label": "", "errors": [], "type": "fields-group-table", "disabled": false, "parentId": "digestiveSystem", "value": [{"digestiveSystemProblem": null, "digestiveSystemProblemDate": null}], "fieldsGroupRowItems": [{"fieldId": "digestiveSystemProblem", "label": "Problem zdrowotny", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "digestiveSystemTableGroup", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Zwężenie przełyku", "value": "digestiveSystemProblem1"}, {"label": "<PERSON><PERSON><PERSON><PERSON> w jamie ustnej (kserostomia)", "value": "digestiveSystemProblem2"}, {"label": "Dysfunkcja gruczołów ślinowych", "value": "digestiveSystemProblem3"}, {"label": "Przewlekłe zapalenie ślinianek", "value": "digestiveSystemProblem4"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>", "value": "digestiveSystemProblem5"}, {"label": "Przewlekłe zapalenie jelit", "value": "digestiveSystemProblem6"}, {"label": "Przeto<PERSON> j<PERSON>", "value": "digestiveSystemProblem7"}, {"label": "Zwężenie jelit", "value": "digestiveSystemProblem8"}, {"label": "<PERSON><PERSON><PERSON> neurogenne", "value": "digestiveSystemProblem9"}, {"label": "Nietrzymanie stolca", "value": "digestiveSystemProblem10"}, {"label": "Niedobór witamin (witaminy B12, folian<PERSON>, β-karotenu) u pacjentów po enterocystoplastyce", "value": "digestiveSystemProblem11"}, {"label": "Dyslipidemia", "value": "digestiveSystemProblem12"}], "placeholder": "", "fieldWidth": 980, "selectUsePatientProtocolsAsOptions": false, "selectUsePatientProtocolsAsOptionsOnlyActive": false}, {"fieldId": "digestiveSystemProblemDate", "label": "Data", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "digestiveSystemTableGroup", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "fieldWidth": 300}], "fieldsGroupTableUniqueValuesFromSelect": ["digestiveSystemProblem"], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "digestiveSystem", "conditionalValue": "yes"}]}]}, {"fieldId": "urinaryTract", "label": "<PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "lateEffectsOfTreatment", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "fieldLabelWidth": 250, "children": [{"fieldId": "urinaryTractTableGroup", "label": "", "errors": [], "type": "fields-group-table", "disabled": false, "parentId": "urinaryTract", "value": [{"urinaryTractProblem": null, "urinaryTractProblemDate": null}], "fieldsGroupRowItems": [{"fieldId": "urinaryTractProblem", "label": "Problem zdrowotny", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "urinaryTractTableGroup", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Krwotoczne zapalenie pęcherza", "value": "urinaryTractProblem1"}, {"label": "Włóknienie pęcherza moczowego", "value": "urinaryTractProblem2"}, {"label": "Zaburzenia mik<PERSON>", "value": "urinaryTractProblem3"}, {"label": "Refluks pęcherzowo-moczowodowy", "value": "urinaryTractProblem4"}, {"label": "Kłębuszkowe zapalenie nerek", "value": "urinaryTractProblem5"}, {"label": "Uszkodzenie cewek nerkowych (kwasica kanalikowa nerkowa, zespół Fanconiego, krzywica hipofosfatemiczna)", "value": "urinaryTractProblem6"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nerek", "value": "urinaryTractProblem7"}, {"label": "Białkomocz", "value": "urinaryTractProblem8"}, {"label": "Hiperfiltracja", "value": "urinaryTractProblem9"}, {"label": "Zaburzenia funkcji dna miednicy (nietrzymanie moczu)", "value": "urinaryTractProblem10"}, {"label": "Bezobjawowy bakteriomocz", "value": "urinaryTractProblem11"}, {"label": "Przewlekłe zakażenie dróg m<PERSON>zowych", "value": "urinaryTractProblem12"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "urinaryTractProblem13"}, {"label": "<PERSON><PERSON><PERSON> nerk<PERSON>e", "value": "urinaryTractProblem14"}, {"label": "Spontaniczne perforacje neopęcherza (pęcherz jelitowy)", "value": "urinaryTractProblem15"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>", "value": "urinaryTractProblem16"}, {"label": "Obtura<PERSON>ja dr<PERSON><PERSON>", "value": "urinaryTractProblem17"}, {"label": "Moczówka prosta", "value": "urinaryTractProblem18"}], "placeholder": "", "fieldWidth": 980, "selectUsePatientProtocolsAsOptions": false, "selectUsePatientProtocolsAsOptionsOnlyActive": false}, {"fieldId": "urinaryTractProblemDate", "label": "Data", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "urinaryTractTableGroup", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "fieldWidth": 300}], "fieldsGroupTableUniqueValuesFromSelect": ["urinaryTractProblem"], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "urinaryTract", "conditionalValue": "yes"}]}]}, {"fieldId": "musculoskeletalSystem", "label": "Układ <PERSON>-szkieletowy", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "lateEffectsOfTreatment", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "fieldLabelWidth": 250, "children": [{"fieldId": "musculoskeletalSystemTableGroup", "label": "", "errors": [], "type": "fields-group-table", "disabled": false, "parentId": "musculoskeletalSystem", "value": [{"musculoskeletalSystemProblem": null, "musculoskeletalSystemProblemDate": null}], "fieldsGroupRowItems": [{"fieldId": "musculoskeletalSystemProblem", "label": "Problem zdrowotny", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "musculoskeletalSystemTableGroup", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Obniżona gęstość mineralna k<PERSON> (BMD)", "value": "musculoskeletalSystemProblem1"}, {"label": "Martwica jałowa kości", "value": "musculoskeletalSystemProblem2"}, {"label": "Niedorozwój układu mi<PERSON>śniowo-szkieletowego", "value": "musculoskeletalSystemProblem3"}, {"label": "Zwłóknienie", "value": "musculoskeletalSystemProblem4"}, {"label": "Opóźniony wzrost/ dysproporcja wzrostu", "value": "musculoskeletalSystemProblem5"}, {"label": "Skrócenie wysokości tułowia", "value": "musculoskeletalSystemProblem6"}, {"label": "Różnica długości kończyn", "value": "musculoskeletalSystemProblem7"}, {"label": "Skolioza/Kifoza", "value": "musculoskeletalSystemProblem8"}, {"label": "<PERSON><PERSON><PERSON><PERSON> radia<PERSON>e", "value": "musculoskeletalSystemProblem9"}, {"label": "Kontraktury stawów", "value": "musculoskeletalSystemProblem10"}, {"label": "Zniekształcenia wyglądu", "value": "musculoskeletalSystemProblem11"}, {"label": "Ograniczenia funkcjonalne i aktywności", "value": "musculoskeletalSystemProblem12"}, {"label": "Przewlekły ból", "value": "musculoskeletalSystemProblem13"}, {"label": "Dysfunkcja protezy (rozluźnienie, brak zrostu, złamanie) wymagająca rewizji, wymiany lub amputacji", "value": "musculoskeletalSystemProblem14"}], "placeholder": "", "fieldWidth": 980, "selectUsePatientProtocolsAsOptions": false, "selectUsePatientProtocolsAsOptionsOnlyActive": false}, {"fieldId": "musculoskeletalSystemProblemDate", "label": "Data", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "musculoskeletalSystemTableGroup", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "fieldWidth": 300}], "fieldsGroupTableUniqueValuesFromSelect": ["musculoskeletalSystemProblem"], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "musculoskeletalSystem", "conditionalValue": "yes"}]}]}, {"fieldId": "genitalSystem", "label": "Uk<PERSON><PERSON> hormon<PERSON> (Płciowy)", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "lateEffectsOfTreatment", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "fieldLabelWidth": 250, "children": [{"fieldId": "genitalSystemTableGroup", "label": "", "errors": [], "type": "fields-group-table", "disabled": false, "parentId": "genitalSystem", "value": [{"genitalSystemProblem": null, "genitalSystemProblemDate": null}], "fieldsGroupRowItems": [{"fieldId": "genitalSystemProblem", "label": "Problem zdrowotny", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "genitalSystemTableGroup", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Przedwczesne dojrzewanie", "value": "genitalSystemProblem1"}, {"label": "Dysfunkcje hormonalne jąder", "value": "genitalSystemProblem2"}, {"label": "Dysfunkcje hormonalne jajników", "value": "genitalSystemProblem3"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "genitalSystemProblem4"}, {"label": "Opóźnione/Zatrzymane dojrzewanie", "value": "genitalSystemProblem5"}, {"label": "Upośledzenie spermatogenezy (obniżona płodność, oligospermia, azoospermia, bezpłodność)", "value": "genitalSystemProblem6"}, {"label": "Przedwczesna niewydolność jajników", "value": "genitalSystemProblem7"}, {"label": "Przedwczesna menopauza", "value": "genitalSystemProblem8"}, {"label": "Zmniejszona rezerwa jajnikowa (POF/POI)", "value": "genitalSystemProblem9"}, {"label": "Niedorozwój tkanki gruczołu piersiowego", "value": "genitalSystemProblem10"}], "placeholder": "", "fieldWidth": 980, "selectUsePatientProtocolsAsOptions": false, "selectUsePatientProtocolsAsOptionsOnlyActive": false}, {"fieldId": "genitalSystemProblemDate", "label": "Data", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "genitalSystemTableGroup", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "fieldWidth": 300}], "fieldsGroupTableUniqueValuesFromSelect": ["genitalSystemProblem"], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "genitalSystem", "conditionalValue": "yes"}]}]}, {"fieldId": "reproductiveSystem", "label": "<PERSON>k<PERSON><PERSON>oz<PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "lateEffectsOfTreatment", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "fieldLabelWidth": 250, "children": [{"fieldId": "reproductiveSystemTableGroup", "label": "", "errors": [], "type": "fields-group-table", "disabled": false, "parentId": "reproductiveSystem", "value": [{"reproductiveSystemProblem": null, "reproductiveSystemProblemDate": null}], "fieldsGroupRowItems": [{"fieldId": "reproductiveSystemProblem", "label": "Problem zdrowotny", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "reproductiveSystemTableGroup", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Zwłóknienie pochwy /stenoza pochwy", "value": "reproductiveSystemProblem1"}, {"label": "Niewydolność naczyniowa maciczno-łożyskowa", "value": "reproductiveSystemProblem2"}, {"label": "Zaburzenia funkcji seksualnej związane z dysfunkcją dna miednicy", "value": "reproductiveSystemProblem3"}, {"label": "Dyspareunia (ból podczas stosunku)", "value": "reproductiveSystemProblem4"}, {"label": "Torbiele jajników powodujące dolegliwości", "value": "reproductiveSystemProblem5"}, {"label": "Blizny sromu", "value": "reproductiveSystemProblem6"}, {"label": "Zaburzenia er<PERSON>ji", "value": "reproductiveSystemProblem7"}, {"label": "Zaburzenia anatomiczne funkcji seksualnych (wytrysk wsteczny, brak wytrysku, azoospermia obturacyjna)", "value": "reproductiveSystemProblem8"}, {"label": "Niepłod<PERSON>ść przy zachowanej funkcji jajników", "value": "reproductiveSystemProblem9"}, {"label": "Niepłod<PERSON>ść przy prawidłowej funkcji jajników", "value": "reproductiveSystemProblem10"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "reproductiveSystemProblem11"}, {"label": "Wodniak j<PERSON>", "value": "reproductiveSystemProblem12"}], "placeholder": "", "fieldWidth": 980, "selectUsePatientProtocolsAsOptions": false, "selectUsePatientProtocolsAsOptionsOnlyActive": false}, {"fieldId": "reproductiveSystemProblemDate", "label": "Data", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "reproductiveSystemTableGroup", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "fieldWidth": 300}], "fieldsGroupTableUniqueValuesFromSelect": ["reproductiveSystemProblem"], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "reproductiveSystem", "conditionalValue": "yes"}]}]}, {"fieldId": "liver", "label": "Wątroba", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "lateEffectsOfTreatment", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "fieldLabelWidth": 250, "children": [{"fieldId": "liverTableGroup", "label": "", "errors": [], "type": "fields-group-table", "disabled": false, "parentId": "liver", "value": [{"liverProblem": null, "liverProblemDate": null}], "fieldsGroupRowItems": [{"fieldId": "liverProblem", "label": "Problem zdrowotny", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "liverTableGroup", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Zespół niedrożności zatok żylnych wątrobowy (SOS, sinusoidal obstruction syndorme)", "value": "liverProblem1"}, {"label": "Przewlekłe zapalenie wątroby", "value": "liverProblem2"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "liverProblem3"}, {"label": "Hemosyderoza", "value": "liverProblem4"}, {"label": "Kamica żółciowa", "value": "liverProblem5"}, {"label": "Zwłóknienie wątroby", "value": "liverProblem6"}, {"label": "Ogniskowy rozrost guzkowy (FNH – focal nodular hyperplasia)", "value": "liverProblem7"}], "placeholder": "", "fieldWidth": 980, "selectUsePatientProtocolsAsOptions": false, "selectUsePatientProtocolsAsOptionsOnlyActive": false}, {"fieldId": "liverProblemDate", "label": "Data", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "liverTableGroup", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "fieldWidth": 300}], "fieldsGroupTableUniqueValuesFromSelect": ["liverProblem"], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "liver", "conditionalValue": "yes"}]}]}, {"fieldId": "skin", "label": "Skóra", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "lateEffectsOfTreatment", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "fieldLabelWidth": 250, "children": [{"fieldId": "skinTableGroup", "label": "", "errors": [], "type": "fields-group-table", "disabled": false, "parentId": "skin", "value": [{"skinProblem": null, "skinProblemDate": null}], "fieldsGroupRowItems": [{"fieldId": "skinProblem", "label": "Problem zdrowotny", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "skinTableGroup", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Łysienie", "value": "skinProblem1"}, {"label": "Dystrofia paznokci", "value": "skinProblem2"}, {"label": "Bielactwo (vitiligo)", "value": "skinProblem3"}, {"label": "Zmiany skórne w przebiegu twardziny skóry", "value": "skinProblem4"}, {"label": "Zaburzenia pigmentacji skóry", "value": "skinProblem5"}, {"label": "Teleangiektazje", "value": "skinProblem6"}, {"label": "Włóknienie skóry", "value": "skinProblem7"}], "placeholder": "", "fieldWidth": 980, "selectUsePatientProtocolsAsOptions": false, "selectUsePatientProtocolsAsOptionsOnlyActive": false}, {"fieldId": "skinProblemDate", "label": "Data", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "skinTableGroup", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "fieldWidth": 300}], "fieldsGroupTableUniqueValuesFromSelect": ["skinProblem"], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "skin", "conditionalValue": "yes"}]}]}, {"fieldId": "hearing", "label": "Słuch", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "lateEffectsOfTreatment", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "fieldLabelWidth": 250, "children": [{"fieldId": "hearingTableGroup", "label": "", "errors": [], "type": "fields-group-table", "disabled": false, "parentId": "hearing", "value": [{"hearingProblem": null, "hearingProblemDate": null}], "fieldsGroupRowItems": [{"fieldId": "hearingProblem", "label": "Problem zdrowotny", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "hearingTableGroup", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Niedosłuch typu czuciowo-nerwowego", "value": "hearingProblem1"}, {"label": "Sklerotyzacja błony bębenkowej", "value": "hearingProblem2"}, {"label": "<PERSON><PERSON>kle<PERSON><PERSON>", "value": "hearingProblem3"}, {"label": "Dysfunkcja trąbki słuchowej", "value": "hearingProblem4"}, {"label": "Niedosłuch przewodzeniowy", "value": "hearingProblem5"}, {"label": "<PERSON><PERSON><PERSON> w us<PERSON>h", "value": "hearingProblem6"}, {"label": "Zawroty głowy", "value": "hearingProblem7"}], "placeholder": "", "fieldWidth": 980, "selectUsePatientProtocolsAsOptions": false, "selectUsePatientProtocolsAsOptionsOnlyActive": false}, {"fieldId": "hearingProblemDate", "label": "Data", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "hearingTableGroup", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "fieldWidth": 300}], "fieldsGroupTableUniqueValuesFromSelect": ["hearingProblem"], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "hearing", "conditionalValue": "yes"}]}]}, {"fieldId": "eyes", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "lateEffectsOfTreatment", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "fieldLabelWidth": 250, "children": [{"fieldId": "eyesTableGroup", "label": "", "errors": [], "type": "fields-group-table", "disabled": false, "parentId": "eyes", "value": [{"eyesProblem": null, "eyesProblemDate": null}], "fieldsGroupRowItems": [{"fieldId": "eyesProblem", "label": "Problem zdrowotny", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "eyesTableGroup", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "eyesProblem1"}, {"label": "<PERSON><PERSON>", "value": "eyesProblem2"}, {"label": "Atrezja przewodów łzowych", "value": "eyesProblem3"}, {"label": "Zespół suchego oka", "value": "eyesProblem4"}, {"label": "Zapalenie rogówki", "value": "eyesProblem5"}, {"label": "Teleangiektazje", "value": "eyesProblem6"}, {"label": "Retinopatia", "value": "eyesProblem7"}, {"label": "Neuropatia skrzyżowania nerwu wzrokowego", "value": "eyesProblem8"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "eyesProblem9"}, {"label": "Przewlekły ból gałki ocznej", "value": "eyesProblem10"}, {"label": "Choroby plamki żółtej", "value": "eyesProblem11"}, {"label": "Neuropatia nerwu wzrokowego", "value": "eyesProblem12"}, {"label": "Jaskra", "value": "eyesProblem13"}, {"label": "Porażenie nerwu wzrokowego", "value": "eyesProblem14"}, {"label": "Zaburzenia ruchomości gałek ocznych", "value": "eyesProblem15"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "eyesProblem16"}, {"label": "Obrzęk tarczy nerwu wzrokowego", "value": "eyesProblem17"}, {"label": "Zanik nerwu wzrokowego", "value": "eyesProblem18"}], "placeholder": "", "fieldWidth": 980, "selectUsePatientProtocolsAsOptions": false, "selectUsePatientProtocolsAsOptionsOnlyActive": false}, {"fieldId": "eyesProblemDate", "label": "Data", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "eyesTableGroup", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "fieldWidth": 300}], "fieldsGroupTableUniqueValuesFromSelect": ["eyesProblem"], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "eyes", "conditionalValue": "yes"}]}]}, {"fieldId": "dental", "label": "Stomatologiczne", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "lateEffectsOfTreatment", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "fieldLabelWidth": 250, "children": [{"fieldId": "dentalTableGroup", "label": "", "errors": [], "type": "fields-group-table", "disabled": false, "parentId": "dental", "value": [{"dentalProblem": null, "dentalProblemDate": null}], "fieldsGroupRowItems": [{"fieldId": "dentalProblem", "label": "Problem zdrowotny", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "dentalTableGroup", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Brak zębów i/lub korzeni", "value": "dentalProblem1"}, {"label": "Zbyt małe zęby", "value": "dentalProblem2"}, {"label": "Wczesne wypadanie zębów", "value": "dentalProblem3"}, {"label": "Dysplazja szkliwa", "value": "dentalProblem4"}, {"label": "Próchnica zębów", "value": "dentalProblem5"}, {"label": "Choroby przyzębia", "value": "dentalProblem6"}, {"label": "Wada zgryzu (otwarty, głęboki, przewieszony, przodozgryz, tyłozgryz, krzyżowy)", "value": "dentalProblem7"}, {"label": "Dysfunkcja stawu skroniowo-żuchwowego", "value": "dentalProblem8"}, {"label": "Martwica kości promieniowej szczęki i/lub żuchwy", "value": "dentalProblem9"}, {"label": "Inne zmiany np. skrócenie lub zwężenie korzeni, mikrodontia, ektopowe wyrzynanie, nie wypadnięcie zębów mlecznych", "value": "dentalProblem10"}], "placeholder": "", "fieldWidth": 980, "selectUsePatientProtocolsAsOptions": false, "selectUsePatientProtocolsAsOptionsOnlyActive": false}, {"fieldId": "dentalProblemDate", "label": "Data", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "dentalTableGroup", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "fieldWidth": 300}], "fieldsGroupTableUniqueValuesFromSelect": ["dentalProblem"], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "dental", "conditionalValue": "yes"}]}]}, {"fieldId": "immunologicalComplications", "label": "Powikłania immunologiczne", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "lateEffectsOfTreatment", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "fieldLabelWidth": 250, "children": [{"fieldId": "immunologicalComplicationsTableGroup", "label": "", "errors": [], "type": "fields-group-table", "disabled": false, "parentId": "immunologicalComplications", "value": [{"immunologicalComplicationsProblem": null, "immunologicalComplicationsProblemDate": null}], "fieldsGroupRowItems": [{"fieldId": "immunologicalComplicationsProblem", "label": "Problem zdrowotny", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "immunologicalComplicationsTableGroup", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Niedobór IgA", "value": "immunologicalComplicationsProblem1"}, {"label": "Hipogammaglobulinemia", "value": "immunologicalComplicationsProblem2"}, {"label": "Obniżona liczba limfocytów B", "value": "immunologicalComplicationsProblem3"}, {"label": "Dysfunkcja limfocytów T", "value": "immunologicalComplicationsProblem4"}, {"label": "Przewlekłe infekcje (zapalenie spojówek, zapalenie zatok, zapalenie oskrzeli, inne)", "value": "immunologicalComplicationsProblem5"}], "placeholder": "", "fieldWidth": 980, "selectUsePatientProtocolsAsOptions": false, "selectUsePatientProtocolsAsOptionsOnlyActive": false}, {"fieldId": "immunologicalComplicationsProblemDate", "label": "Data", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "immunologicalComplicationsTableGroup", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "fieldWidth": 300}], "fieldsGroupTableUniqueValuesFromSelect": ["immunologicalComplicationsProblem"], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "immunologicalComplications", "conditionalValue": "yes"}]}]}, {"fieldId": "psychosocial", "label": "Psychospołeczne", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "lateEffectsOfTreatment", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "fieldLabelWidth": 250, "children": [{"fieldId": "psychosocialTableGroup", "label": "", "errors": [], "type": "fields-group-table", "disabled": false, "parentId": "psychosocial", "value": [{"psychosocialProblem": null, "psychosocialProblemDate": null}], "fieldsGroupRowItems": [{"fieldId": "psychosocialProblem", "label": "Problem zdrowotny", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "psychosocialTableGroup", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Wycofanie s<PERSON>z<PERSON>, problemy w związkach, problemy z zatrudnieniem", "value": "psychosocialProblem1"}, {"label": "<PERSON><PERSON> ed<PERSON>", "value": "psychosocialProblem2"}, {"label": "Niesamodzielność - zależny od wsparcia innych", "value": "psychosocialProblem3"}, {"label": "<PERSON><PERSON><PERSON><PERSON>, l<PERSON><PERSON>, stres pourazowy", "value": "psychosocialProblem4"}, {"label": "Zachowania samobójcze/ryzykowne - zwiększające prawdopodobieństwo wystąpienia choroby lub urazu", "value": "psychosocialProblem5"}, {"label": "Psychospołeczna niepełnosprawność z powodu bólu", "value": "psychosocialProblem6"}, {"label": "Problemy ze snem", "value": "psychosocialProblem7"}, {"label": "Deficyty neurokognitywne (funk<PERSON>jonalne, w nauce matematyki i czytania; obniżony IQ) i zmiany w zachowaniu", "value": "psychosocialProblem8"}], "placeholder": "", "fieldWidth": 980, "selectUsePatientProtocolsAsOptions": false, "selectUsePatientProtocolsAsOptionsOnlyActive": false}, {"fieldId": "psychosocialProblemDate", "label": "Data", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "psychosocialTableGroup", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "fieldWidth": 300}], "fieldsGroupTableUniqueValuesFromSelect": ["psychosocialProblem"], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "psychosocial", "conditionalValue": "yes"}]}]}], "renderConditions": [], "fieldLabelWidth": 250, "fieldWidth": 980}, {"fieldId": "date<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "Data zgonu", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "death"}], "datePickerMaxNowDate": true}, {"fieldId": "directCauseOfDeath", "label": "Bezpośrednia przyczyna zgonu", "errors": [], "type": "live-select-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "ICD_DEATHS", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "death"}]}, {"fieldId": "secondaryCauseOfDeath", "label": "Wtórna przyczna zgonu", "errors": [], "type": "live-select-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "ICD_DEATHS", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "death"}]}, {"fieldId": "initialCauseOfDeath", "label": "Wyjściowa przyczyna zgonu", "errors": [], "type": "live-select-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "ICD_DEATHS", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "death"}]}, {"fieldId": "death", "label": "Zgon", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": []}]}