{"formTitle": "", "sections": [{"title": "WZNOWY", "rootFieldsIds": ["firstRelapse", "secondRelapse", "thirdRelapse", "fourthRelapse", "fifthRelapse"]}, {"title": "KOMENTARZE", "rootFieldsIds": ["relapseComment"]}], "fields": [{"fieldId": "firstRelapse", "label": "Pierwsza wznowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "firstRelapseGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "firstRelapse", "fieldsGroupItems": [{"fieldId": "firstRelapseDate", "label": "Data rozpoznania wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "firstRelapseGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "firstRelapseType", "label": "Typ wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "firstRelapseGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "Lokalna"}, {"label": "Odległa", "value": "Odległa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}, {"fieldId": "firstRelapseChemotherapy", "label": "Chemioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "firstRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}], "children": [{"fieldId": "firstRelapseChemotherapyType", "label": "<PERSON><PERSON><PERSON>emioterapi<PERSON>", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "firstRelapseChemotherapy", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "NBL-CHEM-01", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapseChemotherapy"}], "fieldWidth": 300}]}, {"fieldId": "firstRelapseRadiotherapy", "label": "Radioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "firstRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}], "children": [{"fieldId": "firstRelapseRadiotherapyGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "firstRelapseRadiotherapy", "fieldsGroupItems": [{"fieldId": "firstRelapseRadiotherapyDose", "label": "Dawka", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "firstRelapseRadiotherapyGroup1", "value": "", "maxTextLength": 1000, "placeholder": ""}, {"fieldId": "firstRelapseRadiotherapyLocation", "label": "Lokalizacja napromieniania", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "firstRelapseRadiotherapyGroup1", "value": "", "maxTextLength": 1000, "placeholder": ""}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapseRadiotherapy"}], "fieldsGroupColumns": 2, "fieldWidth": 1000}]}, {"fieldId": "firstRelapseSurgery", "label": "Chirurgia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "firstRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}]}, {"fieldId": "firstRelapseHsct", "label": "Przeszczep", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "firstRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}], "children": [{"fieldId": "firstRelapseHsctGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "firstRelapseHsct", "fieldsGroupItems": [{"fieldId": "firstRelapseHsctDate", "label": "Data przeszczepu", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "firstRelapseHsctGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "firstRelapseHsctType", "label": "Typ przeszczepu", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "firstRelapseHsctGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Auto", "value": "Auto"}, {"label": "Allo", "value": "Allo"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapseHsct"}], "fieldWidth": 850, "fieldsGroupColumns": 4}]}, {"fieldId": "firstRelapseGroup2", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "firstRelapse", "fieldsGroupItems": [{"fieldId": "firstRelapseTreatmentEndDate", "label": "Data zakończenia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "firstRelapseGroup2", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "firstRelapseTreatmentResponse", "label": "Odpowiedź na leczenie wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "firstRelapseGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "CR", "value": "CR"}, {"label": "Progresja", "value": "Progresja"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}]}, {"fieldId": "secondRelapse", "label": "Druga wznowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "firstRelapse"}], "children": [{"fieldId": "secondRelapseGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "secondRelapse", "fieldsGroupItems": [{"fieldId": "secondRelapseDate", "label": "Data rozpoznania wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "secondRelapseGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "secondRelapseType", "label": "Typ wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "secondRelapseGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "Lokalna"}, {"label": "Odległa", "value": "Odległa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}, {"fieldId": "secondRelapseChemotherapy", "label": "Chemioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "secondRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}], "children": [{"fieldId": "secondRelapseChemotherapyType", "label": "<PERSON><PERSON><PERSON>emioterapi<PERSON>", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "secondRelapseChemotherapy", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "NBL-CHEM-01", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapseChemotherapy"}], "fieldWidth": 300}]}, {"fieldId": "secondRelapseRadiotherapy", "label": "Radioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "secondRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}], "children": [{"fieldId": "secondRelapseRadiotherapyGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "secondRelapseRadiotherapy", "fieldsGroupItems": [{"fieldId": "secondRelapseRadiotherapyDose", "label": "Dawka", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "secondRelapseRadiotherapyGroup1", "value": "", "maxTextLength": 1000, "placeholder": ""}, {"fieldId": "secondRelapseRadiotherapyLocation", "label": "Lokalizacja napromieniania", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "secondRelapseRadiotherapyGroup1", "value": "", "maxTextLength": 1000, "placeholder": ""}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapseRadiotherapy"}], "fieldsGroupColumns": 2, "fieldWidth": 1000}]}, {"fieldId": "secondRelapseSurgery", "label": "Chirurgia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "secondRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}]}, {"fieldId": "secondRelapseHsct", "label": "Przeszczep", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "secondRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}], "children": [{"fieldId": "secondRelapseHsctGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "secondRelapseHsct", "fieldsGroupItems": [{"fieldId": "secondRelapseHsctDate", "label": "Data przeszczepu", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "secondRelapseHsctGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "secondRelapseHsctType", "label": "Typ przeszczepu", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "secondRelapseHsctGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Auto", "value": "Auto"}, {"label": "Allo", "value": "Allo"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapseHsct"}], "fieldWidth": 850}]}, {"fieldId": "secondRelapseGroup2", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "secondRelapse", "fieldsGroupItems": [{"fieldId": "secondRelapseTreatmentEndDate", "label": "Data zakończenia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "secondRelapseGroup2", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "secondRelapseTreatmentResponse", "label": "Odpowiedź na leczenie wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "secondRelapseGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "CR", "value": "CR"}, {"label": "Progresja", "value": "Progresja"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}]}, {"fieldId": "thirdRelapse", "label": "Trzecia wznowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "secondRelapse"}], "children": [{"fieldId": "thirdRelapseGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "thirdRelapse", "fieldsGroupItems": [{"fieldId": "thirdRelapseDate", "label": "Data rozpoznania wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "thirdRelapseGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "thirdRelapseType", "label": "Typ wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "thirdRelapseGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "Lokalna"}, {"label": "Odległa", "value": "Odległa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}, {"fieldId": "thirdRelapseChemotherapy", "label": "Chemioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "thirdRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}], "children": [{"fieldId": "thirdRelapseChemotherapyType", "label": "<PERSON><PERSON><PERSON>emioterapi<PERSON>", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "thirdRelapseChemotherapy", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "NBL-CHEM-01", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapseChemotherapy"}], "fieldWidth": 300}]}, {"fieldId": "thirdRelapseRadiotherapy", "label": "Radioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "thirdRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}], "children": [{"fieldId": "thirdRelapseRadiotherapyGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "thirdRelapseRadiotherapy", "fieldsGroupItems": [{"fieldId": "thirdRelapseRadiotherapyDose", "label": "Dawka", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "thirdRelapseRadiotherapyGroup1", "value": "", "maxTextLength": 1000, "placeholder": ""}, {"fieldId": "thirdRelapseRadiotherapyLocation", "label": "Lokalizacja napromieniania", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "thirdRelapseRadiotherapyGroup1", "value": "", "maxTextLength": 1000, "placeholder": ""}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapseRadiotherapy"}], "fieldsGroupColumns": 2, "fieldWidth": 1000}]}, {"fieldId": "thirdRelapseSurgery", "label": "Chirurgia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "thirdRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}]}, {"fieldId": "thirdRelapseHsct", "label": "Przeszczep", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "thirdRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}], "children": [{"fieldId": "thirdRelapseHsctGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "thirdRelapseHsct", "fieldsGroupItems": [{"fieldId": "thirdRelapseHsctDate", "label": "Data przeszczepu", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "thirdRelapseHsctGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "thirdRelapseHsctType", "label": "Typ przeszczepu", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "thirdRelapseHsctGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Auto", "value": "Auto"}, {"label": "Allo", "value": "Allo"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapseHsct"}], "fieldWidth": 850}]}, {"fieldId": "thirdRelapseGroup2", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "thirdRelapse", "fieldsGroupItems": [{"fieldId": "thirdRelapseTreatmentEndDate", "label": "Data zakończenia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "thirdRelapseGroup2", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "thirdRelapseTreatmentResponse", "label": "Odpowiedź na leczenie wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "thirdRelapseGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "CR", "value": "CR"}, {"label": "Progresja", "value": "Progresja"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}]}, {"fieldId": "fourthRelapse", "label": "Czwarta wznowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "thirdRelapse"}], "children": [{"fieldId": "fourthRelapseGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "fourthRelapse", "fieldsGroupItems": [{"fieldId": "fourthRelapseDate", "label": "Data rozpoznania wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "fourthRelapseGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "fourthRelapseType", "label": "Typ wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "fourthRelapseGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "Lokalna"}, {"label": "Odległa", "value": "Odległa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}, {"fieldId": "fourthRelapseChemotherapy", "label": "Chemioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fourthRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}], "children": [{"fieldId": "fourthRelapseChemotherapyType", "label": "<PERSON><PERSON><PERSON>emioterapi<PERSON>", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "fourthRelapseChemotherapy", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "NBL-CHEM-01", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapseChemotherapy"}], "fieldWidth": 300}]}, {"fieldId": "fourthRelapseRadiotherapy", "label": "Radioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fourthRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}], "children": [{"fieldId": "fourthRelapseRadiotherapyGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "fourthRelapseRadiotherapy", "fieldsGroupItems": [{"fieldId": "fourthRelapseRadiotherapyDose", "label": "Dawka", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "fourthRelapseRadiotherapyGroup1", "value": "", "maxTextLength": 1000, "placeholder": ""}, {"fieldId": "fourthRelapseRadiotherapyLocation", "label": "Lokalizacja napromieniania", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "fourthRelapseRadiotherapyGroup1", "value": "", "maxTextLength": 1000, "placeholder": ""}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapseRadiotherapy"}], "fieldsGroupColumns": 2, "fieldWidth": 1000}]}, {"fieldId": "fourthRelapseSurgery", "label": "Chirurgia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fourthRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}]}, {"fieldId": "fourthRelapseHsct", "label": "Przeszczep", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fourthRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}], "children": [{"fieldId": "fourthRelapseHsctGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "fourthRelapseHsct", "fieldsGroupItems": [{"fieldId": "fourthRelapseHsctDate", "label": "Data przeszczepu", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "fourthRelapseHsctGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "fourthRelapseHsctType", "label": "Typ przeszczepu", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "fourthRelapseHsctGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Auto", "value": "Auto"}, {"label": "Allo", "value": "Allo"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapseHsct"}], "fieldWidth": 850}]}, {"fieldId": "fourthRelapseGroup2", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "fourthRelapse", "fieldsGroupItems": [{"fieldId": "fourthRelapseTreatmentEndDate", "label": "Data zakończenia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "fourthRelapseGroup2", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "fourthRelapseTreatmentResponse", "label": "Odpowiedź na leczenie wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "fourthRelapseGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "CR", "value": "CR"}, {"label": "Progresja", "value": "Progresja"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}]}, {"fieldId": "fifthRelapse", "label": "Piąta wznowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fourthRelapse"}], "children": [{"fieldId": "fifthRelapseGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "fifthRelapse", "fieldsGroupItems": [{"fieldId": "fifthRelapseDate", "label": "Data rozpoznania wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "fifthRelapseGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "fifthRelapseType", "label": "Typ wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "fifthRelapseGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "Lokalna"}, {"label": "Odległa", "value": "Odległa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}, {"fieldId": "fifthRelapseChemotherapy", "label": "Chemioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fifthRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}], "children": [{"fieldId": "fifthRelapseChemotherapyType", "label": "<PERSON><PERSON><PERSON>emioterapi<PERSON>", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "fifthRelapseChemotherapy", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "NBL-CHEM-01", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapseChemotherapy"}], "fieldWidth": 300}]}, {"fieldId": "fifthRelapseRadiotherapy", "label": "Radioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fifthRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}], "children": [{"fieldId": "fifthRelapseRadiotherapyGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "fifthRelapseRadiotherapy", "fieldsGroupItems": [{"fieldId": "fifthRelapseRadiotherapyDose", "label": "Dawka", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "fifthRelapseRadiotherapy", "value": "", "maxTextLength": 1000, "placeholder": ""}, {"fieldId": "fifthRelapseRadiotherapyLocation", "label": "Lokalizacja napromieniania", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "fifthRelapseRadiotherapy", "value": "", "maxTextLength": 1000, "placeholder": ""}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapseRadiotherapy"}], "fieldsGroupColumns": 2, "fieldWidth": 1000}]}, {"fieldId": "fifthRelapseSurgery", "label": "Chirurgia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fifthRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}]}, {"fieldId": "fifthRelapseHsct", "label": "Przeszczep", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "fifthRelapse", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}], "children": [{"fieldId": "fifthRelapseHsctGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "fifthRelapseHsct", "fieldsGroupItems": [{"fieldId": "fifthRelapseHsctDate", "label": "Data przeszczepu", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "fifthRelapseHsctGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "fifthRelapseHsctType", "label": "Typ przeszczepu", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "fifthRelapseHsctGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Auto", "value": "Auto"}, {"label": "Allo", "value": "Allo"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapseHsct"}], "fieldWidth": 850}]}, {"fieldId": "fifthRelapseGroup2", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "fifthRelapse", "fieldsGroupItems": [{"fieldId": "fifthRelapseTreatmentEndDate", "label": "Data zakończenia leczenia wznowy", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "fifthRelapseGroup2", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "fifthRelapseTreatmentResponse", "label": "Odpowiedź na leczenie wznowy", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "fifthRelapseGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "CR", "value": "CR"}, {"label": "Progresja", "value": "Progresja"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "fifthRelapse"}], "fieldWidth": 580, "fieldsGroupColumns": 2}]}, {"fieldId": "relapseComment", "label": "", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": []}]}