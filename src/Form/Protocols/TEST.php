<?php

declare(strict_types=1);

namespace App\Form\Protocols;

use App\Form\Form;
use App\Form\ProtocolFormConfig;
use App\SharedKernel\Protocol;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;

class TEST implements ProtocolFormConfig
{
    /** @var Collection<string, Form> */
    private Collection $forms;
    private ?string $icd10;
    private ?\DateTimeImmutable $dateOfDiagnosis;

    private function __construct(?string $icd10 = null, ?\DateTimeImmutable $dateOfDiagnosis = null)
    {
        $this->icd10 = $icd10;
        $this->dateOfDiagnosis = $dateOfDiagnosis;
        $this->forms = new ArrayCollection();
    }

    public static function protocol(): Protocol
    {
        return Protocol::TEST;
    }

    public static function create(?string $icd10, ?\DateTimeImmutable $dateOfDiagnosis): self
    {
        $self = new self($icd10, $dateOfDiagnosis);
        $self->configure();

        return $self;
    }

    /** @return Collection<string, Form> */
    public function configure(): Collection
    {
        $this->forms->set('DEFAULT', Form::create('DEFAULT', self::protocol(), 'Podstawowy', 'default.json'));

        return $this->forms;
    }

    public function getForm(string $formId): ?Form
    {
        return $this->forms->get($formId);
    }

    public function jsonSerialize(): array
    {
        return [
            'protocol' => self::protocol(),
            'icd10' => $this->icd10,
            'dateOfDiagnosis' => $this->dateOfDiagnosis?->format('Y-m-d'),
            'forms' => $this->forms->getValues(),
        ];
    }
}
