<?php

declare(strict_types=1);

namespace App\Form;

use App\SharedKernel\Protocol;
use Doctrine\Common\Collections\Collection;

interface ProtocolFormConfig extends \JsonSerializable
{
    public static function create(?string $icd10, ?\DateTimeImmutable $dateOfDiagnosis): ProtocolFormConfig;

    public static function protocol(): Protocol;

    public function getForm(string $formId): ?Form;

    /** @return Collection<string, Form> */
    public function configure(): Collection;

    /** @return array<string, mixed> */
    public function jsonSerialize(): array;
}
