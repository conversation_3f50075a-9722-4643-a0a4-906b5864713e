<?php

declare(strict_types=1);

namespace App\Form;

use App\Common\Attribute\TrackEntityChange;
use App\Common\Uuid;
use App\SharedKernel\Protocol;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\Entity;

#[Entity]
#[TrackEntityChange(objectLogDetailsFunction: 'logDetails')]
#[ORM\Table(name: 'form_protocol_form_data')]
#[ORM\UniqueConstraint(columns: ['patientProtocolId', 'formId'])]
#[ORM\Index(columns: ['formId', 'patientId', 'protocol'])]
class ProtocolFormData
{
    #[ORM\Id]
    #[ORM\Column(type: 'uuid_symfony', unique: true)]
    private Uuid $protocolFormDataId;

    #[ORM\Column(type: 'uuid_symfony')]
    private Uuid $patientProtocolId;

    #[ORM\Column(length: 255)]
    private string $formId;

    #[ORM\Column(type: 'uuid_symfony')]
    private Uuid $patientId;

    #[ORM\Column(length: 50, enumType: Protocol::class)]
    private Protocol $protocol;

    #[ORM\Column(type: 'text')]
    private string $data;

    #[ORM\Column(type: 'datetime')]
    private \DateTimeInterface $createdAt;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private ?\DateTimeInterface $updatedAt;

    private function __construct(Uuid $protocolFormDataId, Uuid $patientProtocolId, string $formId, Uuid $patientId, Protocol $protocol, string $data)
    {
        $this->protocolFormDataId = $protocolFormDataId;
        $this->patientProtocolId = $patientProtocolId;
        $this->formId = $formId;
        $this->patientId = $patientId;
        $this->protocol = $protocol;
        $this->data = $data;
        $this->createdAt = new \DateTimeImmutable();
        $this->updatedAt = null;
    }

    public static function create(Uuid $protocolFormDataId, Uuid $patientProtocolId, string $formId, Uuid $patientId, Protocol $protocol, string $data): ProtocolFormData
    {
        return new ProtocolFormData($protocolFormDataId, $patientProtocolId, $formId, $patientId, $protocol, $data);
    }

    public function update(string $data): void
    {
        $this->data = $data;
        $this->updatedAt = new \DateTimeImmutable();
    }

    /** @return array<string, string> */
    public function logDetails(): array
    {
        return [
            'formId' => $this->formId,
            'patientId' => $this->patientId->toString(),
            'protocol' => $this->protocol->value,
        ];
    }

    public function protocolFormDataId(): Uuid
    {
        return $this->protocolFormDataId;
    }

    public function patientProtocolId(): Uuid
    {
        return $this->patientProtocolId;
    }

    public function formId(): string
    {
        return $this->formId;
    }

    public function patientId(): Uuid
    {
        return $this->patientId;
    }

    public function protocol(): Protocol
    {
        return $this->protocol;
    }

    public function data(): string
    {
        return $this->data;
    }

    public function createdAt(): \DateTimeInterface
    {
        return $this->createdAt;
    }

    public function updatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }
}
