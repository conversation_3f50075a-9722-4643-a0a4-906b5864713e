<?php

declare(strict_types=1);

namespace App\Form\Validator;

use Symfony\Component\Validator\Constraint;

/**
 * @Annotation
 */
class FieldsSchemaConstraint extends Constraint
{
    /** @var array<int, array<string, mixed>> */
    public array $fieldsSchema = [];

    public function __construct($options = [])
    {
        parent::__construct($options);
        $this->fieldsSchema = $options;
    }

    public function getDefaultOption(): string
    {
        return 'fieldsSchema';
    }

    public function validatedBy(): string
    {
        return static::class.'Validator';
    }
}
