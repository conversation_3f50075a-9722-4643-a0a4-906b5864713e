<?php

declare(strict_types=1);

namespace App\Form\Validator;

use App\SharedKernel\DictionaryType;
use App\SharedKernel\FormFieldsType;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

class FieldsSchemaConstraintValidator extends ConstraintValidator
{
    /** @param array<string, mixed> $value */
    public function validate($value, Constraint $constraint): void
    {
        if (!$constraint instanceof FieldsSchemaConstraint) {
            throw new UnexpectedTypeException($constraint, FieldsSchemaConstraint::class);
        }

        foreach ($constraint->fieldsSchema as $field) {
            $this->fieldValidation($field, 'root');

            if ($field['type'] === 'fields-group-table') {
                if (isset($field['fieldsGroupRowItems']) && is_array($field['fieldsGroupRowItems'])) {
                    foreach ($field['fieldsGroupRowItems'] as $item) {
                        $this->fieldValidation($item, 'children');
                    }
                } else {
                    $this->context->buildViolation('Pole typu "fields-group-table" powinno mieć "fieldsGroupRowItems" jako tablicę.')
                        ->atPath($field['fieldId'])
                        ->addViolation();
                }
            }

            if ($field['type'] === 'fields-group') {
                if (isset($field['fieldsGroupItems']) && is_array($field['fieldsGroupItems'])) {
                    foreach ($field['fieldsGroupItems'] as $item) {
                        $this->fieldValidation($item, 'children');
                    }
                } else {
                    $this->context->buildViolation('Pole typu "fields-group" powinno mieć "fieldsGroupItems" jako tablicę.')
                        ->atPath($field['fieldId'])
                        ->addViolation();
                }
            }
        }
    }

    private function fieldValidation(mixed $field, string $fieldPath): void
    {
        if (!isset($field['fieldId'])) {
            $this->context->buildViolation('Pole nie posiada wymaganego atrybutu: fieldId.')
                ->addViolation();

            return;
        }
        if (!isset($field['label'])) {
            $this->context->buildViolation('Pole nie posiada wymaganego atrybutu: label.')
                ->atPath($field['fieldId'])
                ->addViolation();

            return;
        }

        if (!isset($field['type'])) {
            $this->context->buildViolation('Pole nie posiada wymaganego atrybutu: type.')
                ->atPath($field['fieldId'])
                ->addViolation();

            return;
        }

        if (!isset($field['disabled'])) {
            $this->context->buildViolation('Pole nie posiada wymaganego atrybutu: disabled.')
                ->atPath($field['fieldId'])
                ->addViolation();

            return;
        }

        if (!array_key_exists('parentId', $field)) {
            $this->context->buildViolation('Pole nie posiada wymaganego atrybutu: parentId.')
                ->atPath($field['fieldId'])
                ->addViolation();

            return;
        }

        // Rekurencyjna obsługa children
        if (isset($field['children']) && is_array($field['children'])) {
            foreach ($field['children'] as $childField) {
                $this->fieldValidation($childField, 'children');
            }
        }

        $this->allowedFieldValidation($field, $fieldPath);

        if ($field['type'] === 'three-state-level') {
            if (!isset($field['threeStateLevelOptions']) || count($field['threeStateLevelOptions']) !== 3) {
                $this->context->buildViolation('Pole typu "three-state-level" powinno mieć dokładnie 3 opcje w "threeStateLevelOptions".')
                    ->atPath($field['fieldId'])
                    ->addViolation();
            }
        }

        if ($field['type'] === 'select-input-field') {
            if (!isset($field['selectUsePatientProtocolsAsOptions']) || $field['selectUsePatientProtocolsAsOptions'] !== true) {
                if (!isset($field['selectOptions']) || count($field['selectOptions']) < 1) {
                    $this->context->buildViolation('Pole typu "select-input-field" powinno mieć przynajmniej jedną opcję w "selectOptions".')
                        ->atPath($field['fieldId'])
                        ->addViolation();
                }
            }
        }

        if ($field['type'] === 'live-select-field') {
            if (empty($field['liveSelectDictionary']) || !DictionaryType::isValid($field['liveSelectDictionary'])) {
                $this->context->buildViolation('Pole typu "live-select-field" powinno mieć zdefiniowany jeden z dostępnych słowników.')
                    ->atPath($field['fieldId'])
                    ->addViolation();
            }
        }
    }

    private function allowedFieldValidation(mixed $field, string $fieldPath): void
    {
        $allowedFields = $fieldPath === 'root' ? FormFieldsType::ALLOW_ROOT : FormFieldsType::ALLOW_CHILDREN;

        if (!in_array($field['type'], $allowedFields, true)) {
            $this->context->buildViolation('Pole typu "'.$field['type'].' nie jest dozwolone.')
                ->atPath($field['fieldId'])
                ->addViolation();
        }
    }
}
