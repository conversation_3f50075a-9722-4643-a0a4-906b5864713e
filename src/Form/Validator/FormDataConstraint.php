<?php

declare(strict_types=1);

namespace App\Form\Validator;

use Symfony\Component\Validator\Constraint;

/**
 * @Annotation
 */
class FormDataConstraint extends Constraint
{
    /** @var array<int, array<string, mixed>> */
    public array $flattenFieldsSchema = [];
    public string $patientId = '';

    public function __construct($options = [])
    {
        $this->flattenFieldsSchema = $options['flattenFieldsSchema'] ?? [];
        $this->patientId = $options['patientId'] ?? '';

        parent::__construct($options);
    }

    public function getDefaultOption(): string
    {
        return 'flattenFieldsSchema';
    }

    public function getRequiredOptions(): array
    {
        return ['flattenFieldsSchema', 'patientId'];
    }

    /** @return array<int, array<string, mixed>> */
    public function getFlattenFieldsSchema(): array
    {
        return $this->flattenFieldsSchema;
    }

    public function getPatientId(): string
    {
        return $this->patientId;
    }

    public function validatedBy(): string
    {
        return static::class.'Validator';
    }
}
