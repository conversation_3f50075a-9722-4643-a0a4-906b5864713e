<?php

declare(strict_types=1);

namespace App\Form;

use App\SharedKernel\Protocol;

class Form implements \JsonSerializable
{
    private string $formId;
    private Protocol $protocol;
    private string $name;
    private string $schema;

    private function __construct(string $formId, Protocol $protocol, string $name, string $schema)
    {
        $this->formId = $formId;
        $this->protocol = $protocol;
        $this->name = $name;
        $this->schema = $schema;
    }

    public static function create(string $formId, Protocol $protocol, string $name, string $schema): Form
    {
        return new Form(
            $formId,
            $protocol,
            $name,
            $schema,
        );
    }

    /** @return array<string, string> */
    public function jsonSerialize(): array
    {
        return [
            'formId' => $this->formId,
            'name' => $this->name,
        ];
    }

    public function schema(): string
    {
        return $this->schema;
    }

    public function formId(): string
    {
        return $this->formId;
    }

    public function protocol(): Protocol
    {
        return $this->protocol;
    }

    public function name(): string
    {
        return $this->name;
    }
}
