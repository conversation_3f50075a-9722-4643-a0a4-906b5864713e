<?php

declare(strict_types=1);

namespace App\Form;

use App\Common\Uuid;
use App\Form\Protocols\ALCL;
use App\Form\Protocols\ALL;
use App\Form\Protocols\AML;
use App\Form\Protocols\BT;
use App\Form\Protocols\CML;
use App\Form\Protocols\EGCT;
use App\Form\Protocols\FOLLOWUP;
use App\Form\Protocols\HBL;
use App\Form\Protocols\HL;
use App\Form\Protocols\INNE;
use App\Form\Protocols\LBL;
use App\Form\Protocols\LCH;
use App\Form\Protocols\MDS;
use App\Form\Protocols\MPAL;
use App\Form\Protocols\NBL;
use App\Form\Protocols\NHLB;
use App\Form\Protocols\OUN;
use App\Form\Protocols\RBL;
use App\Form\Protocols\STS;
use App\Form\Protocols\TEST;
use App\Form\Protocols\WT;
use App\Form\View\FormSchemaAndDataView;
use App\Form\View\PatientProtocolView;
use App\Patient\Application\PatientFacade;
use App\Patient\Application\Query\PatientView;
use App\SharedKernel\Protocol;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

readonly class FormService
{
    public function __construct(
        private ProtocolFormDataRepository $formDataRepository,
        private PatientFacade $patientFacade,
        private ParameterBagInterface $parameterBag,
        private ValidationService $validationService,
    ) {
    }

    public function saveFormData(string $patientProtocolId, string $formId, string $data): void
    {
        $patientProtocol = $this->getPatientProtocolByPatientProtocolId($patientProtocolId);

        if ($patientProtocol->isActive() === false) {
            throw new \InvalidArgumentException('Ta jednostka chorobowa przypisana do pacjenta jest nieaktywna.', 400);
        }

        $protocol = Protocol::from($patientProtocol->protocol());
        $form = $this->getForms(
            $protocol,
            $patientProtocol->icd10(),
            $patientProtocol->dateOfDiagnosis()
        )->getForm($formId);

        if ($form === null) {
            throw new \InvalidArgumentException('Nie znaleziono formularza.', 404);
        }

        $schemaArray = json_decode($this->getFormSchema($protocol->value, $form->schema()), true, 512, JSON_THROW_ON_ERROR);
        $dataArray = json_decode($data, true, 512, JSON_THROW_ON_ERROR);

        $this->validationService->tryValidateSchemaAndData($dataArray, $schemaArray, $patientProtocol->patientId());

        $formData = $this->formDataRepository->findByPatientProtocolIdAndFormId($patientProtocolId, $formId);

        if ($formData === null) {
            $formData = ProtocolFormData::create(
                Uuid::generate(),
                Uuid::fromString($patientProtocolId),
                $formId,
                Uuid::fromString($patientProtocol->patientId()),
                $protocol,
                $data
            );
            $this->formDataRepository->add($formData);
        } else {
            $formData->update($data);
            $this->formDataRepository->update($formData);
        }

        $this->updatePatient(
            $patientProtocol->patientId(),
            $patientProtocolId,
            $dataArray,
            $protocol
        );
    }

    public function getFormSchemaAndData(string $patientProtocolId, string $formId): FormSchemaAndDataView
    {
        $patientProtocol = $this->getPatientProtocolByPatientProtocolId($patientProtocolId);

        if ($patientProtocol->isActive() === false) {
            throw new \InvalidArgumentException('Ta jednostka chorobowa przypisana do pacjenta jest nieaktywna.', 400);
        }

        $protocol = Protocol::from($patientProtocol->protocol());
        $form = $this->getForms(
            $protocol,
            $patientProtocol->icd10(),
            $patientProtocol->dateOfDiagnosis()
        )->getForm($formId);
        $formData = $this->formDataRepository->findByPatientProtocolIdAndFormId($patientProtocolId, $formId);

        if ($form === null) {
            throw new \InvalidArgumentException('Nie znaleziono formularza.', 404);
        }

        $schemaJson = $this->getFormSchema($protocol->value, $form->schema());

        return FormSchemaAndDataView::create($schemaJson, $formData?->data());
    }

    /** @return Collection<string, ProtocolFormConfig> */
    public function getPatientForms(string $patientId): Collection
    {
        $patientForms = new ArrayCollection();

        /** @var ?PatientView $patient */
        $patient = $this->patientFacade->findByPatientId($patientId);

        if ($patient === null) {
            throw new \InvalidArgumentException('Nie znaleziono pacjenta.', 404);
        }

        /** @var \App\Patient\Application\Query\PatientProtocolView $protocol */
        foreach ($patient->protocols() as $protocol) {
            if ($protocol->isActive() === false) {
                continue;
            }

            $patientForms->set(
                $protocol->patientProtocolId(),
                $this->getForms(
                    Protocol::from($protocol->protocol()),
                    $protocol->icd10Code(),
                    $protocol->treatmentStartDate()
                )
            );
        }

        return $patientForms;
    }

    public function getFormSchema(string $protocol, string $schema): string
    {
        $projectDir = $this->parameterBag->get('kernel.project_dir');
        $schemaJson = file_get_contents($projectDir."/src/Form/Protocols/Schemas/{$protocol}/".$schema);

        if ($schemaJson === false) {
            throw new \RuntimeException('Nie udało się pobrać schema formularza.', 500);
        }

        return $schemaJson;
    }

    public function getForms(Protocol $protocol, ?string $icd10, ?\DateTimeImmutable $dateOfDiagnosis): ProtocolFormConfig
    {
        return match ($protocol->value) {
            Protocol::STS->value => STS::create($icd10, $dateOfDiagnosis),
            Protocol::WT->value => WT::create($icd10, $dateOfDiagnosis),
            Protocol::NHLB->value => NHLB::create($icd10, $dateOfDiagnosis),
            Protocol::ALCL->value => ALCL::create($icd10, $dateOfDiagnosis),
            Protocol::LBL->value => LBL::create($icd10, $dateOfDiagnosis),
            Protocol::RBL->value => RBL::create($icd10, $dateOfDiagnosis),
            Protocol::EGCT->value => EGCT::create($icd10, $dateOfDiagnosis),
            Protocol::NBL->value => NBL::create($icd10, $dateOfDiagnosis),
            Protocol::OUN->value => OUN::create($icd10, $dateOfDiagnosis),
            Protocol::HL->value => HL::create($icd10, $dateOfDiagnosis),
            Protocol::ALL->value => ALL::create($icd10, $dateOfDiagnosis),
            Protocol::AML->value => AML::create($icd10, $dateOfDiagnosis),
            Protocol::CML->value => CML::create($icd10, $dateOfDiagnosis),
            Protocol::MPAL->value => MPAL::create($icd10, $dateOfDiagnosis),
            Protocol::BT->value => BT::create($icd10, $dateOfDiagnosis),
            Protocol::HBL->value => HBL::create($icd10, $dateOfDiagnosis),
            Protocol::MDS->value => MDS::create($icd10, $dateOfDiagnosis),
            Protocol::LCH->value => LCH::create($icd10, $dateOfDiagnosis),
            Protocol::INNE->value => INNE::create($icd10, $dateOfDiagnosis),
            Protocol::FOLLOWUP->value => FOLLOWUP::create($icd10, $dateOfDiagnosis),
            Protocol::TEST->value => TEST::create($icd10, $dateOfDiagnosis),
            // @phpstan-ignore-next-line
            default => throw new \InvalidArgumentException('Nie znaleziono formularza.'),
        };
    }

    /** @return Collection<int|string, PatientProtocolView> */
    public function getAllPatientProtocols(string $patientId, bool $onlyActive = false): Collection
    {
        /** @var ?PatientView $patient */
        $patient = $this->patientFacade->findByPatientId($patientId);

        if ($patient === null) {
            throw new \InvalidArgumentException('Nie znaleziono pacjenta.', 404);
        }

        $filteredProtocols = $patient->protocols()->filter(function ($element) use ($onlyActive) {
            return $onlyActive ? $element->isActive() : true;
        });

        $result = new ArrayCollection();
        foreach ($filteredProtocols as $key => $protocol) {
            $result->set($key, PatientProtocolView::create(
                $patient->patientId(),
                $patient->lastName().' '.$patient->firstName(),
                $protocol->patientProtocolId(),
                $protocol->protocol(),
                $protocol->icd10Code(),
                $protocol->treatmentStartDate(),
                $protocol->isActive()
            ));
        }

        return $result;
    }

    public function getPatientProtocolByPatientProtocolId(string $patientProtocolId): PatientProtocolView
    {
        /** @var ?PatientView $patient */
        $patient = $this->patientFacade->findByPatientProtocolId($patientProtocolId);

        if ($patient === null) {
            throw new \InvalidArgumentException(sprintf('Nie znaleziono jednostki chorobowej o identyfikatorze %s przypisanej do pacjenta.', $patientProtocolId), 404);
        }

        $patientProtocol = $patient->protocols()->filter(function ($element) use ($patientProtocolId) {
            return $element->patientProtocolId() === $patientProtocolId;
        })->first();

        if ($patientProtocol === false) {
            throw new \InvalidArgumentException(sprintf('Nie znaleziono jednostki chorobowej o identyfikatorze %s przypisanej do pacjenta.', $patientProtocolId), 404);
        }

        return PatientProtocolView::create(
            $patient->patientId(),
            $patient->lastName().' '.$patient->firstName(),
            $patientProtocol->patientProtocolId(),
            $patientProtocol->protocol(),
            $patientProtocol->icd10Code(),
            $patientProtocol->treatmentStartDate(),
            $patientProtocol->isActive()
        );
    }

    /** @param array<string, mixed> $data */
    private function updatePatient(string $patientId, string $patientProtocolId, array $data, Protocol $protocol): void
    {
        if (array_key_exists('dateOfDiagnosis', $data) && !empty($data['dateOfDiagnosis'])) {
            if ($this->isValidDate($data['dateOfDiagnosis'])) {
                $this->patientFacade->updatePatientProtocolStartTreatment(
                    $patientId,
                    $patientProtocolId,
                    new \DateTimeImmutable($data['dateOfDiagnosis'])
                );
            } else {
                $this->patientFacade->updatePatientProtocolStartTreatment(
                    $patientId,
                    $patientProtocolId,
                    null
                );
            }
        }

        if (array_key_exists('icd10', $data) && !empty($data['icd10'])) {
            $this->patientFacade->updatePatientProtocolIcd10Code(
                $patientId,
                $patientProtocolId,
                $data['icd10']
            );
        }

        if (array_key_exists('death', $data)) {
            if ($data['death'] === 'yes') {
                $this->patientFacade->updateDecreaseStatus(
                    $patientId,
                    true
                );
            }

            if ($data['death'] === 'no') {
                $this->patientFacade->updateDecreaseStatus(
                    $patientId,
                    false
                );
            }
        }

        // Add logic for Follow-up completion date
        if ($protocol === Protocol::FOLLOWUP && array_key_exists('dateOfCompletionForm', $data)) {
            $completionDate = null;
            if (!empty($data['dateOfCompletionForm']) && $this->isValidDate($data['dateOfCompletionForm'])) {
                $completionDate = new \DateTimeImmutable($data['dateOfCompletionForm']);
            }
            $this->patientFacade->updateFollowUpDateOfCompletionForm(
                $patientId,
                $completionDate
            );
        }
    }

    private function isValidDate(?string $date, string $format = 'Y-m-d'): bool
    {
        if (empty($date)) {
            return false;
        }

        $d = \DateTime::createFromFormat($format, $date);

        return $d && $d->format($format) === $date;
    }
}
