<?php

declare(strict_types=1);

namespace App\Form;

use App\Form\View\FormSchemaAndDataView;
use App\Form\View\PatientProtocolView;
use Doctrine\Common\Collections\Collection;

readonly class FormFacade
{
    public function __construct(
        private FormService $formService,
        private ProtocolFormDataRepository $protocolFormDataRepository,
    ) {
    }

    public function saveFormData(string $patientProtocolId, string $formId, string $data): void
    {
        $this->formService->saveFormData($patientProtocolId, $formId, $data);
    }

    /** @return Collection<string, ProtocolFormConfig> */
    public function getPatientForms(string $patientId): Collection
    {
        return $this->formService->getPatientForms($patientId);
    }

    public function getFormSchemaAndData(string $protocolFormDataId, string $formId): FormSchemaAndDataView
    {
        return $this->formService->getFormSchemaAndData($protocolFormDataId, $formId);
    }

    public function getPatientProtocolByPatientProtocolId(string $patientProtocolId): PatientProtocolView
    {
        return $this->formService->getPatientProtocolByPatientProtocolId($patientProtocolId);
    }

    public function findAll(?\DateTimeImmutable $changedSince = null): \Generator
    {
        return $this->protocolFormDataRepository->findAll($changedSince);
    }
}
