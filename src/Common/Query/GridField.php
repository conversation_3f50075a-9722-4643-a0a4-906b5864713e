<?php

declare(strict_types=1);

namespace App\Common\Query;

readonly class GridField
{
    private function __construct(
        private string $fieldName,
        private bool $isFilterable,
        private bool $isSortable,
        private GridFilterMethod $filterMethod = GridFilterMethod::STARTS_WITH,
    ) {
    }

    public static function create(
        string $fieldName,
        bool $isFilterable,
        bool $isSortable,
        GridFilterMethod $filterMethod = GridFilterMethod::STARTS_WITH,
    ): self {
        return new self($fieldName, $isFilterable, $isSortable, $filterMethod);
    }

    public function fieldName(): string
    {
        return $this->fieldName;
    }

    public function isFilterable(): bool
    {
        return $this->isFilterable;
    }

    public function isSortable(): bool
    {
        return $this->isSortable;
    }

    public function filterMethod(): GridFilterMethod
    {
        return $this->filterMethod;
    }
}
