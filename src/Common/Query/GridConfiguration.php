<?php

declare(strict_types=1);

namespace App\Common\Query;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Query\QueryBuilder;
use Symfony\Component\HttpFoundation\Request;

class GridConfiguration
{
    public const INVALID_PAGE_NUMBER = 'Nieprawidłowy numer strony. Musi to być liczba większa od 0.';
    public const INVALID_ITEMS_PER_PAGE = 'Nieprawidłowa liczba elementów na stronę. Musi to być liczba większa od 0.';
    public const MAX_ITEMS_PER_PAGE_EXCEEDED = 'Nieprawidłowa liczba elementów na stronę. Maksymalna dozwolona liczba to '.self::MAX_ITEMS_PER_PAGE.'.';
    public const SORTING_NOT_ALLOWED = 'Sortowanie według pola jest niedozwolone.';
    public const INVALID_SORT_DIRECTION = 'Nieprawidłowy kierunek sortowania. Dozwolone są tylko ASC lub DESC.';
    public const FILTERING_NOT_ALLOWED = 'Filtrowanie według pola jest niedozwolone.';

    private const int MAX_ITEMS_PER_PAGE = 500;
    private int $page;
    private int $itemsPerPage;
    /** @var array<string, string> */
    private array $sorts;
    /** @var array<string, mixed> */
    private array $filters;
    /** @var Collection<string, GridField> */
    private Collection $gridFields;

    /**
     * @param Collection<int, GridField> $gridFields
     * @param array<string, string>      $sorts
     * @param array<string, mixed>       $filters
     */
    private function __construct(
        Collection $gridFields,
        int|string $page = 1,
        int|string $itemsPerPage = 20,
        array $sorts = [],
        array $filters = [],
    ) {
        $this->gridFields = new ArrayCollection();

        foreach ($gridFields as $gridField) {
            $this->gridFields->set($gridField->fieldName(), $gridField);
        }

        $this->validatePage($page);
        $this->validateItemsPerPage($itemsPerPage);
        $this->validateSorts($sorts);
        $this->validateFilters($filters);

        $this->page = (int) $page;
        $this->itemsPerPage = (int) $itemsPerPage;
        $this->sorts = $sorts;
        $this->filters = $filters;
    }

    /** @param ArrayCollection<int, GridField> $gridFields */
    public static function createFromRequest(Request $request, ArrayCollection $gridFields): self
    {
        $queryData = $request->query->all();

        $page = isset($queryData['page']) ? (int) $queryData['page'] : 1;
        $itemsPerPage = isset($queryData['itemsPerPage']) ? (int) $queryData['itemsPerPage'] : 20;
        $sorts = isset($queryData['sorts']) && is_array($queryData['sorts']) ? $queryData['sorts'] : [];
        $filters = isset($queryData['filters']) && is_array($queryData['filters']) ? $queryData['filters'] : [];

        return new self($gridFields, $page, $itemsPerPage, $sorts, $filters);
    }

    /**
     * @param Collection<int, GridField> $gridFields
     * @param array<string, string>      $sorts
     * @param array<string, mixed>       $filters
     */
    public static function create(Collection $gridFields, int|string $page = 1, int|string $itemsPerPage = 20,
        array $sorts = [], array $filters = []): self
    {
        return new self($gridFields, $page, $itemsPerPage, $sorts, $filters);
    }

    /** @param array<string, string> $fieldDatabaseMapping */
    public function addSortAndFilterToDbalQueryBuilder(QueryBuilder $queryBuilder, array $fieldDatabaseMapping = []): QueryBuilder
    {
        // Add filtering
        foreach ($this->filters as $field => $value) {
            $gridField = $this->gridFields->offsetGet($field);
            if ($gridField && $gridField->isFilterable()) {
                $paramName = str_replace('.', '_', $field);
                $databaseFieldName = $fieldDatabaseMapping[$field] ?? $gridField->fieldName(); // Use the mapping if provided, else use the databaseFieldName from GridField
                switch ($gridField->filterMethod()) {
                    case GridFilterMethod::STARTS_WITH:
                        $queryBuilder->andWhere($queryBuilder->expr()->like($databaseFieldName, ':'.$paramName))
                            ->setParameter($paramName, $value.'%');
                        break;
                    case GridFilterMethod::CONTAINS:
                        $queryBuilder->andWhere($queryBuilder->expr()->like($databaseFieldName, ':'.$paramName))
                            ->setParameter($paramName, '%'.$value.'%');
                        break;
                    case GridFilterMethod::ENDS_WITH:
                        $queryBuilder->andWhere($queryBuilder->expr()->like($databaseFieldName, ':'.$paramName))
                            ->setParameter($paramName, '%'.$value);
                        break;
                    case GridFilterMethod::EQUALS:
                        $queryBuilder->andWhere($queryBuilder->expr()->eq($databaseFieldName, ':'.$paramName))
                            ->setParameter($paramName, $value);
                        break;
                    case GridFilterMethod::JSON_CONTAINS:
                        $queryBuilder->andWhere('JSON_CONTAINS('.$databaseFieldName.', :'.$paramName.')')
                            ->setParameter($paramName, json_encode($value, JSON_THROW_ON_ERROR));
                        break;
                    case GridFilterMethod::BOOL:
                        $boolValue = in_array($value, [1, '1', 'true', true], true) ? 1 : 0;
                        $queryBuilder->andWhere($queryBuilder->expr()->eq($databaseFieldName, ':'.$paramName))
                            ->setParameter($paramName, $boolValue);
                        break;
                    case GridFilterMethod::IN:
                        $valueArray = is_array($value) ? $value : [$value];

                        if (empty($valueArray)) {
                            break;
                        }

                        $values = array_map(function ($item) { return "'".$item."'"; }, $valueArray);
                        $queryBuilder->andWhere($queryBuilder->expr()->in($databaseFieldName, $values));
                        $queryBuilder->addOrderBy('FIELD('.$databaseFieldName.', '.implode(',', $values).')');
                        break;
                    case GridFilterMethod::IN_JSON_CONTAINS:
                        $valueArray = is_array($value) ? $value : [$value];

                        if (empty($valueArray)) {
                            break;
                        }

                        $orExpressions = [];
                        foreach ($valueArray as $index => $val) {
                            $paramNameWithIndex = $paramName.'_'.$index;
                            // For JSON_CONTAINS we need to properly format the parameter - it must be simple string value with quotes
                            $orExpressions[] = 'JSON_CONTAINS('.$databaseFieldName.', JSON_QUOTE(:'.$paramNameWithIndex.'), "$")';
                            $queryBuilder->setParameter($paramNameWithIndex, $val);
                        }
                        $queryBuilder->andWhere('('.implode(' OR ', $orExpressions).')');
                        break;
                }
            }
        }

        // Add sorting
        foreach ($this->sorts as $field => $direction) {
            $gridField = $this->gridFields->offsetGet($field);
            if ($gridField && $gridField->isSortable()) {
                $databaseFieldName = $fieldDatabaseMapping[$field] ?? $gridField->fieldName(); // Use the mapping if provided, else use the databaseFieldName from GridField
                $queryBuilder->addOrderBy($databaseFieldName, $direction);
            }
        }

        return $queryBuilder;
    }

    private function validatePage(int|string $page): void
    {
        if ($page <= 0) {
            throw new \InvalidArgumentException(self::INVALID_PAGE_NUMBER);
        }
    }

    private function validateItemsPerPage(int|string $itemsPerPage): void
    {
        if ($itemsPerPage <= 0) {
            throw new \InvalidArgumentException(self::INVALID_ITEMS_PER_PAGE);
        }

        if ($itemsPerPage > self::MAX_ITEMS_PER_PAGE) {
            throw new \InvalidArgumentException(self::MAX_ITEMS_PER_PAGE_EXCEEDED);
        }
    }

    /** @param array<string, string> $sorts */
    private function validateSorts(array $sorts): void
    {
        foreach ($sorts as $field => $direction) {
            $gridField = $this->gridFields->get($field);
            if (!$gridField || !$gridField->isSortable()) {
                throw new \InvalidArgumentException(self::SORTING_NOT_ALLOWED." {$field}.");
            }

            if (!in_array(strtoupper($direction), ['ASC', 'DESC'])) {
                throw new \InvalidArgumentException(self::INVALID_SORT_DIRECTION." {$direction}.");
            }
        }
    }

    /** @param array<string, mixed> $filters */
    private function validateFilters(array $filters): void
    {
        foreach ($filters as $field => $value) {
            $gridField = $this->gridFields->get($field);
            if (!$gridField || !$gridField->isFilterable()) {
                throw new \InvalidArgumentException(self::FILTERING_NOT_ALLOWED." {$field}.");
            }
        }
    }

    public function page(): int
    {
        return $this->page;
    }

    public function itemsPerPage(): int
    {
        return $this->itemsPerPage;
    }

    /** @return array<string, string> */
    public function sorts(): array
    {
        return $this->sorts;
    }

    /** @return array<string, mixed> */
    public function filters(): array
    {
        return $this->filters;
    }

    /** @return Collection<string, GridField> */
    public function gridFields(): Collection
    {
        return $this->gridFields;
    }
}
