<?php

declare(strict_types=1);

namespace App\Common\Query;

use Doctrine\Common\Collections\Collection;

/**
 * @template TKey of int|string
 * @template TValue
 */
class GridResult implements \JsonSerializable
{
    /** @var Collection<TKey, TValue> */
    private Collection $data;
    private int $totalItems;
    private int $totalPages;
    private int $currentPage;
    private int $itemsPerPage;

    /** @param Collection<TKey, TValue> $data */
    public function __construct(Collection $data, int $totalItems, int $currentPage, int $itemsPerPage)
    {
        $this->data = $data;
        $this->totalItems = $totalItems;
        $this->totalPages = (int) ceil($totalItems / $itemsPerPage);
        $this->currentPage = $currentPage;
        $this->itemsPerPage = $itemsPerPage;
    }

    public function jsonSerialize(): mixed
    {
        return [
            'totalItems' => $this->totalItems,
            'totalPages' => $this->totalPages,
            'currentPage' => $this->currentPage,
            'itemsPerPage' => $this->itemsPerPage,
            'data' => $this->data->getValues(),
        ];
    }

    /** @param array<mixed, mixed> $replacements */
    public function replaceValueInData(string $propertyName, array $replacements): void
    {
        $data = $this->data->map(function ($item) use ($propertyName, $replacements) {
            if (property_exists($item, $propertyName) && array_key_exists($item->$propertyName, $replacements)) {
                $item->$propertyName = $replacements[$item->$propertyName];
            }

            return $item;
        });

        $this->data = $data;
    }

    /** @return Collection<TKey, TValue> */
    public function data(): Collection
    {
        return $this->data;
    }

    public function totalItems(): int
    {
        return $this->totalItems;
    }

    public function totalPages(): int
    {
        return $this->totalPages;
    }

    public function currentPage(): int
    {
        return $this->currentPage;
    }

    public function itemsPerPage(): int
    {
        return $this->itemsPerPage;
    }
}
