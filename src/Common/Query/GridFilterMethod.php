<?php

declare(strict_types=1);

namespace App\Common\Query;

enum GridFilterMethod: string
{
    case STARTS_WITH = 'starts_with'; // like 'value%'
    case CONTAINS = 'contains'; // like '%value%'
    case ENDS_WITH = 'ends_with'; // like '%value'
    case JSON_CONTAINS = 'json_contains';
    case BOOL = 'boolean';
    case EQUALS = 'equals';
    case IN = 'in';
    case IN_JSON_CONTAINS = 'in_json_contains'; // Checks if JSON array contains any of the provided values
}
