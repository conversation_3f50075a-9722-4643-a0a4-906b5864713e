<?php

declare(strict_types=1);

namespace App\Common;

use Symfony\Component\Uid\AbstractUid;
use Symfony\Component\Uid\Uuid as UuidSymfony;

final class Uuid extends AbstractUid
{
    private AbstractUid $uuid;

    public function __construct(AbstractUid $uuid)
    {
        $this->uuid = $uuid;
    }

    public static function generate(): self
    {
        return new self(UuidSymfony::v4());
    }

    /** @param Uuid $other */
    public function equals($other): bool
    {
        if (!$other instanceof self) {
            return false;
        }

        return $this->valueString() === $other->valueString();
    }

    public static function validate(string $uuid): bool
    {
        return UuidSymfony::isValid($uuid);
    }

    public static function fromString(string $uuid): static
    {
        return new static(UuidSymfony::fromString($uuid));
    }

    public function __toString(): string
    {
        return $this->valueString();
    }

    public function toString(): string
    {
        return $this->valueString();
    }

    public function jsonSerialize(): string
    {
        return $this->valueString();
    }

    public function valueString(): string
    {
        return $this->uuid->toRfc4122();
    }

    public static function isValid(string $uuid): bool
    {
        return UuidSymfony::isValid($uuid);
    }

    public function toBinary(): string
    {
        return $this->uuid->toBinary();
    }

    public function toBase58(): string
    {
        return $this->uuid->toBase58();
    }

    public function toRfc4122(): string
    {
        return $this->uuid->toRfc4122();
    }
}
