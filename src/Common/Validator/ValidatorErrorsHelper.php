<?php

declare(strict_types=1);

namespace App\Common\Validator;

use App\Common\Validator;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\Validator\ValidatorInterface;

final class ValidatorErrorsHelper implements Validator
{
    public function __construct(
        private readonly ValidatorInterface $validator,
    ) {
    }

    /** @return array<string, array<int, string|\Stringable>> */
    public function validate(object $object): array
    {
        $errors = $this->validator->validate($object);
        $arrayErrors = [];

        if ($errors->count() > 0) {
            /** @var ConstraintViolation $error */
            foreach ($errors as $error) {
                $arrayErrors[$error->getPropertyPath()][] = $error->getMessage();
            }
        }

        return $arrayErrors;
    }

    public function tryValidate(object $object): void
    {
        $errors = $this->validator->validate($object);
        if ($errors->count() > 0) {
            $arrayErrors = ['message' => '<PERSON>rz<PERSON>zane wartości są niepoprawne.'];
            $arrayErrors['details'] = [];
            /** @var ConstraintViolation $error */
            foreach ($errors as $error) {
                $arrayErrors['details'][$error->getPropertyPath()][] = $error->getMessage();
            }

            throw new \InvalidArgumentException((string) json_encode($arrayErrors, JSON_THROW_ON_ERROR), 422);
        }
    }
}
