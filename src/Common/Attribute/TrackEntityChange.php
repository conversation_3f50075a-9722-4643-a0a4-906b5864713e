<?php

declare(strict_types=1);

namespace App\Common\Attribute;

#[\Attribute(\Attribute::TARGET_CLASS)]
class TrackEntityChange
{
    private string $objectLogDetailsFunction;

    public function __construct(string $objectLogDetailsFunction)
    {
        $this->objectLogDetailsFunction = $objectLogDetailsFunction;
    }

    public function objectLogDetailsFunction(): string
    {
        return $this->objectLogDetailsFunction;
    }
}
