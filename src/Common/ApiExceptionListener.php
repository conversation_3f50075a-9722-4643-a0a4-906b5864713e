<?php

declare(strict_types=1);

namespace App\Common;

use Psr\Log\LoggerInterface;
use Sentry\State\Scope;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\HttpKernel\Exception\HttpExceptionInterface;

class ApiExceptionListener
{
    private LoggerInterface $logger;

    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    public function onKernelException(ExceptionEvent $event): void
    {
        if (!$event->isMainRequest()) {
            return;
        }

        $exception = $event->getThrowable();
        $request = $event->getRequest();

        if (str_starts_with($request->getPathInfo(), '/api/')) {
            $responseCode = $this->getResponseCode($exception);
            $message = $this->getMessageAndDetails($exception)['message'];
            $details = $this->getMessageAndDetails($exception)['details'];

            $responseData = [
                'message' => $message,
                'details' => $details,
            ];

            if ($_ENV['APP_ENV'] === 'dev' || $_ENV['APP_ENV'] === 'test') {
                $responseData = [
                    'message' => $message,
                    'details' => $details,
                    'called' => [
                        'file' => $exception->getTrace()[0]['file'] ?? null,
                        'line' => $exception->getTrace()[0]['line'] ?? null,
                    ],
                    'occurred' => [
                        'file' => $exception->getFile(),
                        'line' => $exception->getLine(),
                    ],
                    'trace' => $exception->getTrace(),
                ];

                if ($exception->getPrevious() instanceof \Exception) {
                    $responseData += [
                        'previous' => [
                            'message' => $exception->getPrevious()->getMessage(),
                            'exception' => get_class($exception->getPrevious()),
                            'file' => $exception->getPrevious()->getFile(),
                            'line' => $exception->getPrevious()->getLine(),
                            'trace' => $exception->getPrevious()->getTrace(),
                        ],
                    ];
                }
            }

            $response = new JsonResponse($responseData);
            $response->setStatusCode($responseCode);
            $event->setResponse($response);
        }

        // Send to SentryConfig
        \Sentry\captureException($exception);

        $this->logError($exception);
    }

    /** @return array{message: string, details: string} */
    private function getMessageAndDetails(\Throwable $exception): array
    {
        $message = $exception->getMessage();
        $details = '';

        $checkMessageIsJson = json_decode($exception->getMessage(), true, 10);
        if (json_last_error() === JSON_ERROR_NONE) {
            if ($checkMessageIsJson['message'] && $checkMessageIsJson['details']) {
                $message = $checkMessageIsJson['message'];
                $details = $checkMessageIsJson['details'];
            } else {
                $message = $checkMessageIsJson;
            }
        }

        return ['message' => $message, 'details' => $details];
    }

    private function getResponseCode(\Throwable $exception): int
    {
        if ($exception->getCode() >= 100 && $exception->getCode() < 600) {
            return $exception->getCode();
        }

        if ($exception->getPrevious() instanceof \Throwable) {
            if ($exception->getPrevious()->getCode() >= 100 && $exception->getPrevious()->getCode() < 600) {
                return $exception->getPrevious()->getCode();
            }
        }

        if ($exception instanceof HttpExceptionInterface) {
            return $exception->getStatusCode();
        }

        return Response::HTTP_INTERNAL_SERVER_ERROR;
    }

    private function logError(\Throwable $exception): void
    {
        \Sentry\configureScope(function (Scope $scope): void {
            $scope->setTag('doNotSendToSentry', '');
        });

        $log = [
            'code' => $this->getResponseCode($exception),
            'message' => $exception->getMessage(),
            'called' => [
                'file' => $exception->getTrace()[0]['file'] ?? null,
                'line' => $exception->getTrace()[0]['line'] ?? null,
            ],
            'occurred' => [
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
            ],
        ];

        if ($exception->getPrevious() instanceof \Exception) {
            $log += [
                'previous' => [
                    'message' => $exception->getPrevious()->getMessage(),
                    'exception' => get_class($exception->getPrevious()),
                    'file' => $exception->getPrevious()->getFile(),
                    'line' => $exception->getPrevious()->getLine(),
                ],
            ];
        }

        $this->logger->error($exception->getFile(), $log);

        \Sentry\configureScope(function (Scope $scope): void {
            $scope->removeTag('doNotSendToSentry');
        });
    }
}
