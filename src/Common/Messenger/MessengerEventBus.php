<?php

declare(strict_types=1);

namespace App\Common\Messenger;

use App\Common\Event;
use App\Common\EventBus;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Messenger\Stamp\DispatchAfterCurrentBusStamp;

class MessengerEventBus implements EventBus
{
    private MessageBusInterface $eventBus;

    public function __construct(MessageBusInterface $eventBus)
    {
        $this->eventBus = $eventBus;
    }

    public function publish(Event $event): void
    {
        /*  https://symfony.com/doc/current/messenger/dispatch_after_current_bus.html */
        $eventWithEnvelop = (new Envelope($event))
            ->with(new DispatchAfterCurrentBusStamp());

        $this->eventBus->dispatch($eventWithEnvelop);
    }

    public function publishEvents(array $events): void
    {
        /*  https://symfony.com/doc/current/messenger/dispatch_after_current_bus.html */
        foreach ($events as $event) {
            $eventWithEnvelop = (new Envelope($event))
                ->with(new DispatchAfterCurrentBusStamp());

            $this->eventBus->dispatch($eventWithEnvelop);
        }
    }
}
