<?php

declare(strict_types=1);

namespace App\Common\Messenger;

use App\Common\Validator;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Middleware\MiddlewareInterface;
use Symfony\Component\Messenger\Middleware\StackInterface;

final class ValidateMiddleware implements MiddlewareInterface
{
    public function __construct(
        private readonly Validator $validator,
    ) {
    }

    public function handle(Envelope $envelope, StackInterface $stack): Envelope
    {
        $this->validator->tryValidate($envelope->getMessage());

        return $stack->next()->handle($envelope, $stack);
    }
}
