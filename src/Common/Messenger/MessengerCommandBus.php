<?php

declare(strict_types=1);

namespace App\Common\Messenger;

use App\Common\Command;
use App\Common\CommandBus;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Messenger\Stamp\DispatchAfterCurrentBusStamp;

class Messenger<PERSON>ommandBus implements CommandBus
{
    private MessageBusInterface $commandBus;

    public function __construct(MessageBusInterface $commandBus)
    {
        $this->commandBus = $commandBus;
    }

    public function dispatch(Command $cmd, bool $dispatchAfterCurrentBus = false): void
    {
        /*  https://symfony.com/doc/current/messenger/dispatch_after_current_bus.html */
        if ($dispatchAfterCurrentBus === true) {
            $cmdWithEnvelop = (new Envelope($cmd))
                ->with(new DispatchAfterCurrentBusStamp());
            $this->commandBus->dispatch($cmdWithEnvelop);
        } else {
            $this->commandBus->dispatch($cmd);
        }
    }
}
