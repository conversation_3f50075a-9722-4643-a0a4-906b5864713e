<?php

declare(strict_types=1);

namespace App\Common\CorrelationId;

use App\Common\Uuid;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Webmozart\Assert\Assert;

class CorrelationIdRequestSubscriber implements EventSubscriberInterface
{
    public function __construct(private CorrelationIdStorage $correlationIdStorage)
    {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            RequestEvent::class => ['onKernelRequest', 256],
            ResponseEvent::class => 'onKernelResponse',
        ];
    }

    public function onKernelRequest(RequestEvent $event): void
    {
        if (!$event->isMainRequest()) {
            return;
        }

        $correlationId = $event->getRequest()->headers->get('x-correlation-id');

        if (empty($correlationId)) {
            $correlationId = Uuid::generate()->toRfc4122();
        } else {
            Assert::Uuid($correlationId, 'x-correlation-id is not a valid.');
        }

        $event->getRequest()->headers->set('x-correlation-id', $correlationId);
        $this->correlationIdStorage->setCorrelationId($correlationId);
    }

    public function onKernelResponse(ResponseEvent $event): void
    {
        $correlationId = $event->getRequest()->headers->get('x-correlation-id');

        $event->getResponse()->headers->set('x-correlation-id', $correlationId);
    }
}
