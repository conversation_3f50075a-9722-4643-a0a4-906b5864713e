<?php

declare(strict_types=1);

namespace App\Common\CorrelationId;

use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Middleware\MiddlewareInterface;
use Symfony\Component\Messenger\Middleware\StackInterface;

class AddCorrelationIdMiddleware implements MiddlewareInterface
{
    public function __construct(
        private RequestStack $requestStack,
        private CorrelationIdStorage $correlationIdStorage)
    {
    }

    public function handle(Envelope $envelope, StackInterface $stack): Envelope
    {
        $request = $this->requestStack->getCurrentRequest();
        if ($request && $correlationId = $request->headers->get('x-correlation-id')) {
            $envelope = $envelope->with(new CorrelationIdStamp($correlationId));
            $this->correlationIdStorage->setCorrelationId($correlationId);
        }

        return $stack->next()->handle($envelope, $stack);
    }
}
