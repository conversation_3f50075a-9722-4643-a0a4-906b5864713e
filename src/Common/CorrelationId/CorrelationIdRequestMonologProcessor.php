<?php

declare(strict_types=1);

namespace App\Common\CorrelationId;

use Monolog\LogRecord;
use Symfony\Component\HttpFoundation\RequestStack;

readonly class CorrelationIdRequestMonologProcessor
{
    public function __construct(
        private RequestStack $requestStack)
    {
    }

    public function __invoke(LogRecord $record): LogRecord
    {
        $request = $this->requestStack->getCurrentRequest();

        if (!$request) {
            return $record;
        }

        $correlationId = $request->headers->get('x-correlation-id');

        if (empty($correlationId)) {
            return $record;
        }

        // If we have a correlation id include it in every monolog line
        /* @phpstan-ignore-next-line */
        $record['extra']['correlation_id'] = $correlationId;

        return $record;
    }
}
