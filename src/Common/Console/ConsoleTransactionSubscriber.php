<?php

declare(strict_types=1);

namespace App\Common\Console;

use App\Common\Attribute\Transactional;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\ConsoleEvents;
use Symfony\Component\Console\Event\ConsoleCommandEvent;
use Symfony\Component\Console\Event\ConsoleTerminateEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class ConsoleTransactionSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            ConsoleEvents::COMMAND => 'onCommandStart',
            ConsoleEvents::TERMINATE => 'onCommandTerminate',
        ];
    }

    public function onCommandStart(ConsoleCommandEvent $event): void
    {
        $command = $event->getCommand();

        if ($command !== null) {
            $reflectionClass = new \ReflectionClass(get_class($command));

            if ($reflectionClass->getAttributes(Transactional::class)) {
                $this->entityManager->beginTransaction();
            }
        }
    }

    public function onCommandTerminate(ConsoleTerminateEvent $event): void
    {
        if ($this->entityManager->getConnection()->isTransactionActive()) {
            if ($event->getExitCode() === 0) {
                $this->entityManager->commit();
            } else {
                $this->entityManager->rollback();
            }
        }
    }
}
