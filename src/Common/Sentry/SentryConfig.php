<?php

declare(strict_types=1);

namespace App\Common\Sentry;

use App\Auth\Infrastructure\Security\UserSecurity;
use Sentry\UserDataBag;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpFoundation\RequestStack;

readonly class SentryConfig
{
    public function __construct(
        private Security $security,
        private RequestStack $requestStack,
    ) {
    }

    public function getBeforeSend(): callable
    {
        return function (\Sentry\Event $event): ?\Sentry\Event {
            if (array_key_exists('doNotSendToSentry', $event->getTags())) {
                return null;
            }

            $correlationId = $this->requestStack->getCurrentRequest()?->headers->get('x-correlation-id');
            if (!empty($correlationId)) {
                $event->setTag('correlationId', $correlationId);
            }

            /** @var ?UserSecurity $user */
            $user = $this->security->getUser();

            $userIdentifier = $user?->getRootId();
            if ($userIdentifier) {
                $event->setUser(UserDataBag::createFromUserIdentifier($userIdentifier));
            }

            return $event;
        };
    }
}
