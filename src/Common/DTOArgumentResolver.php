<?php

declare(strict_types=1);

namespace App\Common;

use App\Common\Exception\BadContentTypeFormat;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Controller\ValueResolverInterface;
use Symfony\Component\HttpKernel\ControllerMetadata\ArgumentMetadata;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\PropertyInfo\Extractor\PhpDocExtractor;
use Symfony\Component\PropertyInfo\Extractor\ReflectionExtractor;
use Symfony\Component\PropertyInfo\PropertyInfoExtractor;
use Symfony\Component\Serializer\Encoder\JsonEncoder;
use Symfony\Component\Serializer\Exception\ExceptionInterface;
use Symfony\Component\Serializer\Normalizer\ArrayDenormalizer;
use Symfony\Component\Serializer\Normalizer\PropertyNormalizer;
use Symfony\Component\Serializer\Serializer;
use Symfony\Component\Serializer\SerializerInterface;

class DTOArgumentResolver implements ValueResolverInterface
{
    private SerializerInterface $serializer;

    public function __construct(
        private readonly Validator $validator,
    ) {
        $this->serializer = new Serializer(
            [
                new ArrayDenormalizer(),
                new PropertyNormalizer(null, null, new PropertyInfoExtractor([], [new PhpDocExtractor(), new ReflectionExtractor()])),
            ],
            [new JsonEncoder()]
        );
    }

    /** @return iterable<int, RequestDtoInterface> */
    public function resolve(Request $request, ArgumentMetadata $argument): iterable
    {
        if (!$this->isSupport($argument)) {
            return;
        }

        try {
            $dto = '';

            if ($request->getContentTypeFormat() === 'json') {
                $content = $request->getContent();
                $data = json_decode($content, true, 512, JSON_THROW_ON_ERROR);

                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw BadContentTypeFormat::becauseBodyContentTypeIsWrong();
                }

                if (is_array($data)) {
                    $data = $this->trimData($data);
                    $dto = $this->serializer->deserialize(json_encode($data), (string) $argument->getType(), 'json');
                }
            } elseif ($request->getContentTypeFormat() === 'form') {
                $data = $request->request->all();
                if ($request->files->count() > 0) {
                    foreach ($request->files->all() as $fieldName => $uploadFile) {
                        $data[$fieldName] = $uploadFile;
                    }
                }

                $className = $argument->getType();
                /** @phpstan-ignore-next-line */
                $dto = $className::createFromDataForm($data);
            } else {
                throw BadContentTypeFormat::becauseBodyContentTypeIsWrong();
            }
        } catch (ExceptionInterface $exception) {
            throw new BadRequestHttpException($exception->getMessage(), $exception);
        }

        $this->validator->tryValidate($dto);

        yield $dto;
    }

    private function isSupport(ArgumentMetadata $argument): bool
    {
        $argumentType = (string) $argument->getType();

        if (is_subclass_of($argumentType, RequestDtoInterface::class)) {
            return true;
        }

        return false;
    }

    /**
     * @param array<string|int, mixed> $data
     *
     * @return array<string|int, mixed>
     */
    private function trimData(array $data): array
    {
        array_walk_recursive($data, function (&$value): void {
            if (is_string($value)) {
                $value = trim($value);
            }
        });

        return $data;
    }
}
