<?php

declare(strict_types=1);

namespace App\Common;

use App\Common\LoggerInterface as AppLoggerInterface;
use Psr\Log\LoggerInterface as PsrLoggerInterface;

class AppLogger implements AppLoggerInterface
{
    public function __construct(private readonly PsrLoggerInterface $logger)
    {
    }

    public function logExceptions(\Throwable $exception): void
    {
        $log = [
            'code' => $exception->getCode(),
            'message' => $exception->getMessage(),
            'exception' => get_class($exception),
            'called' => [
                'file' => $exception->getTrace()[0]['file'] ?? null,
                'line' => $exception->getTrace()[0]['line'] ?? null,
            ],
            'occurred' => [
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
            ],
        ];

        if (method_exists($exception, 'getHttpBodyContent')) {
            $log += [
                'httpBodyContent' => $exception->getHttpBodyContent(),
            ];
        }

        $previous = $exception->getPrevious();
        if ($previous !== null && $previous !== $exception) {
            $log += [
                'previous' => [
                    'message' => $previous->getMessage(),
                    'exception' => get_class($previous),
                    'file' => $previous->getFile(),
                    'line' => $previous->getLine(),
                ],
            ];

            if (method_exists($previous, 'getHttpBodyContent')) {
                $log['previous'] += [
                    'httpBodyContent' => $previous->getHttpBodyContent(),
                ];
            }
        }

        $this->logger->critical($exception->getMessage(), $log);
    }
}
