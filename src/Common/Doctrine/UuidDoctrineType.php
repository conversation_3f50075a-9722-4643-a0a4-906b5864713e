<?php

declare(strict_types=1);

namespace App\Common\Doctrine;

use App\Common\Uuid;
use Doctrine\DBAL\Platforms\AbstractPlatform;
use Doctrine\DBAL\Types\ConversionException;
use Doctrine\DBAL\Types\GuidType;
use Symfony\Component\Uid\AbstractUid;

final class UuidDoctrineType extends GuidType
{
    public const NAME = 'uuid_symfony';

    public function convertToPHPValue($value, AbstractPlatform $platform): ?AbstractUid
    {
        if ($value === null || $value === '') {
            return null;
        }

        if ($value instanceof AbstractUid) {
            return $value;
        }

        try {
            $uuid = Uuid::fromString($value);
        } catch (\InvalidArgumentException $e) {
            throw ConversionException::conversionFailed($value, static::NAME);
        }

        return $uuid;
    }

    public function convertToDatabaseValue($value, AbstractPlatform $platform): ?string
    {
        if ($value === null || $value === '') {
            return null;
        }

        if ($value instanceof Uuid) {
            return $value->toString();
        }

        if (is_string($value)) {
            return Uuid::fromString($value)->toString();
        }

        throw ConversionException::conversionFailed($value, self::NAME);
    }

    /** {@inheritdoc} */
    public function getName(): string
    {
        return self::NAME;
    }

    /** {@inheritdoc} */
    public function getDefaultLength(AbstractPlatform $platform): int
    {
        return 36;
    }

    public function getSQLDeclaration(array $column, AbstractPlatform $platform): string
    {
        return $platform->getVarcharTypeDeclarationSQL(['length' => $this->getDefaultLength($platform), 'fixed' => true]);
    }
}
