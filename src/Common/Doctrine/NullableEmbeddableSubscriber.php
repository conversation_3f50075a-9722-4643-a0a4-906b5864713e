<?php

declare(strict_types=1);

namespace App\Common\Doctrine;

use App\Common\Attribute\EntityWithNullableEmbeddable;
use App\Common\Attribute\NullableEmbeddable;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\ORM\Event\PostLoadEventArgs;
use Doctrine\ORM\Events;

#[AsDoctrineListener('postLoad')]
final class NullableEmbeddableSubscriber
{
    /** @return string[] */
    public function getSubscribedEvents(): array
    {
        return [
            Events::postLoad,
        ];
    }

    public function postLoad(PostLoadEventArgs $args): void
    {
        $entity = $args->getObject();

        $reflector = new \ReflectionClass($entity);
        $attributes = $reflector->getAttributes(EntityWithNullableEmbeddable::class);
        if (empty($attributes)) {
            return;
        }

        $objectReflection = new \ReflectionObject($entity);
        foreach ($objectReflection->getProperties() as $property) {
            $propertyAttributes = $property->getAttributes(NullableEmbeddable::class);
            if (empty($propertyAttributes)) {
                continue;
            }
            $value = $property->getValue($entity);
            if ($this->allPropertiesAreNull($value)) {
                $property->setValue($entity, null);
            }
        }
    }

    private function allPropertiesAreNull(mixed $object): bool
    {
        $objectReflection = new \ReflectionObject($object);
        foreach ($objectReflection->getProperties() as $property) {
            if ($property->isInitialized($object) && null !== $property->getValue($object)) {
                return false;
            }
        }

        return true;
    }
}
