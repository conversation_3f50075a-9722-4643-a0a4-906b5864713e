<?php

declare(strict_types=1);

namespace App\Account\Application\Query;

use App\Common\Query\GridConfiguration;
use App\Common\Query\GridResult;

interface AccountQueryInterface
{
    public function findByAccountId(string $accountId): ?AccountView;

    /** @return array<AccountView> */
    public function findAll(?\DateTimeImmutable $changedSince = null): array;

    /** @return GridResult<string, AccountView> */
    public function findForGrid(GridConfiguration $gridConfiguration): GridResult;

    public function checkIfEmailExists(string $email, ?string $excludeAccountId = null): bool;

    public function checkIfMobilePhoneNumberExists(string $mobilePhoneNumber, ?string $excludeAccountId = null): bool;

    public function hospitalIsUsed(string $hospitalId): bool;
}
