<?php

declare(strict_types=1);

namespace App\Account\Application\Query;

use App\Common\QueryView;

readonly class HospitalSelectView implements QueryView
{
    public function __construct(
        private string $hospitalId,
        private string $shortName,
        private string $fullName,
    ) {
    }

    /** @param array<string, string> $data */
    public static function deserialize(array $data): self
    {
        return new self(
            $data['hospitalId'],
            $data['shortName'],
            $data['fullName'],
        );
    }

    public function jsonSerialize(): mixed
    {
        return [
            'hospitalId' => $this->hospitalId,
            'shortName' => $this->shortName,
            'fullName' => $this->fullName,
        ];
    }

    public function hospitalId(): string
    {
        return $this->hospitalId;
    }

    public function shortName(): string
    {
        return $this->shortName;
    }

    public function fullName(): string
    {
        return $this->fullName;
    }
}
