<?php

declare(strict_types=1);

namespace App\Account\Application;

use App\Account\Application\Query\AccountQueryInterface;

readonly class AccountHospitalFacade
{
    public function __construct(
        private AccountQueryInterface $accountQuery,
    ) {
    }

    public function hospitalIsUsed(string $hospitalId): bool
    {
        return $this->accountQuery->hospitalIsUsed($hospitalId);
    }
}
