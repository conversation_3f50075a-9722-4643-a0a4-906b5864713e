<?php

declare(strict_types=1);

namespace App\Account\Application\Exception;

class AccountCannotBeCreated extends \Exception
{
    public static function becauseThisEmailAsAlreadyInUse(string $email): self
    {
        return new self(sprintf('Konto z adresem email %s już istnieje. Nie można utworzyć nowego konta.', $email), 409);
    }

    public static function becauseThisMobilePhoneNumberAsAlreadyInUse(string $phoneNumber): self
    {
        return new self(sprintf('Konto z numerem telefonu %s już istnieje. Nie można utworzyć nowego konta.', $phoneNumber), 409);
    }

    public static function becauseOneOrMoreHospitalsDoNotExist(): self
    {
        return new self('Jeden lub więcej z podanych szpitali nie istnieje.', 422);
    }
}
