<?php

declare(strict_types=1);

namespace App\Account\Application\Exception;

class AccountCannotBeUpdated extends \Exception
{
    public static function becauseThisEmailAsAlreadyInUse(string $email): self
    {
        return new self(sprintf('Konto z adresem email %s już istnieje. Nie można zaktualizować konta.', $email), 409);
    }

    public static function becauseThisMobilePhoneNumberAsAlreadyInUse(string $phoneNumber): self
    {
        return new self(sprintf('Konto z numerem telefonu %s już istnieje. Nie można zaktualizować konta.', $phoneNumber), 409);
    }

    public static function becauseOneOrMoreHospitalsDoNotExist(): self
    {
        return new self('Jeden lub więcej z podanych szpitali nie istnieje, lub jest nieaktywny.', 422);
    }
}
