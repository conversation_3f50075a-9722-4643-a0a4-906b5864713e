<?php

declare(strict_types=1);

namespace App\Account\Application;

use App\Account\Application\Query\AccountQueryInterface;
use App\Account\Application\Query\AccountView;
use App\Account\Application\Service\AccountService;
use App\Common\Query\GridConfiguration;
use App\Common\Query\GridResult;
use App\Common\Uuid;
use App\SharedKernel\Email;
use App\SharedKernel\Roles;

final readonly class AccountFacade
{
    public function __construct(
        private AccountService $accountService,
        private AccountQueryInterface $accountQuery,
    ) {
    }

    /**
     * @param array<string> $protocols
     * @param array<string> $hospitals
     */
    public function create(
        string $accountId,
        string $firstName,
        string $lastName,
        string $email,
        string $mobilePhoneNumber,
        string $role,
        array $protocols = [],
        array $hospitals = [],
    ): void {
        $this->accountService->createAccount(
            Uuid::fromString($accountId),
            $firstName,
            $lastName,
            Email::create($email),
            $mobilePhoneNumber,
            Roles::from($role),
            $protocols,
            $hospitals,
        );
    }

    /**
     * @param array<string> $protocols
     * @param array<string> $hospitals
     */
    public function update(
        string $accountId,
        string $firstName,
        string $lastName,
        string $mobilePhoneNumber,
        array $protocols = [],
        array $hospitals = [],
    ): void {
        $this->accountService->update(
            Uuid::fromString($accountId),
            $firstName,
            $lastName,
            $mobilePhoneNumber,
            $protocols,
            $hospitals,
        );
    }

    public function changeEmail(string $accountId, string $email): void
    {
        $this->accountService->changeEmail(Uuid::fromString($accountId), $email);
    }

    public function activate(string $accountId): void
    {
        $this->accountService->activate(Uuid::fromString($accountId));
    }

    public function deactivate(string $accountId, string $reason): void
    {
        $this->accountService->deactivate(Uuid::fromString($accountId), $reason);
    }

    public function findByAccountId(string $accountId): ?AccountView
    {
        return $this->accountQuery->findByAccountId($accountId);
    }

    /** @return array<AccountView> */
    public function findAll(?\DateTimeImmutable $changedSince = null): array
    {
        return $this->accountQuery->findAll($changedSince);
    }

    /** @return GridResult<string, AccountView> */
    public function findForGrid(GridConfiguration $gridConfiguration): GridResult
    {
        return $this->accountQuery->findForGrid($gridConfiguration);
    }
}
