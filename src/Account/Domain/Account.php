<?php

declare(strict_types=1);

namespace App\Account\Domain;

use App\Account\Domain\Exception\AccountCannotBeActivated;
use App\Account\Domain\Exception\AccountCannotBeCreated;
use App\Account\Domain\Exception\AccountCannotBeDeactivated;
use App\Account\Domain\Exception\AccountCannotBeUpdated;
use App\Common\Attribute\TrackEntityChange;
use App\Common\Uuid;
use App\SharedKernel\Email;
use App\SharedKernel\Roles;

use function Symfony\Component\Clock\now;

#[TrackEntityChange(objectLogDetailsFunction: 'logDetails')]
class Account
{
    private bool $isActive;
    private ?string $deactivationReason = null;
    private \DateTimeImmutable $createdAt;
    private ?\DateTimeImmutable $updatedAt = null;
    private int $version;

    private function __construct(
        private Uuid $accountId,
        private string $firstName,
        private string $lastName,
        private Email $email,
        private string $mobilePhoneNumber,
        private readonly Roles $role,
        /** @var array<string> */
        private array $protocols = [],
        /** @var array<string> */
        private array $hospitals = [],
    ) {
        $this->isActive = true;
        $this->createdAt = now();
    }

    public static function createCentralAdminAccount(
        Uuid $accountId,
        string $firstName,
        string $lastName,
        Email $email,
        string $mobilePhoneNumber,
    ): self {
        return new self($accountId,
            $firstName,
            $lastName,
            $email,
            $mobilePhoneNumber,
            Roles::ROLE_CENTRAL_ADMINISTRATOR,
            [],
            []
        );
    }

    /**
     * @param array<string> $protocols
     * @param array<string> $hospitals
     */
    public static function createReportingPhysician(
        Uuid $accountId,
        string $firstName,
        string $lastName,
        Email $email,
        string $mobilePhoneNumber,
        array $protocols,
        array $hospitals,
    ): self {
        if (empty($protocols)) {
            throw AccountCannotBeCreated::becauseAtLeastOneProtocolIsRequired();
        }

        if (empty($hospitals)) {
            throw AccountCannotBeCreated::becauseAtLeastOneHospitalIsRequired();
        }

        return new self($accountId,
            $firstName,
            $lastName,
            $email,
            $mobilePhoneNumber,
            Roles::ROLE_REPORTER_PHYSICIAN,
            $protocols,
            $hospitals
        );
    }

    /** @param array<string> $protocols */
    public static function createCoordinator(
        Uuid $accountId,
        string $firstName,
        string $lastName,
        Email $email,
        string $mobilePhoneNumber,
        array $protocols,
    ): self {
        if (empty($protocols)) {
            throw AccountCannotBeCreated::becauseAtLeastOneProtocolIsRequired();
        }

        return new self($accountId,
            $firstName,
            $lastName,
            $email,
            $mobilePhoneNumber,
            Roles::ROLE_COORDINATOR,
            $protocols
        );
    }

    /** @param array<string> $hospitals */
    public static function createDataAdministrator(
        Uuid $accountId,
        string $firstName,
        string $lastName,
        Email $email,
        string $mobilePhoneNumber,
        array $hospitals,
    ): self {
        if (empty($hospitals)) {
            throw AccountCannotBeCreated::becauseAtLeastOneHospitalIsRequired();
        }

        return new self($accountId,
            $firstName,
            $lastName,
            $email,
            $mobilePhoneNumber,
            Roles::ROLE_DATA_ADMINISTRATOR,
            [],
            $hospitals
        );
    }

    public function updateCentralAdminAccount(
        string $firstName,
        string $lastName,
        string $mobilePhoneNumber,
    ): void {
        $this->firstName = $firstName;
        $this->lastName = $lastName;
        $this->mobilePhoneNumber = $mobilePhoneNumber;
        $this->updatedAt = now();
    }

    /**
     * @param array<string> $protocols
     * @param array<string> $hospitals
     */
    public function updateReportingPhysician(
        string $firstName,
        string $lastName,
        string $mobilePhoneNumber,
        array $protocols,
        array $hospitals,
    ): void {
        if (empty($protocols)) {
            throw AccountCannotBeUpdated::becauseAtLeastOneProtocolIsRequired();
        }

        if (empty($hospitals)) {
            throw AccountCannotBeUpdated::becauseAtLeastOneHospitalIsRequired();
        }

        $this->firstName = $firstName;
        $this->lastName = $lastName;
        $this->mobilePhoneNumber = $mobilePhoneNumber;
        $this->protocols = $protocols;
        $this->hospitals = $hospitals;
        $this->updatedAt = now();
    }

    /** @param array<string> $protocols */
    public function updateCoordinator(
        string $firstName,
        string $lastName,
        string $mobilePhoneNumber,
        array $protocols,
    ): void {
        if (empty($protocols)) {
            throw AccountCannotBeUpdated::becauseAtLeastOneProtocolIsRequired();
        }

        $this->firstName = $firstName;
        $this->lastName = $lastName;
        $this->mobilePhoneNumber = $mobilePhoneNumber;
        $this->protocols = $protocols;
        $this->updatedAt = now();
    }

    /** @param array<string> $hospitals */
    public function updateDataAdministrator(
        string $firstName,
        string $lastName,
        string $mobilePhoneNumber,
        array $hospitals,
    ): void {
        if (empty($hospitals)) {
            throw AccountCannotBeUpdated::becauseAtLeastOneHospitalIsRequired();
        }

        $this->firstName = $firstName;
        $this->lastName = $lastName;
        $this->mobilePhoneNumber = $mobilePhoneNumber;
        $this->hospitals = $hospitals;
        $this->updatedAt = now();
    }

    public function activate(): void
    {
        if ($this->isActive === true) {
            throw AccountCannotBeActivated::becauseIsAlreadyActive();
        }

        $this->isActive = true;
        $this->deactivationReason = null;
        $this->updatedAt = now();
    }

    public function deactivate(string $reason): void
    {
        if ($this->isActive === false) {
            throw AccountCannotBeDeactivated::becauseIsAlreadyInactive();
        }

        $this->isActive = false;
        $this->deactivationReason = $reason;
        $this->updatedAt = now();
    }

    public function changeEmail(Email $email): void
    {
        $this->email = $email;
        $this->updatedAt = now();
    }

    public function accountId(): string
    {
        return $this->accountId->valueString();
    }

    /** @return array<string, mixed> */
    public function logDetails(): array
    {
        return [
            'firstName' => $this->firstName,
            'lastName' => $this->lastName,
            'email' => $this->email->valueString(),
        ];
    }

    public function firstName(): string
    {
        return $this->firstName;
    }

    public function lastName(): string
    {
        return $this->lastName;
    }

    public function email(): Email
    {
        return $this->email;
    }

    public function mobilePhoneNumber(): string
    {
        return $this->mobilePhoneNumber;
    }

    public function createAt(): \DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function updatedAt(): ?\DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function role(): Roles
    {
        return $this->role;
    }

    public function isActive(): bool
    {
        return $this->isActive;
    }
}
