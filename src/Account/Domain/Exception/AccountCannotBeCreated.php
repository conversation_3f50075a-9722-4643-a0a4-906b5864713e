<?php

declare(strict_types=1);

namespace App\Account\Domain\Exception;

class AccountCannotBeCreated extends \Exception
{
    public static function becauseAtLeastOneProtocolIsRequired(): self
    {
        return new self('Przynajmniej jedna jednostka chorobowa jest wymagana.', 409);
    }

    public static function becauseAtLeastOneHospitalIsRequired(): self
    {
        return new self('Przynajmniej jeden szpital jest wymagany.', 422);
    }
}
