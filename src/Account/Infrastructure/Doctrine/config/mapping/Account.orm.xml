<doctrine-mapping xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping
                                      https://www.doctrine-project.org/schemas/orm/doctrine-mapping.xsd">

    <entity name="App\Account\Domain\Account" table="account_account">

        <id name="accountId" type="uuid_symfony">
            <generator strategy="NONE"/>
        </id>

        <unique-constraints>
            <unique-constraint columns="email"/>
            <unique-constraint columns="mobilePhoneNumber"/>
        </unique-constraints>

        <indexes>
            <index columns="accountId"/>
            <index columns="email"/>
            <index columns="mobilePhoneNumber"/>
        </indexes>

        <field name="firstName" length="50"/>
        <field name="lastName" length="50"/>
        <embedded name="email" class="App\SharedKernel\Email" use-column-prefix="false"/>
        <field name="mobilePhoneNumber" length="20"/>
        <field name="role" enum-type="App\SharedKernel\Roles"/>
        <field name="isActive" type="boolean"/>
        <field name="deactivationReason" length="1000" nullable="true"/>
        <field name="createdAt" type="datetime_immutable"/>
        <field name="updatedAt" type="datetime_immutable" nullable="true"/>
        <field name="version" type="integer" version="true"/>

        <field name="protocols" type="json"/>
        <field name="hospitals" type="json"/>

    </entity>
</doctrine-mapping>
