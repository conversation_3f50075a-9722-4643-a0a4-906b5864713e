<?php

declare(strict_types=1);

namespace App\Account\Infrastructure\Query;

use App\Account\Application\Query\AccountQueryInterface;
use App\Account\Application\Query\AccountView;
use App\Common\Query\GridConfiguration;
use App\Common\Query\GridResult;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Query\QueryBuilder;

class DbalAccountQuery implements AccountQueryInterface
{
    public const string TABLE_NAME = 'account_account';

    public function __construct(private readonly Connection $connection)
    {
    }

    public function findByAccountId(string $accountId): ?AccountView
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('accountId', 'firstName', 'lastName', 'email', 'mobilePhoneNumber', 'protocols', 'hospitals',
                'role', 'isActive', 'deactivationReason', 'createdAt', 'updatedAt')
            ->from(self::TABLE_NAME, 'aa')
            ->andWhere('aa.accountId = :accountId')
            ->setParameter('accountId', $accountId);

        $account = $this->connection->fetchAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        if ($account === false) {
            return null;
        }

        return AccountView::deserialize($account);
    }

    public function findAll(?\DateTimeImmutable $changedSince = null): array
    {
        $queryBuilder = $this->connection->createQueryBuilder();

        $queryBuilder
            ->select('aa.*')
            ->from(self::TABLE_NAME, 'aa');

        if ($changedSince !== null) {
            $queryBuilder
                ->andWhere('aa.createdAt >= :changedSince OR aa.updatedAt >= :changedSince')
                ->setParameter('changedSince', $changedSince->format('Y-m-d H:i:s'));
        }

        $data = $this->connection->fetchAllAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        $accounts = [];
        foreach ($data as $accountData) {
            $accounts[] = AccountView::deserialize($accountData);
        }

        return $accounts;
    }

    public function checkIfEmailExists(string $email, ?string $excludeAccountId = null): bool
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('email')
            ->from(self::TABLE_NAME, 'aa')
            ->andWhere('aa.email = :email')
            ->setParameter('email', $email);

        if ($excludeAccountId !== null) {
            $queryBuilder
                ->andWhere('aa.accountId != :excludeAccountId')
                ->setParameter('excludeAccountId', $excludeAccountId);
        }

        $account = $this->connection->fetchAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        return $account === false;
    }

    public function checkIfMobilePhoneNumberExists(string $mobilePhoneNumber, ?string $excludeAccountId = null): bool
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('mobilePhoneNumber')
            ->from(self::TABLE_NAME, 'aa')
            ->andWhere('aa.mobilePhoneNumber = :mobilePhoneNumber')
            ->setParameter('mobilePhoneNumber', $mobilePhoneNumber);

        if ($excludeAccountId !== null) {
            $queryBuilder
                ->andWhere('aa.accountId != :excludeAccountId')
                ->setParameter('excludeAccountId', $excludeAccountId);
        }

        $account = $this->connection->fetchAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        return $account === false;
    }

    /** @return GridResult<string, AccountView> */
    public function findForGrid(GridConfiguration $gridConfiguration): GridResult
    {
        $baseQueryBuilder = $this->prepareQueryForGrid($gridConfiguration);

        // Count total items for pagination
        $queryBuilderTotalItems = clone $baseQueryBuilder;
        $queryBuilderTotalItems->resetOrderBy()
            ->select('COUNT(DISTINCT accountId)');
        $totalItems = (int) $this->connection->fetchOne($queryBuilderTotalItems->getSQL(), $queryBuilderTotalItems->getParameters());

        // Add pagination
        $queryBuilder = clone $baseQueryBuilder;
        $queryBuilder->setFirstResult(($gridConfiguration->page() - 1) * $gridConfiguration->itemsPerPage())
            ->setMaxResults($gridConfiguration->itemsPerPage());

        $data = $this->connection->fetchAllAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        $collection = new ArrayCollection();

        foreach ($data as $key => $row) {
            $collection->set($row['accountId'], AccountView::deserialize($row));
        }

        return new GridResult($collection,
            $totalItems,
            $gridConfiguration->page(),
            $gridConfiguration->itemsPerPage()
        );
    }

    private function prepareQueryForGrid(GridConfiguration $gridConfiguration): QueryBuilder
    {
        $queryBuilder = $this->connection->createQueryBuilder();

        $queryBuilder
            ->select('accountId', 'firstName', 'lastName', 'email', 'mobilePhoneNumber', 'protocols', 'hospitals',
                'role', 'isActive', 'createdAt', 'updatedAt')
            ->from(self::TABLE_NAME, 'aa');

        if (count($gridConfiguration->sorts()) === 0) {
            $queryBuilder->addOrderBy('aa.createdAt', 'ASC');
        }

        $gridConfiguration->addSortAndFilterToDbalQueryBuilder($queryBuilder);

        return $queryBuilder;
    }

    public function hospitalIsUsed(string $hospitalId): bool
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('COUNT(*)')
            ->from(self::TABLE_NAME, 'aa')
            ->andWhere('JSON_CONTAINS(aa.hospitals, :hospitalId)')
            ->setParameter('hospitalId', json_encode([$hospitalId]));

        $count = (int) $this->connection->fetchOne($queryBuilder->getSQL(), $queryBuilder->getParameters());

        return $count > 0;
    }
}
