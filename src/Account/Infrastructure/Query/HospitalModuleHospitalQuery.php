<?php

declare(strict_types=1);

namespace App\Account\Infrastructure\Query;

use App\Account\Application\Query\HospitalQueryInterface;
use App\Account\Application\Query\HospitalSelectView;
use App\Hospital\Application\HospitalFacade;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;

readonly class HospitalModuleHospitalQuery implements HospitalQueryInterface
{
    public function __construct(private HospitalFacade $hospitalFacade)
    {
    }

    /** @return Collection<string, HospitalSelectView> */
    public function findAllActiveHospitalSelect(): Collection
    {
        $activeHospitals = $this->hospitalFacade->findAllActiveHospitalSelect();
        /** @var ArrayCollection<string, HospitalSelectView> $hospitalViews */
        $hospitalViews = new ArrayCollection();

        foreach ($activeHospitals as $hospital) {
            $hospitalView = new HospitalSelectView(
                $hospital->hospitalId(),
                $hospital->shortName(),
                $hospital->fullName()
            );

            $hospitalViews->set($hospital->hospitalId(), $hospitalView);
        }

        return $hospitalViews;
    }
}
