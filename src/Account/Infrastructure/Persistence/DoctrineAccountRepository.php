<?php

declare(strict_types=1);

namespace App\Account\Infrastructure\Persistence;

use App\Account\Domain\Account;
use App\Account\Domain\AccountRepositoryInterface;
use App\Account\Infrastructure\Exception\AccountNotExist;
use App\Common\Uuid;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Account>
 */
class DoctrineAccountRepository extends ServiceEntityRepository implements AccountRepositoryInterface
{
    public function __construct(
        private readonly EntityManagerInterface $em,
        ManagerRegistry $registry,
    ) {
        parent::__construct($registry, Account::class);
    }

    public function add(Account $account): void
    {
        $this->em->persist($account);
        $this->em->flush();
    }

    public function update(Account $account): void
    {
        $this->em->flush();
    }

    public function getByAccountId(Uuid $accountId): Account
    {
        $account = $this->em->getRepository(Account::class)->findOneBy([
            'accountId' => $accountId->valueString(),
        ]);

        if (!$account) {
            throw AccountNotExist::becauseNotExistInDatabase($accountId->valueString());
        }

        return $account;
    }
}
