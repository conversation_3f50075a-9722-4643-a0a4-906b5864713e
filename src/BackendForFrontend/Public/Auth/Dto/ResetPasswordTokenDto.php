<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Public\Auth\Dto;

use App\Common\RequestDtoInterface;
use Symfony\Component\Validator\Constraints as Assert;

class ResetPasswordTokenDto implements RequestDtoInterface
{
    public function __construct(
        #[Assert\NotBlank]
        #[Assert\Length(
            min: 40,
            max: 40,
            minMessage: 'The reset password link is invalid. Please try to reset your password again.',
            maxMessage: 'The reset password link is invalid. Please try to reset your password again.'
        )]
        public string $token,
    ) {
    }

    public function token(): string
    {
        return $this->token;
    }
}
