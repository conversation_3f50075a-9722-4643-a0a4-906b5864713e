<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Public\Auth\Dto;

use App\Common\RequestDtoInterface;
use Symfony\Component\Validator\Constraints as Assert;

class ResetPasswordDto implements RequestDtoInterface
{
    #[Assert\NotBlank()]
    #[Assert\Length(max: 100)]
    #[Assert\Email()]
    public string $email;

    private function __construct()
    {
    }

    public static function create(string $email): self
    {
        $dto = new self();
        $dto->email = $email;

        return $dto;
    }
}
