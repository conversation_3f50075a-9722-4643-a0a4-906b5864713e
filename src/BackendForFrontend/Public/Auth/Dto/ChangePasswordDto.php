<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Public\Auth\Dto;

use App\Common\RequestDtoInterface;
use Symfony\Component\Validator\Constraints as Assert;

class ChangePasswordDto implements RequestDtoInterface
{
    #[Assert\NotBlank]
    #[Assert\Length(
        min: 40,
        max: 40,
        minMessage: 'The reset password link is invalid. Please try to reset your password again.',
        maxMessage: 'The reset password link is invalid. Please try to reset your password again.'
    )]
    public string $token;
    #[Assert\NotBlank()]
    #[Assert\Length(min: 8, max: 12, )]
    public string $newPassword1;
    #[Assert\NotBlank()]
    #[Assert\Length(min: 8, max: 12, )]
    #[Assert\EqualTo(propertyPath: 'newPassword1', message: 'Passwords do not match. ')]
    public string $newPassword2;

    private function __construct()
    {
    }

    public static function create(string $token, string $newPassword1, string $newPassword2): self
    {
        $dto = new self();
        $dto->token = $token;
        $dto->newPassword1 = $newPassword1;
        $dto->newPassword2 = $newPassword2;

        return $dto;
    }
}
