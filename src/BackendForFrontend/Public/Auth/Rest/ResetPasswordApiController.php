<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Public\Auth\Rest;

use App\Auth\Application\NoAuthFacade;
use App\Auth\Infrastructure\ParameterBag;
use App\BackendForFrontend\Public\Auth\Dto\ChangePasswordDto;
use App\BackendForFrontend\Public\Auth\Dto\ResetPasswordDto;
use App\BackendForFrontend\Public\Auth\Dto\ResetPasswordTokenDto;
use App\Common\BaseController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpKernel\Attribute\MapQueryString;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/reset-password')]
class ResetPasswordApiController extends BaseController
{
    public function __construct(
        private readonly NoAuthFacade $noAuthFacade,
        private readonly ParameterBag $parameterBag,
    ) {
    }

    #[IsGranted('PUBLIC_ACCESS')]
    #[Route('', name: 'auth_forgot_password_request', methods: 'PUT')]
    public function forgotPasswordRequest(ResetPasswordDto $dto): JsonResponse
    {
        $this->noAuthFacade->forgotPasswordRequest($dto->email);

        $tokenLifetimeInMinutes = $this->parameterBag->getResetPasswordRequestLifetime() / 60;
        $message = 'Dziękujemy za inicjowanie procesu resetowania hasła.';
        $message .= 'Proszę sprawdzić swoją skrzynkę e-mail, gdzie znajdzie Pan/Pani wiadomość z linkiem lub kodem resetowania hasła.';
        $message .= "Upewnij się, że link/kod jest trzymany w tajemnicy i użyj go w ciągu $tokenLifetimeInMinutes min.";
        $message .= 'Po zresetowaniu hasła, proszę zapamiętać nowe hasło i zachować je w tajemnicy.';
        $message .= 'Jeśli potrzebujesz pomocy, skontaktuj się z naszym działem wsparcia pod adresem [kontakt do wsparcia].';

        return new JsonResponse([
            'status' => 'success',
            'message' => $message,
        ], 200);
    }

    #[IsGranted('PUBLIC_ACCESS')]
    #[Route('/reset/', name: 'auth_reset_password', methods: 'PUT')]
    public function reset(
        #[MapQueryString] ResetPasswordTokenDto $dto,
    ): JsonResponse {
        $this->noAuthFacade->checkToken($dto->token());

        return new JsonResponse([
            'status' => 'success',
            'message' => 'Token verified',
        ], 200);
    }

    #[IsGranted('PUBLIC_ACCESS')]
    #[Route('/change-password', name: 'auth_change_password', methods: 'POST')]
    public function changePassword(ChangePasswordDto $dto): JsonResponse
    {
        $this->noAuthFacade->changePasswordWithToken($dto->token, $dto->newPassword1, $dto->newPassword2);

        return new JsonResponse([
            'status' => 'success',
            'message' => 'Password changed',
        ], 200);
    }
}
