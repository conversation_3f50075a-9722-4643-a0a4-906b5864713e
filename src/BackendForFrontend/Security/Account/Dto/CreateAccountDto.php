<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Account\Dto;

use App\Common\RequestDtoInterface;
use App\SharedKernel\Protocol;
use App\SharedKernel\Roles;
use Symfony\Component\Validator\Constraints as Assert;

final class CreateAccountDto implements RequestDtoInterface
{
    #[Assert\NotBlank()]
    #[Assert\Length(max: 50)]
    private string $firstName;

    #[Assert\NotBlank()]
    #[Assert\Length(max: 50)]
    private string $lastName;

    #[Assert\NotBlank()]
    #[Assert\Length(max: 100)]
    #[Assert\Email()]
    private string $email;

    #[Assert\NotBlank()]
    #[Assert\Length(max: 20)]
    #[Assert\Regex(pattern: '/^\+[0-9]{2}[0-9]{9}$/', message: 'Proszę wprowadzić numer telefonu w formacie +XXYYYYYYYYY.')]
    private string $mobilePhoneNumber;

    #[Assert\NotBlank]
    #[Assert\Choice(callback: [Roles::class, 'values'])]
    private string $role;

    /** @var array<string> */
    #[Assert\All(
        constraints: [new Assert\Choice(callback: [Protocol::class, 'values'])]
    )]
    private array $protocols = [];

    /** @var array<string> */
    #[Assert\All([
        new Assert\NotBlank(),
        new Assert\Uuid(),
    ])]
    private array $hospitals = [];

    private function __construct()
    {
    }

    /**
     * @param array<string> $protocols
     * @param array<string> $hospitals
     */
    public static function create(
        string $firstName,
        string $lastName,
        string $email,
        string $mobilePhoneNumber,
        string $role,
        array $protocols = [],
        array $hospitals = [],
    ): self {
        $dto = new self();
        $dto->firstName = $firstName;
        $dto->lastName = $lastName;
        $dto->email = $email;
        $dto->mobilePhoneNumber = $mobilePhoneNumber;
        $dto->role = $role;
        $dto->protocols = $protocols;
        $dto->hospitals = $hospitals;

        return $dto;
    }

    public function firstName(): string
    {
        return $this->firstName;
    }

    public function lastName(): string
    {
        return $this->lastName;
    }

    public function email(): string
    {
        return $this->email;
    }

    public function mobilePhoneNumber(): string
    {
        return $this->mobilePhoneNumber;
    }

    public function role(): string
    {
        return $this->role;
    }

    /** @return array<string> */
    public function protocols(): array
    {
        return $this->protocols;
    }

    /** @return array<string> */
    public function hospitals(): array
    {
        return $this->hospitals;
    }
}
