<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Account\Dto;

use App\Common\RequestDtoInterface;
use Symfony\Component\Validator\Constraints as Assert;

readonly class ChangeAccountEmailDto implements RequestDtoInterface
{
    public function __construct(
        #[Assert\NotBlank()]
        #[Assert\Length(max: 100)]
        #[Assert\Email()]
        public string $email,
    ) {
    }

    public function email(): string
    {
        return $this->email;
    }
}
