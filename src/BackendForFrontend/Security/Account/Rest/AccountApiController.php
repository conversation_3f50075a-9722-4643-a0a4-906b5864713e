<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Account\Rest;

use App\Account\Application\AccountFacade;
use App\Account\Application\Query\AccountView;
use App\Auth\Infrastructure\Security\UserSecurity;
use App\BackendForFrontend\Security\Account\Dto\ChangeAccountEmailDto;
use App\BackendForFrontend\Security\Account\Dto\CreateAccountDto;
use App\BackendForFrontend\Security\Account\Dto\DeactivateAccountDto;
use App\BackendForFrontend\Security\Account\Dto\UpdateAccountDto;
use App\Common\Query\GridConfiguration;
use App\Common\Query\GridField;
use App\Common\Query\GridFilterMethod;
use App\Common\Uuid;
use App\Hospital\Application\HospitalFacade;
use App\SharedKernel\Protocol;
use App\SharedKernel\Roles;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\Requirement\Requirement;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/account', name: 'account_')]
class AccountApiController extends AbstractController
{
    public function __construct(
        private readonly AccountFacade $accountFacade,
        private readonly HospitalFacade $hospitalFacade,
    ) {
    }

    #[Route('/accounts', name: 'account_create', methods: 'POST')]
    #[IsGranted('ROLE_CENTRAL_ADMINISTRATOR')]
    public function create(CreateAccountDto $dto): JsonResponse
    {
        $accountId = Uuid::generate();

        $this->accountFacade->create(
            $accountId->valueString(),
            $dto->firstName(),
            $dto->lastName(),
            $dto->email(),
            $dto->mobilePhoneNumber(),
            $dto->role(),
            $dto->protocols(),
            $dto->hospitals(),
        );

        return new JsonResponse(['accountId' => $accountId->valueString()], Response::HTTP_CREATED);
    }

    #[Route('/accounts/{accountId}', name: 'change_account', requirements: ['accountId' => Requirement::UUID_V4], methods: 'PUT')]
    #[IsGranted('ROLE_CENTRAL_ADMINISTRATOR')]
    public function change(string $accountId, UpdateAccountDto $dto): JsonResponse
    {
        $this->accountFacade->update(
            $accountId,
            $dto->firstName(),
            $dto->lastName(),
            $dto->mobilePhoneNumber(),
            $dto->protocols(),
            $dto->hospitals(),
        );

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }

    #[Route('/accounts/{accountId}/change-email', name: 'change_account_email', requirements: ['accountId' => Requirement::UUID_V4], methods: 'PATCH')]
    #[IsGranted('ROLE_CENTRAL_ADMINISTRATOR')]
    public function changeEmail(string $accountId, ChangeAccountEmailDto $dto): JsonResponse
    {
        $this->accountFacade->changeEmail($accountId, $dto->email());

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }

    #[Route('/accounts/{accountId}/activate', name: 'activate_account', requirements: ['accountId' => Requirement::UUID_V4], methods: 'PATCH')]
    #[IsGranted('ROLE_CENTRAL_ADMINISTRATOR')]
    public function activate(string $accountId): JsonResponse
    {
        $this->accountFacade->activate($accountId);

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }

    #[Route('/accounts/{accountId}/deactivate', name: 'deactivate_account', requirements: ['accountId' => Requirement::UUID_V4], methods: 'PATCH')]
    #[IsGranted('ROLE_CENTRAL_ADMINISTRATOR')]
    public function deactivate(string $accountId, DeactivateAccountDto $dto): JsonResponse
    {
        $this->accountFacade->deactivate($accountId, $dto->reason);

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }

    #[Route('/accounts/{accountId}', name: 'find_account_by_id', requirements: ['accountId' => Requirement::UUID_V4], methods: 'GET')]
    #[IsGranted('ROLE_CENTRAL_ADMINISTRATOR')]
    public function findByAccountId(string $accountId): JsonResponse
    {
        $account = $this->accountFacade->findByAccountId($accountId);

        return new JsonResponse($account, $account === null ? Response::HTTP_NOT_FOUND : Response::HTTP_OK);
    }

    #[Route('/accounts/me', name: 'get_account_by_logged_in_user', methods: 'GET')]
    public function getByLoggedInUser(): JsonResponse
    {
        /** @var ?UserSecurity $user */
        $user = $this->getUser();

        if (!$user) {
            return new JsonResponse(null, Response::HTTP_UNAUTHORIZED);
        }

        $account = $this->accountFacade->findByAccountId($user->getRootId());

        if (!$account) {
            return new JsonResponse(null, Response::HTTP_UNAUTHORIZED);
        }

        $account->setLoginAt($user->authUser()->loginAt());
        $this->addAllActiveHospitalsForSpecifiedRoles($user, $account);
        $this->addAllProtocolsForSpecifiedRoles($user, $account);

        return new JsonResponse($account);
    }

    #[Route(path: '/accounts', name: 'get_accounts_for_grid', methods: ['GET'])]
    #[IsGranted('ROLE_CENTRAL_ADMINISTRATOR')]
    public function getForGrid(Request $request): JsonResponse
    {
        $gridFields = new ArrayCollection();
        $gridFields->add(GridField::create('accountId', true, true));
        $gridFields->add(GridField::create('firstName', true, true));
        $gridFields->add(GridField::create('lastName', true, true));
        $gridFields->add(GridField::create('email', true, true));
        $gridFields->add(GridField::create('mobilePhoneNumber', true, true));
        $gridFields->add(GridField::create('role', true, true));
        $gridFields->add(GridField::create('isActive', true, true, GridFilterMethod::BOOL));
        $gridFields->add(GridField::create('createdAt', true, true));
        $gridFields->add(GridField::create('updatedAt', true, true));

        $gridConfiguration = GridConfiguration::createFromRequest($request, $gridFields);
        $gridResult = $this->accountFacade->findForGrid($gridConfiguration);

        return new JsonResponse($gridResult);
    }

    private function addAllActiveHospitalsForSpecifiedRoles(UserSecurity $user, AccountView $account): void
    {
        $roleExists = match (true) {
            in_array(Roles::ROLE_CENTRAL_ADMINISTRATOR->value, $user->getRoles(), true),
            in_array(Roles::ROLE_COORDINATOR->value, $user->getRoles(), true) => true,
            default => false,
        };

        if ($roleExists) {
            $activeHospitals = $this->hospitalFacade->findAllActiveHospitalSelect();
            $account->setHospitals(array_map(static fn ($hospital) => $hospital->hospitalId(), $activeHospitals->getValues()));
        }
    }

    private function addAllProtocolsForSpecifiedRoles(UserSecurity $user, AccountView $account): void
    {
        $roleExists = match (true) {
            in_array(Roles::ROLE_CENTRAL_ADMINISTRATOR->value, $user->getRoles(), true),
            in_array(Roles::ROLE_DATA_ADMINISTRATOR->value, $user->getRoles(), true) => true,
            default => false,
        };

        if ($roleExists) {
            $protocols = Protocol::values();
            $account->setProtocols($protocols);
        }
    }
}
