<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Account\Console;

use App\Account\Application\AccountFacade;
use App\Common\Attribute\Transactional;
use App\Common\Uuid;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\ChoiceQuestion;
use Symfony\Component\Console\Question\ConfirmationQuestion;
use Symfony\Component\Console\Question\Question;
use Symfony\Component\Validator\Constraints\Email;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Regex;
use Symfony\Component\Validator\Validation;

#[Transactional]
#[AsCommand(
    name: 'app:account:create',
)]
class CreateAccountConsoleCommand extends Command
{
    public function __construct(private readonly AccountFacade $accountFacade)
    {
        parent::__construct();
    }

    protected function configure(): void
    {
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('======================================');
        $helper = $this->getHelper('question');

        $questionFirstName = new Question('Podaj imię: ', false);
        $questionFirstName->setValidator(Validation::createCallable(
            new NotBlank(message: 'Imię nie może być puste!'),
            new Length(max: 50, maxMessage: 'Imię może zawierać maksymalnie 50 znaków!'),
        ));
        $questionFirstName->setMaxAttempts(2);

        $questionLastName = new Question('Podaj nazwisko: ', false);
        $questionLastName->setValidator(Validation::createCallable(
            new NotBlank(message: 'Nazwisko nie może być puste!'),
            new Length(max: 50, maxMessage: 'Nazwisko może zawierać maksymalnie 50 znaków!'),
        ));
        $questionLastName->setMaxAttempts(2);

        $questionEmail = new Question('Podaj adres email: ', false);
        $questionEmail->setValidator(Validation::createCallable(
            new NotBlank(message: 'Adres email nie może być pusty!'),
            new Email(message: 'Nie poprawny adres email!')
        ));
        $questionEmail->setMaxAttempts(2);

        $questionMobilePhoneNumber = new Question('Podaj numer telefonu: ', false);
        $questionMobilePhoneNumber->setValidator(Validation::createCallable(
            new NotBlank(message: 'Numer telefonu nie może być pusty!'),
            new Length(max: 20, maxMessage: 'Numer telefonu może zawierać maksymalnie 20 znaków!'),
            new Regex(pattern: '/^\+[0-9]{2}[0-9]{9}$/', message: 'Nie poprawny format numeru! Przykład: +48123456789'),
        ));
        $questionMobilePhoneNumber->setMaxAttempts(2);

        $questionRole = new ChoiceQuestion(
            'Wybierz role:',
            [
                'ROLE_CENTRAL_ADMINISTRATOR',
                'ROLE_REPORTER_PHYSICIAN',
                'ROLE_COORDINATOR',
                'ROLE_DATA_ADMINISTRATOR',
            ],
            null
        );

        $firstName = $helper->ask($input, $output, $questionFirstName);
        $lastName = $helper->ask($input, $output, $questionLastName);
        $email = $helper->ask($input, $output, $questionEmail);
        $mobilePhoneNumber = $helper->ask($input, $output, $questionMobilePhoneNumber);
        $role = $helper->ask($input, $output, $questionRole);

        $output->writeln('======================================');

        $output->writeln('<info>Podsumowanie:</info>');
        $output->writeln("Imię: <info>$firstName</info>");
        $output->writeln("Nazwisko: <info>$lastName</info>");
        $output->writeln("Email: <info>$email</info>");
        $output->writeln("Numer telefonu: <info>$mobilePhoneNumber</info>");
        $output->writeln("Rola: <info>$role</info>");

        $output->writeln('======================================');

        $question = new ConfirmationQuestion("Czy dodać użytkownika? \n<info>[t] Tak</info>\n<comment>[n] Nie</comment>\n wybrano: ", false, '/^t/i');
        $answer = $helper->ask($input, $output, $question);

        if ($answer) {
            $output->writeln('T');

            $this->accountFacade->create(
                Uuid::generate()->valueString(),
                $firstName,
                $lastName,
                $email,
                $mobilePhoneNumber,
                $role
            );

            return Command::SUCCESS;
        }

        $output->writeln('Operacja przerwana.');

        return Command::FAILURE;
    }
}
