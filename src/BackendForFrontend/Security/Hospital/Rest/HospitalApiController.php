<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Hospital\Rest;

use App\BackendForFrontend\Security\Hospital\Dto\CreateHospitalDto;
use App\BackendForFrontend\Security\Hospital\Dto\DeactivateHospitalDto;
use App\BackendForFrontend\Security\Hospital\Dto\GetActiveHospitalsForSelectDto;
use App\BackendForFrontend\Security\Hospital\Dto\UpdateHospitalDto;
use App\Common\Attribute\DoNotLogRequest;
use App\Common\Query\GridConfiguration;
use App\Common\Query\GridField;
use App\Common\Query\GridFilterMethod;
use App\Common\Uuid;
use App\Hospital\Application\HospitalFacade;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\Requirement\Requirement;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[AsController]
#[Route(path: '/hospital', name: 'hospital_')]
class HospitalApiController extends AbstractController
{
    public function __construct(private readonly HospitalFacade $hospitalFacade)
    {
    }

    #[Route(path: '/hospitals', name: 'hospital_create', methods: ['POST'])]
    #[IsGranted('ROLE_CENTRAL_ADMINISTRATOR')]
    public function create(CreateHospitalDto $dto): JsonResponse
    {
        $hospitalId = Uuid::generate()->valueString();
        $this->hospitalFacade->create(
            $hospitalId,
            $dto->shortName,
            $dto->fullName,
            $dto->streetWithNumber,
            $dto->postCode,
            $dto->city
        );

        return new JsonResponse(['hospitalId' => $hospitalId], Response::HTTP_CREATED);
    }

    #[Route(path: '/hospitals/{hospitalId}', name: 'hospital_update', requirements: ['hospitalId' => Requirement::UUID_V4], methods: ['PUT'])]
    #[IsGranted('ROLE_CENTRAL_ADMINISTRATOR')]
    public function update(string $hospitalId, UpdateHospitalDto $dto): JsonResponse
    {
        $this->hospitalFacade->update(
            $hospitalId,
            $dto->shortName,
            $dto->fullName,
            $dto->streetWithNumber,
            $dto->postCode,
            $dto->city
        );

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }

    #[Route(path: '/hospitals/{hospitalId}/activate', name: 'hospital_activate', requirements: ['hospitalId' => Requirement::UUID_V4], methods: ['PATCH'])]
    #[IsGranted('ROLE_CENTRAL_ADMINISTRATOR')]
    public function activate(string $hospitalId): JsonResponse
    {
        $this->hospitalFacade->activate($hospitalId);

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }

    #[Route(path: '/hospitals/{hospitalId}/deactivate', name: 'hospital_deactivate', requirements: ['hospitalId' => Requirement::UUID_V4], methods: ['PATCH'])]
    #[IsGranted('ROLE_CENTRAL_ADMINISTRATOR')]
    public function deactivate(string $hospitalId, DeactivateHospitalDto $dto): JsonResponse
    {
        $this->hospitalFacade->deactivate($hospitalId, $dto->reason);

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }

    #[Route(path: '/hospitals/{hospitalId}', name: 'hospital_get', requirements: ['hospitalId' => Requirement::UUID_V4], methods: ['GET'])]
    #[IsGranted('ROLE_CENTRAL_ADMINISTRATOR')]
    public function get(string $hospitalId): JsonResponse
    {
        $hospital = $this->hospitalFacade->findByHospitalId($hospitalId);

        return new JsonResponse($hospital, $hospital === null ? Response::HTTP_NOT_FOUND : Response::HTTP_OK);
    }

    #[DoNotLogRequest]
    #[Route(path: '/hospitals/for-select', name: 'get_all_active_hospitals_for_select', methods: ['POST'])]
    public function getAllActiveForSelect(GetActiveHospitalsForSelectDto $dto): JsonResponse
    {
        if ($dto->hospitals !== null) {
            $hospitals = $this->hospitalFacade->findAllActiveHospitalSelect($dto->hospitals)->getValues();
        } else {
            $hospitals = $this->hospitalFacade->findAllActiveHospitalSelect()->getValues();
        }

        return new JsonResponse($hospitals);
    }

    #[Route(path: '/hospitals', name: 'get_hospitals_for_grid', methods: ['GET'])]
    #[IsGranted('ROLE_CENTRAL_ADMINISTRATOR')]
    public function getForGrid(Request $request): JsonResponse
    {
        $gridFields = new ArrayCollection();
        $gridFields->add(GridField::create('hospitalId', true, true));
        $gridFields->add(GridField::create('shortName', true, true));
        $gridFields->add(GridField::create('fullName', true, true, GridFilterMethod::CONTAINS));
        $gridFields->add(GridField::create('streetWithNumber', true, true, GridFilterMethod::CONTAINS));
        $gridFields->add(GridField::create('postCode', true, true));
        $gridFields->add(GridField::create('city', true, true, GridFilterMethod::CONTAINS));
        $gridFields->add(GridField::create('isActive', true, true, GridFilterMethod::BOOL));
        $gridFields->add(GridField::create('createdAt', true, true));
        $gridFields->add(GridField::create('updatedAt', true, true));

        $gridConfiguration = GridConfiguration::createFromRequest($request, $gridFields);
        $gridResult = $this->hospitalFacade->findForGrid($gridConfiguration);

        return new JsonResponse($gridResult);
    }
}
