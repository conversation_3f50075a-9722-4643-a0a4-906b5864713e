<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Hospital\Dto;

use App\Common\RequestDtoInterface;
use Symfony\Component\Validator\Constraints as Assert;

final class CreateHospitalDto implements RequestDtoInterface
{
    #[Assert\NotBlank()]
    #[Assert\Length(max: 20)]
    public string $shortName;

    #[Assert\NotBlank()]
    #[Assert\Length(min: 10, max: 255)]
    public string $fullName;

    #[Assert\NotBlank()]
    #[Assert\Length(max: 100)]
    public string $streetWithNumber;

    #[Assert\NotBlank()]
    #[Assert\Length(max: 6)]
    #[Assert\Regex(pattern: "/^\d{2}-\d{3}$/", message: 'Proszę wprowadzić kod pocztowy w formacie XX-XXX.')]
    public string $postCode;

    #[Assert\NotBlank()]
    #[Assert\Length(max: 40)]
    public string $city;

    private function __construct()
    {
    }

    public static function create(string $shortName, string $fullName, string $streetWithNumber, string $postCode, string $city): self
    {
        $dto = new self();
        $dto->shortName = $shortName;
        $dto->fullName = $fullName;
        $dto->streetWithNumber = $streetWithNumber;
        $dto->postCode = $postCode;
        $dto->city = $city;

        return $dto;
    }
}
