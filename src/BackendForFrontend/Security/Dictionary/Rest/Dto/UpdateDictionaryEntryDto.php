<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Dictionary\Rest\Dto;

use App\Common\RequestDtoInterface;
use Symfony\Component\Validator\Constraints as Assert;

final class UpdateDictionaryEntryDto implements RequestDtoInterface
{
    #[Assert\NotBlank()]
    #[Assert\Length(max: 500)]
    private string $description;

    #[Assert\NotBlank()]
    #[Assert\Length(max: 500)]
    private string $searchableDescription;

    private function __construct()
    {
    }

    public static function create(
        string $description,
        string $searchableDescription,
    ): self {
        $dto = new self();
        $dto->description = $description;
        $dto->searchableDescription = $searchableDescription;

        return $dto;
    }

    public function description(): string
    {
        return $this->description;
    }

    public function searchableDescription(): string
    {
        return $this->searchableDescription;
    }
}
