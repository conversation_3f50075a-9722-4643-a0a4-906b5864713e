<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Dictionary\Rest\Dto;

use App\Common\RequestDtoInterface;
use Symfony\Component\Validator\Constraints as Assert;

final class CreateDictionaryEntryDto implements RequestDtoInterface
{
    #[Assert\NotBlank()]
    #[Assert\Length(max: 50)]
    #[Assert\Regex('/^[A-Z0-9-_.]+$/', message: 'Wartość może zawierać tylko duże litery, cyfry i znaki (-_.).')]
    private string $value;

    #[Assert\NotBlank()]
    #[Assert\Length(max: 500)]
    private string $description;

    #[Assert\NotBlank()]
    #[Assert\Length(max: 500)]
    private string $searchableDescription;

    private function __construct()
    {
    }

    public static function create(
        string $value,
        string $description,
        string $searchableDescription,
    ): self {
        $dto = new self();
        $dto->value = $value;
        $dto->description = $description;
        $dto->searchableDescription = $searchableDescription;

        return $dto;
    }

    public function value(): string
    {
        return $this->value;
    }

    public function description(): string
    {
        return $this->description;
    }

    public function searchableDescription(): string
    {
        return $this->searchableDescription;
    }
}
