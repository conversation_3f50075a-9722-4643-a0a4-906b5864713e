<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Dictionary\Rest;

use App\BackendForFrontend\Security\Dictionary\Rest\Dto\CreateDictionaryEntryDto;
use App\BackendForFrontend\Security\Dictionary\Rest\Dto\UpdateDictionaryEntryDto;
use App\Common\Query\GridConfiguration;
use App\Common\Query\GridField;
use App\Common\Query\GridFilterMethod;
use App\Dictionary\DictionaryFacade;
use App\SharedKernel\DictionaryType;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[AsController]
#[Route(path: '/dictionary', name: 'dictionary_')]
class DictionaryApiController extends AbstractController
{
    public function __construct(
        private readonly DictionaryFacade $dictionaryFacade,
    ) {
    }

    #[IsGranted('ROLE_CENTRAL_ADMINISTRATOR')]
    #[Route(path: '/dictionaries/{dictionaryType}/entries', name: 'create_dictionary', methods: ['POST'])]
    public function create(string $dictionaryType, CreateDictionaryEntryDto $dto): JsonResponse
    {
        $this->dictionaryFacade->addDictionaryEntry(
            $dto->value(),
            $dto->description(),
            $dto->searchableDescription(),
            DictionaryType::from($dictionaryType)
        );

        return new JsonResponse(null, Response::HTTP_CREATED);
    }

    #[IsGranted('ROLE_CENTRAL_ADMINISTRATOR')]
    #[Route(path: '/dictionaries/{dictionaryType}/entries/{dictionaryEntryId}', name: 'change_dictionary', methods: ['PUT'])]
    public function change(int $dictionaryEntryId, UpdateDictionaryEntryDto $dto): JsonResponse
    {
        $this->dictionaryFacade->editDictionaryEntry(
            $dictionaryEntryId,
            $dto->description(),
            $dto->searchableDescription()
        );

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }

    #[IsGranted('ROLE_CENTRAL_ADMINISTRATOR')]
    #[Route(path: '/dictionaries/dictionaryTypes', name: 'get_dictionary_types', methods: ['GET'])]
    public function getDictionaryTypes(): JsonResponse
    {
        return new JsonResponse(DictionaryType::values());
    }

    #[IsGranted('ROLE_CENTRAL_ADMINISTRATOR')]
    #[Route(path: '/dictionaries/entries', name: 'find_for_grid', methods: ['GET'])]
    public function findForGrid(Request $request): JsonResponse
    {
        $gridFields = new ArrayCollection();
        $gridFields->add(GridField::create('value', true, true, GridFilterMethod::EQUALS));
        $gridFields->add(GridField::create('description', true, true, GridFilterMethod::CONTAINS));
        $gridFields->add(GridField::create('searchableDescription', true, true, GridFilterMethod::CONTAINS));
        $gridFields->add(GridField::create('dictionaryType', true, true, GridFilterMethod::EQUALS));

        $gridConfiguration = GridConfiguration::createFromRequest($request, $gridFields);
        $gridResult = $this->dictionaryFacade->findForGrid($gridConfiguration);

        return new JsonResponse($gridResult);
    }

    #[Route(path: '/dictionaries/{dictionaryType}/entries', name: 'find_dictionary_for_select', methods: ['GET'])]
    public function findDictionaryEntriesForSelect(string $dictionaryType, Request $request): JsonResponse
    {
        $gridFields = new ArrayCollection();
        $gridFields->add(GridField::create('value', true, true, GridFilterMethod::EQUALS));
        $gridFields->add(GridField::create('description', true, true, GridFilterMethod::CONTAINS));
        $gridFields->add(GridField::create('searchableDescription', true, true, GridFilterMethod::CONTAINS));

        $gridConfiguration = GridConfiguration::createFromRequest($request, $gridFields);
        $gridResult = $this->dictionaryFacade->findDictionaryEntriesForSelect(
            $gridConfiguration,
            $dictionaryType
        );

        return new JsonResponse($gridResult);
    }
}
