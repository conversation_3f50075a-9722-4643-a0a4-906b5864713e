<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Form\Voter;

use App\Auth\Infrastructure\Security\UserSecurity;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;

class FormVoter extends Voter
{
    public const GET_PATIENT_FORMS = 'get_patient_forms';
    public const GET_FORM_SCHEMA_AND_DATA = 'get_form_schema_and_data';
    public const SAVE_FORM_DATA = 'save_form_data';

    protected function supports(string $attribute, mixed $subject): bool
    {
        if (!$subject instanceof FormSubject) {
            return false;
        }

        return $attribute === self::GET_PATIENT_FORMS || $attribute === self::GET_FORM_SCHEMA_AND_DATA
            || $attribute === self::SAVE_FORM_DATA;
    }

    /** @param FormSubject $subject */
    protected function voteOnAttribute(string $attribute, $subject, TokenInterface $token): bool
    {
        $user = $token->getUser();

        if (!$user instanceof UserSecurity) {
            return false;
        }

        $hospitals = $user->getHospitals();
        $protocols = $user->getProtocols();

        if (in_array('ROLE_CENTRAL_ADMINISTRATOR', $user->getRoles(), true)) {
            return true;
        }

        if (in_array('ROLE_REPORTER_PHYSICIAN', $user->getRoles(), true)) {
            return !empty(array_intersect($subject->hospitals(), $hospitals))
                && !empty(array_intersect($subject->protocols(), $protocols));
        }

        if (in_array('ROLE_COORDINATOR', $user->getRoles(), true)) {
            return !empty(array_intersect($subject->protocols(), $protocols));
        }

        if (in_array('ROLE_DATA_ADMINISTRATOR', $user->getRoles(), true)) {
            return !empty(array_intersect($subject->hospitals(), $hospitals));
        }

        return false;
    }
}
