<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Form\Voter;

class FormSubject
{
    /** @var array<string> */
    private array $hospitals;

    /** @var array<string> */
    private array $protocols;

    /**
     * @param array<string> $hospitals
     * @param array<string> $protocols
     */
    private function __construct(array $hospitals, array $protocols)
    {
        $this->hospitals = $hospitals;
        $this->protocols = $protocols;
    }

    /**
     * @param array<string> $hospitals
     * @param array<string> $protocols
     */
    public static function createFromArray(array $hospitals, array $protocols): self
    {
        return new self($hospitals, $protocols);
    }

    public static function createFromString(string $hospital, string $protocol): self
    {
        return new self([$hospital], [$protocol]);
    }

    /** @param array<string> $protocols */
    public static function createOnlyProtocols(array $protocols): self
    {
        return new self([], $protocols);
    }

    /** @param array<string> $hospitals */
    public static function createOnlyHospitals(array $hospitals): self
    {
        return new self($hospitals, []);
    }

    /** @return array<string> */
    public function hospitals(): array
    {
        return $this->hospitals;
    }

    /** @return array<string> */
    public function protocols(): array
    {
        return $this->protocols;
    }
}
