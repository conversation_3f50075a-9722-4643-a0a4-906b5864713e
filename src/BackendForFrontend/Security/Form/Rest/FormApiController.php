<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Form\Rest;

use App\BackendForFrontend\Security\Form\Voter\FormSubject;
use App\BackendForFrontend\Security\Form\Voter\FormVoter;
use App\Form\FormFacade;
use App\Patient\Application\PatientFacade;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\Requirement\Requirement;

#[AsController]
#[Route(path: '/form', name: 'form_')]
class FormApiController extends AbstractController
{
    private const string NO_ACCESS_GET_FORMS = 'Nie możesz pobrać formularza tego pacjenta. Nie znajduje się on w zakresie Twoich uprawnień. Sprawdź swoje uprawnienia lub skontaktuj się z administratorem, aby uzyskać więcej informacji.';
    private const string NO_ACCESS_SAVE_DATA_FORMS = 'Nie możesz zapisać formularza tego pacjenta. Nie znajduje się on w zakresie Twoich uprawnień. Sprawdź swoje uprawnienia lub skontaktuj się z administratorem, aby uzyskać więcej informacji.';

    public function __construct(
        private readonly FormFacade $formFacade,
        private readonly PatientFacade $patientFacade,
    ) {
    }

    #[Route(path: '/patient-forms/{patientId}', name: 'forms_get_patient_forms', requirements: ['patientId' => Requirement::UUID_V4], methods: ['GET'])]
    public function getPatientForms(string $patientId): JsonResponse
    {
        $patientSubject = $this->getPatientSubjectToVoterFromPatientView($patientId);
        $this->denyAccessUnlessGranted(
            FormVoter::GET_PATIENT_FORMS,
            $patientSubject,
            self::NO_ACCESS_GET_FORMS
        );

        $patientForms = $this->formFacade->getPatientForms($patientId);

        return new JsonResponse($patientForms->toArray(), Response::HTTP_OK);
    }

    #[Route(path: '/forms/{formId}/protocol-form-data/{patientProtocolId}', name: 'forms_get_form_schema_and_data', requirements: ['patientProtocolId' => Requirement::UUID_V4], methods: ['GET'])]
    public function getFormSchemaAndData(string $patientProtocolId, string $formId): JsonResponse
    {
        $patient = $this->formFacade->getPatientProtocolByPatientProtocolId($patientProtocolId);

        $patientSubject = $this->getPatientSubjectToVoterFromPatientView($patient->patientId());
        $this->denyAccessUnlessGranted(
            FormVoter::GET_FORM_SCHEMA_AND_DATA,
            $patientSubject,
            self::NO_ACCESS_GET_FORMS
        );

        $schemaAndData = $this->formFacade->getFormSchemaAndData($patientProtocolId, $formId);

        return new JsonResponse($schemaAndData, Response::HTTP_OK);
    }

    #[Route(path: '/forms/{formId}/protocol-form-data/{patientProtocolId}', name: 'forms_save_form_data', requirements: ['patientProtocolId' => Requirement::UUID_V4], methods: ['POST'])]
    public function saveFormData(string $patientProtocolId, string $formId, Request $request): JsonResponse
    {
        $patient = $this->formFacade->getPatientProtocolByPatientProtocolId($patientProtocolId);

        $patientSubject = $this->getPatientSubjectToVoterFromPatientView($patient->patientId());
        $this->denyAccessUnlessGranted(
            FormVoter::SAVE_FORM_DATA,
            $patientSubject,
            self::NO_ACCESS_SAVE_DATA_FORMS
        );

        $this->formFacade->saveFormData(
            $patientProtocolId,
            $formId,
            $request->getContent()
        );

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }

    private function getPatientSubjectToVoterFromPatientView(string $patientId): FormSubject
    {
        $patientView = $this->patientFacade->findByPatientId($patientId);

        if ($patientView === null) {
            throw $this->createNotFoundException(sprintf('Nie znaleziono pacjenta o identyfikatorze %s.', $patientId));
        }

        return FormSubject::createFromArray($patientView->hospitals(), $patientView->protocolsValueList());
    }
}
