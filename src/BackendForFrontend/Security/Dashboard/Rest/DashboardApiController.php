<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Dashboard\Rest;

use App\Account\Application\Query\AccountQueryInterface;
use App\Auth\Infrastructure\Security\UserSecurity;
use App\Common\Query\GridConfiguration;
use App\Common\Query\GridField;
use App\Common\Query\GridFilterMethod;
use App\Common\Query\GridResult;
use App\Logger\LoggerStatsFacade;
use App\Patient\Application\FavoritePatientFacade;
use App\Patient\Application\PatientCorrectionFlagFacade;
use App\Patient\Application\PatientFacade;
use App\Patient\Application\PatientStatsFacade;
use App\Patient\Application\Query\PatientView;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\Routing\Attribute\Route;

#[AsController]
#[Route(path: '/dashboard', name: 'dashboard_')]
final class DashboardApiController extends AbstractController
{
    public function __construct(
        private readonly PatientStatsFacade $patientStatsFacade,
        private readonly LoggerStatsFacade $loggerStatsFacade,
        private readonly PatientFacade $patientFacade,
        private readonly FavoritePatientFacade $favoritePatientFacade,
        private readonly PatientCorrectionFlagFacade $correctionFlagFacade,
        private readonly AccountQueryInterface $accountQuery,
    ) {
    }

    #[Route(path: '/stats', methods: ['GET'])]
    public function getStats(Request $request): JsonResponse
    {
        /** @var UserSecurity $user */
        $user = $this->getUser();

        $year = $request->query->get('year');
        $year = $year ? (int) $year : null;

        $stats = [
            'uniqueTreatmentStartYears' => $this->patientStatsFacade->getUniqueTreatmentStartYears()->getValues(),
            'countOfPatientsByHospitals' => $this->patientStatsFacade->countOfPatientsByHospitals($year)->getValues(),
            'countOfPatientsByGender' => $this->patientStatsFacade->countOfPatientsByGender($year)->getValues(),
            'countOfPatientsByProtocols' => $this->patientStatsFacade->countOfPatientsByProtocols($year)->getValues(),
            'countOfProtocolsByProtocolType' => $this->patientStatsFacade->countAllProtocolsByProtocolType($year)->getValues(),
            'countOfVisiblePatientsForAccount' => $this->patientStatsFacade->countOfVisiblePatientsForAccount(
                $user->getHospitals(),
                $user->getProtocols()
            ),
            'countOfPatientsAddedInLast30DaysByAccount' => $this->loggerStatsFacade->countOfPatientsAddedInLast30DaysByAccount($user->getRootId()),
            'countOfUpdatedFormsInLast30DaysByAccount' => $this->loggerStatsFacade->countOfUpdatedFormsInLast30DaysByAccount($user->getRootId()),
            'lastPatientsWithFormsUpdatedByAccount' => $this->lastPatientsWithFormsUpdatedByAccount($user)?->data()->getValues(),
        ];

        return new JsonResponse($stats);
    }

    #[Route(path: '/favorite-patients', name: 'favorite_patients_get_for_grid', methods: ['GET'])]
    public function getFavoritePatientsForGrid(Request $request): JsonResponse
    {
        /** @var UserSecurity $user */
        $user = $this->getUser();

        $gridConfiguration = GridConfiguration::createFromRequest($request, new ArrayCollection());
        $gridResult = $this->favoritePatientFacade->findForGrid(
            $gridConfiguration,
            $user->authUser()->aggregateRootId(),
            $user->getHospitals(),
            $user->getProtocols());

        return new JsonResponse($gridResult, Response::HTTP_OK);
    }

    #[Route(path: '/correction-flags', name: 'correction_flags_get_for_grid', methods: ['GET'])]
    public function getCorrectionFlagsForGrid(Request $request): JsonResponse
    {
        /** @var UserSecurity $user */
        $user = $this->getUser();
        $authUser = $user->authUser();

        $accountView = $this->accountQuery->findByAccountId($authUser->aggregateRootId());
        if ($accountView === null) {
            throw $this->createAccessDeniedException('User account details not found.');
        }

        $gridConfiguration = GridConfiguration::createFromRequest($request, new ArrayCollection());

        $onlyAllowHospitals = $accountView->hospitals();
        $onlyAllowProtocols = $accountView->protocols();

        $gridResult = $this->correctionFlagFacade->findForGrid(
            gridConfiguration: $gridConfiguration,
            onlyAllowHospitals: $onlyAllowHospitals,
            onlyAllowProtocols: $onlyAllowProtocols
        );

        return new JsonResponse($gridResult);
    }

    /** @return GridResult<string, PatientView>|null */
    private function lastPatientsWithFormsUpdatedByAccount(UserSecurity $user): ?GridResult
    {
        $lastPatients = $this->loggerStatsFacade->listLastPatientIdsOfUpdatedFormsByAccount($user->getRootId());

        if (0 === count($lastPatients)) {
            return null;
        }

        $gridFields = new ArrayCollection();
        $gridFields->add(GridField::create('patientId', true, false, GridFilterMethod::IN));

        $gridConfiguration = GridConfiguration::create($gridFields, 1, 10, [], ['patientId' => $lastPatients]);

        return $this->patientFacade->findForGrid($gridConfiguration,
            $user->getHospitals(),
            $user->getProtocols()
        );
    }
}
