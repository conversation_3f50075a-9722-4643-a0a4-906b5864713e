<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Logger;

use App\Common\Query\GridConfiguration;
use App\Common\Query\GridField;
use App\Common\Query\GridFilterMethod;
use App\Logger\LoggerFacade;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[AsController]
#[Route(path: '/logger', name: 'logger_')]
class LoggerApiController extends AbstractController
{
    public function __construct(
        private readonly LoggerFacade $loggerFacade)
    {
    }

    #[Route(path: '/logs', name: 'get_log_for_grid', methods: ['GET'])]
    #[IsGranted('ROLE_CENTRAL_ADMINISTRATOR')]
    public function getForGrid(Request $request): JsonResponse
    {
        $gridFields = new ArrayCollection();
        $gridFields->add(GridField::create('action', true, true, GridFilterMethod::CONTAINS));
        $gridFields->add(GridField::create('path', true, true, GridFilterMethod::CONTAINS));
        $gridFields->add(GridField::create('objectId', true, true, GridFilterMethod::EQUALS));
        $gridFields->add(GridField::create('objectDetails', true, true, GridFilterMethod::CONTAINS));
        $gridFields->add(GridField::create('accountFirstName', true, true, GridFilterMethod::CONTAINS));
        $gridFields->add(GridField::create('accountLastName', true, true, GridFilterMethod::CONTAINS));
        $gridFields->add(GridField::create('createdAt', true, true, GridFilterMethod::CONTAINS));

        $gridConfiguration = GridConfiguration::createFromRequest($request, $gridFields);
        $gridResult = $this->loggerFacade->findForGrid($gridConfiguration);

        return new JsonResponse($gridResult);
    }
}
