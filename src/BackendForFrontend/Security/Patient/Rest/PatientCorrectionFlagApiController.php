<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Patient\Rest;

use App\Auth\Infrastructure\Security\UserSecurity;
use App\BackendForFrontend\Security\Patient\Dto\CreatePatientCorrectionFlagDto;
use App\BackendForFrontend\Security\Patient\Voter\PatientSubject;
use App\BackendForFrontend\Security\Patient\Voter\PatientVoter;
use App\Patient\Application\PatientCorrectionFlagFacade;
use App\Patient\Application\PatientFacade;
use App\Patient\Application\Query\PatientProtocolView;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\HttpKernel\Attribute\MapRequestPayload;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\Requirement\Requirement;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[AsController]
#[Route(path: '/patient', name: 'patient_correction_flag_')]
class PatientCorrectionFlagApiController extends AbstractController
{
    public function __construct(
        private readonly PatientCorrectionFlagFacade $facade,
        private readonly PatientFacade $patientFacade,
    ) {
    }

    #[Route(path: '/patients/{patientId}/correction-flags', name: 'create', requirements: ['patientId' => Requirement::UUID_V4], methods: ['POST'])]
    #[IsGranted('ROLE_CENTRAL_ADMINISTRATOR')]
    public function createCorrectionFlag(
        string $patientId,
        #[MapRequestPayload] CreatePatientCorrectionFlagDto $dto,
    ): JsonResponse {
        $flagId = $this->facade->createFlag(
            patientId: $patientId,
            hospitalId: $dto->hospitalId,
            comment: $dto->comment
        );

        return new JsonResponse(['flagId' => $flagId], Response::HTTP_CREATED);
    }

    #[Route(path: '/patients/{patientId}/correction-flags/{flagId}/resolve', name: 'resolve', requirements: ['flagId' => Requirement::UUID_V4, 'patientId' => Requirement::UUID_V4], methods: ['PATCH'])]
    public function resolveCorrectionFlag(string $patientId, string $flagId): JsonResponse
    {
        /** @var UserSecurity $user */
        $user = $this->getUser();
        $resolverUserId = $user->authUser()->aggregateRootId();

        $flagView = $this->facade->findById($flagId);
        if ($flagView === null) {
            throw $this->createNotFoundException('Flaga korekty nie została znaleziona.');
        }

        if ($flagView->patientId !== $patientId) {
            throw $this->createNotFoundException(sprintf('Flaga korekty o ID "%s" nie jest powiązana z pacjentem o ID "%s".', $flagId, $patientId));
        }

        $patientSubject = $this->getPatientSubjectForVoter($flagView->patientId);
        $this->denyAccessUnlessGranted(PatientVoter::RESOLVE_CORRECTION_FLAG, $patientSubject);

        $this->facade->resolveFlag($flagId, $resolverUserId);

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }

    private function getPatientSubjectForVoter(string $patientId): PatientSubject
    {
        $patientView = $this->patientFacade->findByPatientId($patientId);

        if ($patientView === null) {
            throw $this->createNotFoundException(sprintf('Pacjent o identyfikatorze "%s", powiązany z flagą, nie został znaleziony.', $patientId));
        }

        $protocolsArray = array_map(
            fn (PatientProtocolView $protocolView) => $protocolView->protocol(),
            $patientView->protocols()->toArray()
        );

        return PatientSubject::createFromArray($patientView->hospitals(), $protocolsArray);
    }
}
