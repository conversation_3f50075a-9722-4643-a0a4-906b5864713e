<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Patient\Rest;

use App\Auth\Infrastructure\Security\UserSecurity;
use App\BackendForFrontend\Security\Patient\Voter\PatientSubject;
use App\BackendForFrontend\Security\Patient\Voter\PatientVoter;
use App\Patient\Application\FavoritePatientFacade;
use App\Patient\Application\PatientFacade;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\Requirement\Requirement;

#[AsController]
#[Route(path: '/patient', name: 'patient_')]
class FavoritePatientApiController extends AbstractController
{
    private const string NO_ACCESS_TO_FAVORITES = 'Nie możesz dodać tego pacjenta do ulubionych. Szpital lub jednostka chorobowa, do której jest przypisany, nie znajduje się w zakresie Twoich uprawnień. Sprawdź swoje uprawnienia lub skontaktuj się z administratorem, aby uzyskać więcej informacji.';

    public function __construct(
        private readonly PatientFacade $patientFacade,
        private readonly FavoritePatientFacade $favoritePatientFacade,
    ) {
    }

    #[Route(path: '/patients/{patientId}/favorite-patients', name: 'favorite_patient_add', requirements: ['patientId' => Requirement::UUID_V4], methods: ['POST'])]
    public function addFavoritePatient(string $patientId): JsonResponse
    {
        /** @var UserSecurity $user */
        $user = $this->getUser();
        $authUserId = $user->authUser()->aggregateRootId();

        $voterSubject = $this->getPatientSubjectToVoterFromPatientView($patientId);
        $this->denyAccessUnlessGranted(PatientVoter::SHOW, $voterSubject, self::NO_ACCESS_TO_FAVORITES);

        $this->favoritePatientFacade->addFavoritePatient($authUserId, $patientId);

        return new JsonResponse(null, Response::HTTP_CREATED);
    }

    #[Route(path: '/patients/{patientId}/favorite-patients', name: 'favorite_patient_remove', requirements: ['patientId' => Requirement::UUID_V4], methods: ['DELETE'])]
    public function removeFavoritePatient(string $patientId): JsonResponse
    {
        /** @var UserSecurity $user */
        $user = $this->getUser();
        $authUserId = $user->authUser()->aggregateRootId();

        $this->favoritePatientFacade->removeFavoritePatient($authUserId, $patientId);

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }

    #[Route(path: '/patients/{patientId}/favorite-patients', name: 'favorite_patient_is_favorite', requirements: ['patientId' => Requirement::UUID_V4], methods: ['GET'])]
    public function isPatientFavorite(string $patientId): JsonResponse
    {
        /** @var UserSecurity $user */
        $user = $this->getUser();
        $authUserId = $user->authUser()->aggregateRootId();

        $isFavorite = $this->favoritePatientFacade->isPatientFavorite($authUserId, $patientId);

        return new JsonResponse(['isFavorite' => $isFavorite], Response::HTTP_OK);
    }

    private function getPatientSubjectToVoterFromPatientView(string $patientId): PatientSubject
    {
        $patientView = $this->patientFacade->findByPatientId($patientId);

        if ($patientView === null) {
            throw $this->createNotFoundException();
        }

        return PatientSubject::createFromArray($patientView->hospitals(), $patientView->protocolsValueList());
    }
}
