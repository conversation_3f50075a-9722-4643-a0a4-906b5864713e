<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Patient\Rest;

use App\BackendForFrontend\Security\Patient\Dto\GetPeselDetailsDto;
use App\Common\Attribute\DoNotLogRequest;
use App\Patient\Application\PatientFacade;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\Routing\Attribute\Route;

#[AsController]
#[Route(path: '/patient', name: 'patient_')]
class PeselApiController extends AbstractController
{
    public function __construct(private readonly PatientFacade $patientFacade)
    {
    }

    #[DoNotLogRequest]
    #[Route(path: '/pesel/details', name: 'get_pesel_details', methods: ['POST'])]
    public function create(GetPeselDetailsDto $dto): JsonResponse
    {
        $peselDetails = $this->patientFacade->getPeselDetails($dto->pesel);

        return new JsonResponse($peselDetails, Response::HTTP_OK);
    }
}
