<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Patient\Rest;

use App\Auth\Infrastructure\Security\UserSecurity;
use App\BackendForFrontend\Security\Patient\Dto\AddPatientAttachmentDto;
use App\BackendForFrontend\Security\Patient\Dto\AddPatientProtocolDto;
use App\BackendForFrontend\Security\Patient\Dto\AddPatientRodoConsentDto;
use App\BackendForFrontend\Security\Patient\Dto\CreatePatientDto;
use App\BackendForFrontend\Security\Patient\Dto\DeactivatePatientDto;
use App\BackendForFrontend\Security\Patient\Dto\UpdatePatientDto;
use App\BackendForFrontend\Security\Patient\Voter\PatientSubject;
use App\BackendForFrontend\Security\Patient\Voter\PatientVoter;
use App\Common\Query\GridConfiguration;
use App\Common\Query\GridField;
use App\Common\Query\GridFilterMethod;
use App\Common\Uuid;
use App\Patient\Application\PatientFacade;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\Requirement\Requirement;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[AsController]
#[Route(path: '/patient', name: 'patient_')]
class PatientApiController extends AbstractController
{
    private const string NO_ACCESS_TO_CREATE = 'Nie możesz dodać tego pacjenta. Szpital lub jednostka chorobowa, do której próbujesz go przypisać, nie znajduje się w zakresie Twoich uprawnień. Sprawdź swoje uprawnienia lub skontaktuj się z administratorem, aby uzyskać więcej informacji.';
    private const string NO_ACCESS_TO_UPDATE = 'Nie możesz zaktualizować tego pacjenta. Szpital lub jednostka chorobowa, do której próbujesz go przypisać, nie znajduje się w zakresie Twoich uprawnień. Sprawdź swoje uprawnienia lub skontaktuj się z administratorem, aby uzyskać więcej informacji.';
    private const string NO_ACCESS_TO_ADD_PATIENT_PROTOCOL = 'Nie możesz dodać jednostki chorobowej do tego pacjenta. Wybrana jednostka chorobowa, nie znajduje się w zakresie Twoich uprawnień. Sprawdź swoje uprawnienia lub skontaktuj się z administratorem, aby uzyskać więcej informacji.';
    private const string NO_ACCESS_TO_ADD_PATIENT_RODO_CONSENT = 'Nie możesz dodać załącznika ze zgodą RODO dla tego pacjenta. Wybrany pacjent, nie znajduje się w zakresie Twoich uprawnień. Sprawdź swoje uprawnienia lub skontaktuj się z administratorem, aby uzyskać więcej informacji.';
    private const string NO_ACCESS_TO_SHOW = 'Nie możesz pobrać tego pacjenta. Nie znajduje się on w zakresie Twoich uprawnień. Sprawdź swoje uprawnienia lub skontaktuj się z administratorem, aby uzyskać więcej informacji.';

    private const string NO_ACCESS_DOWNLOAD_PATIENT_RODO_CONSENT = 'Nie możesz pobrać zgody RODO tego pacjenta. Nie znajduje się on w zakresie Twoich uprawnień. Sprawdź swoje uprawnienia lub skontaktuj się z administratorem, aby uzyskać więcej informacji.';
    private const string NO_ACCESS_TO_ADD_ATTACHMENT = 'Nie możesz dodać załącznika dla tego pacjenta. Nie znajduje się on w zakresie Twoich uprawnień. Sprawdź swoje uprawnienia lub skontaktuj się z administratorem, aby uzyskać więcej informacji.';
    private const string NO_ACCESS_TO_DOWNLOAD_ATTACHMENT = 'Nie możesz pobrać tego załącznika pacjenta. Nie znajduje się on w zakresie Twoich uprawnień. Sprawdź swoje uprawnienia lub skontaktuj się z administratorem, aby uzyskać więcej informacji.';
    private const string NO_ACCESS_TO_DELETE_ATTACHMENT = 'Nie możesz usunąć tego załącznika pacjenta. Nie znajduje się on w zakresie Twoich uprawnień. Sprawdź swoje uprawnienia lub skontaktuj się z administratorem, aby uzyskać więcej informacji.';

    public function __construct(
        private readonly PatientFacade $patientFacade,
    ) {
    }

    #[Route(path: '/patients', name: 'patient_create', methods: ['POST'])]
    public function create(CreatePatientDto $dto): JsonResponse
    {
        $voterSubject = PatientSubject::createFromArray($dto->hospitals, [$dto->protocol]);
        $this->denyAccessUnlessGranted(PatientVoter::CREATE, $voterSubject, self::NO_ACCESS_TO_CREATE);

        $patientId = Uuid::generate();
        $this->patientFacade->create($patientId->toString(), $dto);

        return new JsonResponse(['patientId' => $patientId], Response::HTTP_CREATED);
    }

    #[Route(path: '/patients/{patientId}', name: 'patient_update', requirements: ['patientId' => Requirement::UUID_V4], methods: ['PUT'])]
    public function update(string $patientId, UpdatePatientDto $dto): JsonResponse
    {
        $voterSubject = $this->getPatientSubjectToVoterFromPatientView($patientId);
        $this->denyAccessUnlessGranted(PatientVoter::UPDATE, $voterSubject, self::NO_ACCESS_TO_UPDATE);

        $this->patientFacade->update($patientId, $dto);

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }

    #[Route(path: '/patients/{patientId}/activate', name: 'patient_activate', requirements: ['patientId' => Requirement::UUID_V4], methods: ['PATCH'])]
    #[IsGranted('ROLE_CENTRAL_ADMINISTRATOR')]
    public function activate(string $patientId): JsonResponse
    {
        $this->patientFacade->activate($patientId);

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }

    #[Route(path: '/patients/{patientId}/deactivate', name: 'patient_deactivate', requirements: ['patientId' => Requirement::UUID_V4], methods: ['PATCH'])]
    #[IsGranted('ROLE_CENTRAL_ADMINISTRATOR')]
    public function deactivate(string $patientId, DeactivatePatientDto $dto): JsonResponse
    {
        $this->patientFacade->deactivate($patientId, $dto->reason);

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }

    #[Route(path: '/patients/{patientId}/hospitals/{hospitalId}', name: 'patient_add_hospital', requirements: ['patientId' => Requirement::UUID_V4, 'hospitalId' => Requirement::UUID_V4], methods: ['POST'])]
    #[IsGranted('ROLE_CENTRAL_ADMINISTRATOR')]
    public function addHospital(string $patientId, string $hospitalId): JsonResponse
    {
        $this->patientFacade->addHospital($patientId, $hospitalId);

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }

    #[Route(path: '/patients/{patientId}/hospitals/{hospitalId}', name: 'patient_remove_hospital', requirements: ['patientId' => Requirement::UUID_V4, 'hospitalId' => Requirement::UUID_V4], methods: ['DELETE'])]
    #[IsGranted('ROLE_CENTRAL_ADMINISTRATOR')]
    public function removeHospital(string $patientId, string $hospitalId): JsonResponse
    {
        $this->patientFacade->removeHospital($patientId, $hospitalId);

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }

    #[Route(path: '/patients/{patientId}/protocols', name: 'patient_add_protocol', requirements: ['patientId' => Requirement::UUID_V4], methods: ['POST'])]
    public function addPatientProtocol(string $patientId, AddPatientProtocolDto $dto): JsonResponse
    {
        $patientSubject = $this->getPatientSubjectToVoterFromPatientViewAndProtocol($patientId, $dto->protocol);
        $this->denyAccessUnlessGranted(PatientVoter::ADD_PATIENT_PROTOCOL, $patientSubject, self::NO_ACCESS_TO_ADD_PATIENT_PROTOCOL);

        $patientProtocolId = Uuid::generate()->toString();
        $this->patientFacade->addPatientProtocol($patientId, $patientProtocolId, $dto->protocol);

        return new JsonResponse(['patientProtocolId' => $patientProtocolId], Response::HTTP_CREATED);
    }

    #[Route(path: '/patients/{patientId}/rodo-consents', name: 'patient_add_rodo_consent', requirements: ['patientId' => Requirement::UUID_V4], methods: ['POST'])]
    public function addPatientRodoConsent(string $patientId, AddPatientRodoConsentDto $dto): JsonResponse
    {
        $patientSubject = $this->getPatientSubjectToVoterFromPatientView($patientId);
        $this->denyAccessUnlessGranted(PatientVoter::ADD_PATIENT_RODO_CONSENT, $patientSubject, self::NO_ACCESS_TO_ADD_PATIENT_RODO_CONSENT);

        $patientRodoConsentId = Uuid::generate()->toString();
        $this->patientFacade->addPatientRodoConsent(
            $patientId,
            $patientRodoConsentId,
            $dto->fileName,
            $dto->extension,
            $dto->fileContentBase64
        );

        return new JsonResponse(['patientRodoConsentId' => $patientRodoConsentId], Response::HTTP_CREATED);
    }

    #[Route(path: '/patients/{patientId}/rodo-consents/{patientRodoConsentId}', name: 'patient_get_rodo_consent_file', requirements: ['patientId' => Requirement::UUID_V4, 'patientRodoConsentId' => Requirement::UUID_V4], methods: ['GET'])]
    public function getPatientRodoConsentFile(string $patientId, string $patientRodoConsentId, Request $request): BinaryFileResponse|JsonResponse
    {
        $patientSubject = $this->getPatientSubjectToVoterFromPatientView($patientId);
        $this->denyAccessUnlessGranted(
            PatientVoter::DOWNLOAD_PATIENT_RODO_CONSENT,
            $patientSubject,
            self::NO_ACCESS_DOWNLOAD_PATIENT_RODO_CONSENT
        );

        $filePath = $this->patientFacade->getPatientRodoConsentFilePath($patientId, $patientRodoConsentId);

        if (!file_exists($filePath)) {
            throw $this->createNotFoundException('Plik nie istnieje.');
        }

        if ($request->query->get('base64') === 'true') {
            return new JsonResponse([
                'type' => pathinfo($filePath, PATHINFO_EXTENSION),
                'data' => base64_encode((string) file_get_contents($filePath)),
            ]);
        }

        return $this->file($filePath);
    }

    #[Route(path: '/patients/{patientId}/protocols/{patientProtocolId}/activate', name: 'patient_activate_protocol', requirements: ['patientId' => Requirement::UUID_V4, 'patientProtocolId' => Requirement::UUID_V4], methods: ['PATCH'])]
    #[IsGranted('ROLE_CENTRAL_ADMINISTRATOR')]
    public function activatePatientProtocol(string $patientId, string $patientProtocolId): JsonResponse
    {
        $this->patientFacade->activatePatientProtocol($patientId, $patientProtocolId);

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }

    #[Route(path: '/patients/{patientId}/protocols/{patientProtocolId}/deactivate', name: 'patient_deactivate_protocol', requirements: ['patientId' => Requirement::UUID_V4, 'patientProtocolId' => Requirement::UUID_V4], methods: ['PATCH'])]
    #[IsGranted('ROLE_CENTRAL_ADMINISTRATOR')]
    public function deactivatePatientProtocol(string $patientId, string $patientProtocolId): JsonResponse
    {
        $this->patientFacade->deactivatePatientProtocol($patientId, $patientProtocolId);

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }

    #[Route(path: '/patients/{patientId}', name: 'patient_get', requirements: ['patientId' => Requirement::UUID_V4], methods: ['GET'])]
    public function get(string $patientId): JsonResponse
    {
        $voterSubject = $this->getPatientSubjectToVoterFromPatientView($patientId);
        $this->denyAccessUnlessGranted(PatientVoter::SHOW, $voterSubject, self::NO_ACCESS_TO_SHOW);

        $patient = $this->patientFacade->findByPatientId($patientId);

        return new JsonResponse($patient, $patient === null ? Response::HTTP_NOT_FOUND : Response::HTTP_OK);
    }

    #[Route(path: '/patients', name: 'patients_get_for_grid', methods: ['GET'])]
    public function getForGrid(Request $request): JsonResponse
    {
        /** @var UserSecurity $user */
        $user = $this->getUser();

        $gridFields = new ArrayCollection();
        $gridFields->add(GridField::create('patientId', true, true));
        $gridFields->add(GridField::create('patientPublicId', true, true));
        $gridFields->add(GridField::create('firstName', true, true));
        $gridFields->add(GridField::create('lastName', true, true));
        $gridFields->add(GridField::create('gender', true, true, GridFilterMethod::EQUALS));
        $gridFields->add(GridField::create('birthDate', true, true));
        $gridFields->add(GridField::create('isActive', true, true, GridFilterMethod::BOOL));
        $gridFields->add(GridField::create('isDeceased', true, true, GridFilterMethod::BOOL));
        $gridFields->add(GridField::create('hospitals', true, false, GridFilterMethod::IN_JSON_CONTAINS));
        $gridFields->add(GridField::create('protocols', true, false, GridFilterMethod::IN));
        $gridFields->add(GridField::create('followUpDateOfCompletionForm', true, true, GridFilterMethod::EQUALS));

        $gridConfiguration = GridConfiguration::createFromRequest($request, $gridFields);
        $gridResult = $this->patientFacade->findForGrid($gridConfiguration,
            $user->getHospitals(),
            $user->getProtocols()
        );

        return new JsonResponse($gridResult, Response::HTTP_OK);
    }

    #[Route(path: '/patients/{patientId}/attachments', name: 'patient_add_attachment', requirements: ['patientId' => Requirement::UUID_V4], methods: ['POST'])]
    public function addPatientAttachment(string $patientId, AddPatientAttachmentDto $dto): JsonResponse
    {
        $patientSubject = $this->getPatientSubjectToVoterFromPatientView($patientId);
        $this->denyAccessUnlessGranted(PatientVoter::ADD_ATTACHMENT, $patientSubject, self::NO_ACCESS_TO_ADD_ATTACHMENT);

        $patientAttachmentId = $this->patientFacade->addPatientAttachment($patientId, $dto);

        return new JsonResponse(['patientAttachmentId' => $patientAttachmentId], Response::HTTP_CREATED);
    }

    #[Route(path: '/patients/{patientId}/attachments/{patientAttachmentId}', name: 'patient_get_attachment_file', requirements: ['patientId' => Requirement::UUID_V4, 'patientAttachmentId' => Requirement::UUID_V4], methods: ['GET'])]
    public function getPatientAttachmentFile(string $patientId, string $patientAttachmentId, Request $request): BinaryFileResponse|JsonResponse
    {
        $patientSubject = $this->getPatientSubjectToVoterFromPatientView($patientId);
        $this->denyAccessUnlessGranted(
            PatientVoter::DOWNLOAD_ATTACHMENT,
            $patientSubject,
            self::NO_ACCESS_TO_DOWNLOAD_ATTACHMENT
        );

        $filePath = $this->patientFacade->getPatientAttachmentFilePath($patientId, $patientAttachmentId);

        if (!file_exists($filePath)) {
            throw $this->createNotFoundException('Plik załącznika nie istnieje.');
        }

        if ($request->query->get('base64') === 'true') {
            return new JsonResponse([
                'type' => pathinfo($filePath, PATHINFO_EXTENSION),
                'data' => base64_encode((string) file_get_contents($filePath)),
            ]);
        }

        return $this->file($filePath);
    }

    #[IsGranted('ROLE_CENTRAL_ADMINISTRATOR')]
    #[Route(path: '/patients/{patientId}/attachments/{patientAttachmentId}', name: 'patient_delete_attachment', requirements: ['patientId' => Requirement::UUID_V4, 'patientAttachmentId' => Requirement::UUID_V4], methods: ['DELETE'])]
    public function deletePatientAttachment(string $patientId, string $patientAttachmentId): JsonResponse
    {
        $patientSubject = $this->getPatientSubjectToVoterFromPatientView($patientId);
        $this->denyAccessUnlessGranted(
            PatientVoter::DELETE_ATTACHMENT,
            $patientSubject,
            self::NO_ACCESS_TO_DELETE_ATTACHMENT
        );

        $this->patientFacade->deletePatientAttachment($patientId, $patientAttachmentId);

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }

    private function getPatientSubjectToVoterFromPatientView(string $patientId): PatientSubject
    {
        $patientView = $this->patientFacade->findByPatientId($patientId);

        if ($patientView === null) {
            throw $this->createNotFoundException();
        }

        return PatientSubject::createFromArray($patientView->hospitals(), $patientView->protocolsValueList());
    }

    private function getPatientSubjectToVoterFromPatientViewAndProtocol(string $patientId, string $protocol): PatientSubject
    {
        $patientView = $this->patientFacade->findByPatientId($patientId);

        if ($patientView === null) {
            throw $this->createNotFoundException(sprintf('Nie znaleziono pacjenta o identyfikatorze %s.', $patientId));
        }

        return PatientSubject::createFromArray($patientView->hospitals(), [$protocol]);
    }
}
