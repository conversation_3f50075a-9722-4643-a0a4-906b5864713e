<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Patient\Validator;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

class Base64FileValidator extends ConstraintValidator
{
    public function validate(mixed $value, Constraint $constraint): void
    {
        if (!$constraint instanceof Base64File) {
            throw new UnexpectedTypeException($constraint, Base64File::class);
        }

        if (null === $value || '' === $value) {
            return;
        }

        // Check if the string is valid base64
        if (!preg_match('%^[a-zA-Z0-9/+]*={0,2}$%', $value)) {
            $this->context->buildViolation($constraint->invalidBase64Message)
                ->setParameter('{{ value }}', $value)
                ->addViolation();

            return;
        }

        // Check if the decoded file is a valid jpg or pdf
        $decodedFile = base64_decode($value);
        $finfo = new \finfo(FILEINFO_MIME_TYPE);
        $mimeType = $finfo->buffer($decodedFile);

        if (!in_array($mimeType, ['image/jpeg', 'application/pdf'])) {
            $this->context->buildViolation($constraint->invalidFileMessage)
                ->setParameter('{{ value }}', $value)
                ->addViolation();
        }
    }
}
