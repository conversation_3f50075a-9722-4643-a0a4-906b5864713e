<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Patient\Validator;

use Symfony\Component\Validator\Constraint;

/**
 * @Annotation
 */
#[\Attribute] class Base64File extends Constraint
{
    public string $invalidBase64Message = 'Przesłane dane nie są poprawnym ciągiem base64.';
    public string $invalidFileMessage = 'Przesłane dane nie są poprawnym plikiem jpg lub pdf.';
}
