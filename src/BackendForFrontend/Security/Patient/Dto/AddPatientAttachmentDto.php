<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Patient\Dto;

use App\BackendForFrontend\Security\Patient\Validator\Base64File;
use App\Common\RequestDtoInterface;
use App\Patient\Domain\PatientAttachmentType;
use Symfony\Component\Validator\Constraints as Assert;

class AddPatientAttachmentDto implements RequestDtoInterface
{
    #[Assert\NotBlank]
    #[Assert\Length(min: 1, max: 255)]
    public string $fileName;

    #[Assert\NotBlank]
    #[Assert\Choice(choices: ['pdf', 'jpg', 'jpeg', 'png'], message: 'Nieprawidłowe rozszerzenie pliku. Dozwolone rozszerzenia to pdf, jpg, jpeg, png.')]
    public string $extension;

    #[Assert\NotBlank]
    #[Base64File]
    #[Assert\Length(max: 14 * 1024 * 1024, maxMessage: 'Maksymalny rozmiar pliku to 10MB.')]
    public string $fileContentBase64;

    #[Assert\NotBlank]
    #[Assert\Choice(callback: [PatientAttachmentType::class, 'values'], message: 'Nieprawidłowy typ załącznika.')]
    public string $type;
}
