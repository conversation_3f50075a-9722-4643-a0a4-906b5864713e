<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Patient\Dto;

use App\Common\RequestDtoInterface;
use Symfony\Component\Validator\Constraints as Assert;

class GetPeselDetailsDto implements RequestDtoInterface
{
    #[Assert\NotBlank()]
    #[Assert\Length(min: 11, max: 11)]
    #[Assert\Regex(pattern: "/^\d+$/", message: 'Numer PESEL powinien składać się wyłącznie z cyfr.')]
    public string $pesel;
}
