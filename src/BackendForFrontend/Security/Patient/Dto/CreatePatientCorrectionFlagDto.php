<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Patient\Dto;

use App\Common\RequestDtoInterface;
use Symfony\Component\Validator\Constraints as Assert;

final class CreatePatientCorrectionFlagDto implements RequestDtoInterface
{
    #[Assert\NotBlank]
    #[Assert\Uuid]
    public string $hospitalId;

    #[Assert\NotBlank]
    #[Assert\Length(max: 1000)]
    public string $comment;
}
