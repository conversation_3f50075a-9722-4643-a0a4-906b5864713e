<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Patient\Dto;

use App\BackendForFrontend\Security\Patient\Validator\Base64File;
use App\Common\RequestDtoInterface;
use Symfony\Component\Validator\Constraints as Assert;

class AddPatientRodoConsentDto implements RequestDtoInterface
{
    #[Assert\NotBlank]
    #[Assert\Length(min: 1, max: 255)]
    public string $fileName;

    #[Assert\NotBlank]
    #[Assert\Choice(choices: ['jpg', 'jpeg', 'pdf'], message: 'Nieprawidłowe rozszerzenie pliku. Dozwolone rozszerzenia to jpg, jpeg, pdf.')]
    public string $extension;

    #[Assert\NotBlank]
    #[Base64File]
    #[Assert\Length(max: 14 * 1024 * 1024, maxMessage: 'Maksymal<PERSON> rozmiar pliku to 10MB.')]
    public string $fileContentBase64;
}
