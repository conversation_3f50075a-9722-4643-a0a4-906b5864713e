<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Patient\Dto;

use App\Common\RequestDtoInterface;
use App\Patient\Domain\Citizenship;
use App\Patient\Domain\Gender;
use App\SharedKernel\Protocol;
use Symfony\Component\Validator\Constraints as Assert;

class CreatePatientDto implements RequestDtoInterface
{
    #[Assert\NotBlank()]
    #[Assert\Length(max: 50)]
    public string $firstName;

    #[Assert\NotBlank()]
    #[Assert\Length(max: 50)]
    public string $lastName;

    #[Assert\Choice(callback: [Citizenship::class, 'values'])]
    public string $citizenship;

    #[Assert\NotNull()]
    public bool $isRegisteredAddressSameAsResidence;

    #[Assert\NotNull()]
    public bool $isCorrespondenceAddressSameAsResidence;

    #[Assert\NotBlank()]
    #[Assert\Valid()]
    public AddressDto $residenceAddress;

    #[Assert\Valid()]
    public ?AddressDto $registeredAddress = null;

    #[Assert\Valid()]
    public ?AddressDto $correspondenceAddress = null;

    #[Assert\Length(max: 100)]
    #[Assert\Email()]
    public ?string $email = null;

    #[Assert\Length(max: 20)]
    #[Assert\Regex(pattern: "/^[^\s]*$/", message: 'Numer kontaktowy nie powinien zawierać spacji.')]
    public ?string $contactNumber = null;

    #[Assert\NotNull()]
    public bool $isPatientIdentified;

    #[Assert\Length(min: 11, max: 11)]
    #[Assert\Regex(pattern: "/^\d+$/", message: 'Numer PESEL powinien składać się wyłącznie z cyfr.')]
    public ?string $pesel;

    #[Assert\Length(max: 20)]
    public ?string $passportNumber;
    #[Assert\Length(min: 11, max: 11)]
    #[Assert\Regex(pattern: "/^\d+$/", message: 'Numer PESEL powinien składać się wyłącznie z cyfr.')]
    public ?string $legalRepresentativePesel;
    #[Assert\Length(max: 20)]
    public ?string $legalRepresentativePassportNumber;

    #[Assert\Choice(callback: [Gender::class, 'values'])]
    public string $gender;

    #[Assert\Date]
    public string $birthDate;

    /** @var array<string> */
    #[Assert\All([
        new Assert\NotBlank(),
        new Assert\Uuid(),
    ])]
    #[Assert\Count(min: 1)]
    public array $hospitals;

    #[Assert\Choice(callback: [Protocol::class, 'values'])]
    public string $protocol;

    #[Assert\Valid()]
    #[Assert\NotBlank()]
    public AddPatientRodoConsentDto $rodoConsent;
}
