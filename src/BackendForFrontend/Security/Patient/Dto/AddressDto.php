<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Patient\Dto;

use Symfony\Component\Validator\Constraints as Assert;

class AddressDto
{
    #[Assert\NotBlank()]
    #[Assert\Length(max: 100)]
    public string $streetWithNumber;

    #[Assert\NotBlank()]
    #[Assert\Length(min: 6, max: 6)]
    #[Assert\Regex(pattern: "/^\d{2}-\d{3}$/", message: 'Proszę wprowadzić kod pocztowy w formacie XX-XXX.')]
    public string $postCode;

    #[Assert\NotBlank()]
    #[Assert\Length(max: 40)]
    public string $city;
}
