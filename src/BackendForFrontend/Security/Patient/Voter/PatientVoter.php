<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Patient\Voter;

use App\Auth\Infrastructure\Security\UserSecurity;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;

class PatientVoter extends Voter
{
    public const CREATE = 'create';
    public const UPDATE = 'update';
    public const SHOW = 'show';
    public const ADD_PATIENT_PROTOCOL = 'add_patient_protocol';
    public const ADD_PATIENT_RODO_CONSENT = 'add_patient_rodo_consent';
    public const DOWNLOAD_PATIENT_RODO_CONSENT = 'download_patient_rodo_consent';
    public const ADD_ATTACHMENT = 'add_attachment';
    public const DOWNLOAD_ATTACHMENT = 'download_attachment';
    public const DELETE_ATTACHMENT = 'delete_attachment';
    public const RESOLVE_CORRECTION_FLAG = 'resolve_correction_flag';

    protected function supports(string $attribute, mixed $subject): bool
    {
        if (!$subject instanceof PatientSubject) {
            return false;
        }

        return in_array($attribute, [
            self::CREATE,
            self::UPDATE,
            self::SHOW,
            self::ADD_PATIENT_PROTOCOL,
            self::ADD_PATIENT_RODO_CONSENT,
            self::DOWNLOAD_PATIENT_RODO_CONSENT,
            self::ADD_ATTACHMENT,
            self::DOWNLOAD_ATTACHMENT,
            self::DELETE_ATTACHMENT,
            self::RESOLVE_CORRECTION_FLAG,
        ], true);
    }

    /** @param PatientSubject $subject */
    protected function voteOnAttribute(string $attribute, $subject, TokenInterface $token): bool
    {
        $user = $token->getUser();

        if (!$user instanceof UserSecurity) {
            return false;
        }

        // Central Admin can do anything related to patients
        if (in_array('ROLE_CENTRAL_ADMINISTRATOR', $user->getRoles(), true)) {
            return true;
        }

        $hospitals = $user->getHospitals();
        $protocols = $user->getProtocols();

        if (in_array('ROLE_REPORTER_PHYSICIAN', $user->getRoles(), true)) {
            return !empty(array_intersect($subject->hospitals(), $hospitals))
                && !empty(array_intersect($subject->protocols(), $protocols));
        }

        if (in_array('ROLE_COORDINATOR', $user->getRoles(), true)) {
            return !empty(array_intersect($subject->protocols(), $protocols));
        }

        if (in_array('ROLE_DATA_ADMINISTRATOR', $user->getRoles(), true)) {
            return !empty(array_intersect($subject->hospitals(), $hospitals));
        }

        return false;
    }
}
