<?php

declare(strict_types=1);

namespace App\BackendForFrontend\Security\Auth\Rest;

use App\Auth\Application\AuthAccountFacade;
use App\Auth\Infrastructure\Security\UserSecurity;
use App\BackendForFrontend\Security\Auth\Dto\LogoutDto;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class AuthController extends AbstractController
{
    private AuthAccountFacade $authAccountFacade;

    public function __construct(AuthAccountFacade $authAccountFacade)
    {
        $this->authAccountFacade = $authAccountFacade;
    }

    #[Route('/logout', name: 'auth_logout', methods: ['POST'])]
    public function logout(LogoutDto $logoutDto): JsonResponse
    {
        /** @var ?UserSecurity $user */
        $user = $this->getUser();

        if ($user === null) {
            return new JsonResponse(null, Response::HTTP_UNAUTHORIZED);
        }

        $this->authAccountFacade->logoutAuthUser(
            $user->getRootId(),
            $user->getUserIdentifier(),
            $logoutDto->refreshToken
        );

        return new JsonResponse(null, 200);
    }
}
