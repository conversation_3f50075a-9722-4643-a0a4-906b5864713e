<?php

declare(strict_types=1);

namespace App\BackendForFrontend\External;

use App\Account\Application\AccountFacade;
use App\Dictionary\DictionaryFacade;
use App\Form\FormFacade;
use App\Hospital\Application\HospitalFacade;
use App\Patient\Application\PatientCorrectionFlagFacade;
use App\Patient\Application\PatientFacade;
use App\SharedKernel\Protocol;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\StreamedJsonResponse;
use Symfony\Component\HttpKernel\Attribute\AsController;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[AsController]
#[IsGranted('ROLE_EXTERNAL_API_ANALYTICS')]
#[Route('/analytic', name: 'analytic_')]
final class DataAnalyticsAPIController extends AbstractController
{
    public function __construct(
        private readonly AccountFacade $accountFacade,
        private readonly HospitalFacade $hospitalFacade,
        private readonly PatientFacade $patientFacade,
        private readonly FormFacade $formFacade,
        private readonly DictionaryFacade $dictionaryFacade,
        private readonly PatientCorrectionFlagFacade $patientCorrectionFlagFacade,
    ) {
    }

    #[Route('/accounts', name: 'accounts', methods: ['GET'])]
    public function getAllAccounts(Request $request): JsonResponse
    {
        $changedSince = $request->query->get('changedSince');
        $changedSinceDateTime = $changedSince ? new \DateTimeImmutable($changedSince) : null;
        $accounts = $this->accountFacade->findAll($changedSinceDateTime);

        return $this->json($accounts);
    }

    #[Route('/hospitals', name: 'get_all_hospitals', methods: ['GET'])]
    public function getAllHospitals(Request $request): JsonResponse
    {
        $changedSince = $request->query->get('changedSince');
        $changedSinceDateTime = $changedSince ? new \DateTimeImmutable($changedSince) : null;
        $hospitals = $this->hospitalFacade->findAll($changedSinceDateTime);

        return $this->json($hospitals);
    }

    #[Route('/patients', name: 'get_all_patients', methods: ['GET'])]
    public function getAllPatients(Request $request): JsonResponse
    {
        $changedSince = $request->query->get('changedSince');
        $changedSinceDateTime = $changedSince ? new \DateTimeImmutable($changedSince) : null;
        $patients = $this->patientFacade->findAll($changedSinceDateTime);

        return $this->json($patients);
    }

    #[Route('/forms', name: 'get_all_forms', methods: ['GET'])]
    public function getAllForms(Request $request): StreamedJsonResponse
    {
        $changedSince = $request->query->get('changedSince');
        $changedSinceDateTime = $changedSince ? new \DateTimeImmutable($changedSince) : null;
        $forms = $this->formFacade->findAll($changedSinceDateTime);

        return new StreamedJsonResponse($forms);
    }

    #[Route('/correction-flags', name: 'get_all_correction_flags', methods: ['GET'])]
    public function getAllCorrectionFlags(Request $request): JsonResponse
    {
        $changedSince = $request->query->get('changedSince');
        $createdSinceDateTime = $changedSince ? new \DateTimeImmutable($changedSince) : null;
        $flags = $this->patientCorrectionFlagFacade->findAll($createdSinceDateTime);

        return $this->json($flags);
    }

    #[Route('/protocols', name: 'get_all_protocols', methods: ['GET'])]
    public function getAllProtocols(): JsonResponse
    {
        $protocols = Protocol::values();
        $protocols = array_diff($protocols, [Protocol::TEST->value]);

        return $this->json($protocols);
    }

    #[Route('/dictionaries', name: 'get_all_dictionaries', methods: ['GET'])]
    public function getAllDictionaries(): JsonResponse
    {
        $dictionaries = $this->dictionaryFacade->findAll();

        return $this->json($dictionaries);
    }
}
