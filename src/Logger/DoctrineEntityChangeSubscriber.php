<?php

declare(strict_types=1);

namespace App\Logger;

use App\Auth\Infrastructure\Security\UserSecurity;
use App\Common\Attribute\TrackEntityChange;
use App\Common\CorrelationId\CorrelationIdStorage;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\Common\EventSubscriber;
use Doctrine\ORM\Events;
use Doctrine\Persistence\Event\LifecycleEventArgs;
use Symfony\Bundle\SecurityBundle\Security;

#[AsDoctrineListener(event: Events::postUpdate)]
#[AsDoctrineListener(event: Events::postPersist)]
#[AsDoctrineListener(event: Events::postRemove)]
readonly class DoctrineEntityChangeSubscriber implements EventSubscriber
{
    public function __construct(
        private Security $security,
        private LogRepository $eventLogRepository,
        private CorrelationIdStorage $correlationIdStorage)
    {
    }

    public function getSubscribedEvents(): array
    {
        return [
            Events::postUpdate,
            Events::postPersist,
            Events::postRemove,
        ];
    }

    /** @phpstan-ignore-next-line */
    public function postUpdate(LifecycleEventArgs $args): void
    {
        $this->handleEvent($args, 'UPDATE');
    }

    /** @phpstan-ignore-next-line */
    public function postPersist(LifecycleEventArgs $args): void
    {
        $this->handleEvent($args, 'CREATE');
    }

    /** @phpstan-ignore-next-line */
    public function postRemove(LifecycleEventArgs $args): void
    {
        $this->handleEvent($args, 'DELETE');
    }

    /** @phpstan-ignore-next-line */
    private function handleEvent(LifecycleEventArgs $args, string $action): void
    {
        if (getenv('FIXTURES_LOADING') === 'true') {
            return;
        }

        $entity = $args->getObject();
        $reflectionClass = new \ReflectionClass($entity);

        $attributes = $reflectionClass->getAttributes(TrackEntityChange::class);
        if (empty($attributes)) {
            return;
        }

        $objectLogDetails = $this->getLogDetails($attributes, $entity, $reflectionClass);

        /** @phpstan-ignore-next-line */
        $changeSet = $args->getObjectManager()->getUnitOfWork()->getEntityChangeSet($entity);

        if (empty($changeSet)) {
            /** @phpstan-ignore-next-line */
            $changeSet = $args->getObjectManager()->getUnitOfWork()->getOriginalEntityData($entity);
        }

        /** @phpstan-ignore-next-line */
        $objectId = $args->getObjectManager()->getUnitOfWork()->getSingleIdentifierValue($entity);

        $changeSetJson = json_encode($changeSet, JSON_THROW_ON_ERROR | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES, 10);

        $eventLog = Log::create(
            $this->correlationIdStorage->getCorrelationId() ?? '',
            LogType::entity,
            $action,
            $reflectionClass->getName(),
            $changeSetJson
        );

        $eventLog->setObjectId((string) $objectId);
        $eventLog->setObjectDetails($objectLogDetails);

        /** @var UserSecurity|null $user */
        $user = $this->security->getUser();
        if ($user) {
            $eventLog->setAuthUserId($user->getRootId());
        }

        $this->eventLogRepository->add($eventLog);
    }

    /** @phpstan-ignore-next-line */
    protected function getLogDetails(array $attributes, object $entity, \ReflectionClass $reflectionClass): array
    {
        /** @var TrackEntityChange $trackEntityChangeAttribute */
        $trackEntityChangeAttribute = $attributes[0]->newInstance();
        $objectLogDetailsFunction = $trackEntityChangeAttribute->objectLogDetailsFunction();

        if (!method_exists($entity, $objectLogDetailsFunction)) {
            throw new \RuntimeException(sprintf('Method %s does not exist in class %s', $objectLogDetailsFunction, $reflectionClass->getName()));
        }

        return $entity->$objectLogDetailsFunction();
    }
}
