<?php

declare(strict_types=1);

namespace App\Logger;

use App\Common\CorrelationId\CorrelationIdStorage;

readonly class LoggerService
{
    public function __construct(
        private CorrelationIdStorage $correlationIdStorage,
        private LogRepository $logRepository,
    ) {
    }

    /** @param array<string, mixed> $objectDetails */
    public function createCustomLog(LogType $logType, string $action, string $path, string $payload, string $objectId, array $objectDetails): void
    {
        $log = Log::create(
            $this->correlationIdStorage->getCorrelationId() ?? '',
            $logType,
            $action,
            $path,
            $payload);

        $log->setObjectId($objectId);
        $log->setObjectDetails($objectDetails);

        $this->logRepository->add($log);
    }
}
