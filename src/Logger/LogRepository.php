<?php

declare(strict_types=1);

namespace App\Logger;

use Doctrine\ORM\EntityManagerInterface;

class LogRepository
{
    public function __construct(private EntityManagerInterface $em)
    {
    }

    public function add(Log $eventLog): void
    {
        $this->em->persist($eventLog);
        $this->em->flush();
    }

    public function findByCorrelationIdAndPath(string $correlationId, string $path): ?Log
    {
        $queryBuilder = $this->em->createQueryBuilder()
            ->select('e')
            ->from(Log::class, 'e')
            ->where('e.correlationId = :correlationId')
            ->andWhere('e.path = :path')
            ->setParameter('correlationId', $correlationId)
            ->setParameter('path', $path)
            ->setMaxResults(1)
        ;

        return $queryBuilder->getQuery()->getOneOrNullResult();
    }

    public function deleteRowsWithCreatedAtBefore(\DateTimeImmutable $date): int
    {
        return $this->em->createQueryBuilder()
            ->delete(Log::class, 'e')
            ->where('e.createdAt < :date')
            ->setParameter('date', $date)
            ->getQuery()
            ->execute()
        ;
    }
}
