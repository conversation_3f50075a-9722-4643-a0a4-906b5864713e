<?php

declare(strict_types=1);

namespace App\Logger;

use App\Logger\Query\StatsLogQuery;

final readonly class LoggerStatsFacade
{
    public function __construct(
        private StatsLogQuery $statsLogQuery,
    ) {
    }

    public function countOfPatientsAddedInLast30DaysByAccount(string $accountId): int
    {
        return $this->statsLogQuery->countOfPatientsAddedInLast30DaysByAccount($accountId);
    }

    public function countOfUpdatedFormsInLast30DaysByAccount(string $accountId): int
    {
        return $this->statsLogQuery->countOfUpdatedFormsInLast30DaysByAccount($accountId);
    }

    /** @return array<int, string> */
    public function listLastPatientIdsOfUpdatedFormsByAccount(string $accountId): array
    {
        return $this->statsLogQuery->listLastPatientIdsOfUpdatedFormsByAccount($accountId);
    }
}
