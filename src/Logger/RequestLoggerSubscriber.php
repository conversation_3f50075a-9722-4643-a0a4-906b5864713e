<?php

declare(strict_types=1);

namespace App\Logger;

use App\Auth\Infrastructure\Security\UserSecurity;
use App\Common\Attribute\DoNotLogRequest;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Event\ControllerArgumentsEvent;
use Symfony\Component\HttpKernel\KernelEvents;

readonly class RequestLoggerSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private Security $security,
        private LogRepository $eventLogRepository)
    {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::CONTROLLER_ARGUMENTS => ['onKernelControllerArguments', 100],
        ];
    }

    public function onKernelControllerArguments(ControllerArgumentsEvent $event): void
    {
        $request = $event->getRequest();
        $controller = $event->getController();

        if ('GET' === $request->getMethod()) {
            return;
        }

        if ($this->hasDoNotLogAttribute($controller)) {
            return;
        }

        $requestContent = (match ($request->getContentTypeFormat()) {
            'form' => fn () => $this->formatFormContent($request),
            'json' => fn () => $this->cleanUpJson($request->getContent()),
            default => fn () => $request->getContent(),
        })();

        $correlationId = $request->headers->get('x-correlation-id');

        $eventLog = Log::create(
            $correlationId ?? '',
            LogType::request,
            $request->getMethod(),
            $request->getPathInfo(),
            $requestContent,
            $request->getClientIp(),
            $request->server->get('HTTP_REFERER'),
            $request->server->get('HTTP_USER_AGENT')
        );

        /** @var UserSecurity|null $user */
        $user = $this->security->getUser();
        if ($user) {
            $eventLog->setAuthUserId($user->getRootId());
        }

        $this->eventLogRepository->add($eventLog);
    }

    private function hasDoNotLogAttribute(mixed $controller): bool
    {
        // When controller is a closure, we can't check for attributes
        if (!is_array($controller)) {
            return true;
        }

        $controllerMethod = new \ReflectionMethod($controller[0], $controller[1]);

        // Check if the DoNotLogRequest attribute is set on the controller method
        $doNotLogAttribute = $controllerMethod->getAttributes(DoNotLogRequest::class);

        return count($doNotLogAttribute) > 0;
    }

    private function formatFormContent(Request $request): string
    {
        parse_str($request->getContent(), $array);

        return (string) json_encode($array);
    }

    private function cleanUpJson(string $json): string
    {
        $array = json_decode($json, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return $json;
        }

        $this->replaceNestedKeys($array);

        return json_encode($array, JSON_THROW_ON_ERROR | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }

    /** @param array<string, mixed> &$array */
    private function replaceNestedKeys(array &$array): void
    {
        $keysToReplace = ['fileContentBase64'];

        foreach ($array as $key => &$value) {
            if (is_array($value)) {
                $this->replaceNestedKeys($value);
            } elseif (in_array($key, $keysToReplace, true)) {
                $value = '[DoNotLog]';
            }
        }
    }
}
