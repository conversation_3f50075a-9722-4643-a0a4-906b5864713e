<?php

declare(strict_types=1);

namespace App\Logger;

use App\Auth\Infrastructure\Security\UserSecurity;
use App\Common\Attribute\EventLoggable;
use App\Common\CorrelationId\CorrelationIdStamp;
use App\Common\CorrelationId\CorrelationIdStorage;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Middleware\MiddlewareInterface;
use Symfony\Component\Messenger\Middleware\StackInterface;
use Symfony\Component\Messenger\Stamp\ReceivedStamp;

readonly class EventLoggerMiddleware implements MiddlewareInterface
{
    public function __construct(
        private Security $security,
        private LogRepository $eventLogRepository,
        private CorrelationIdStorage $correlationIdStorage,
    ) {
    }

    public function handle(Envelope $envelope, StackInterface $stack): Envelope
    {
        $message = $envelope->getMessage();

        $reflectionClass = new \ReflectionClass($message);
        if (0 === \count($reflectionClass->getAttributes(EventLoggable::class))) {
            return $stack->next()->handle($envelope, $stack);
        }

        if (!$envelope->last(ReceivedStamp::class)) {
            return $stack->next()->handle($envelope, $stack);
        }

        $stamps = $envelope->all();
        if (isset($stamps[CorrelationIdStamp::class])) {
            /** @phpstan-ignore-next-line */
            $correlationId = $stamps[CorrelationIdStamp::class][0]->getCorrelationId();
            $this->correlationIdStorage->setCorrelationId($correlationId);
        } else {
            $correlationId = '';
        }

        $eventLog = Log::create(
            $correlationId,
            LogType::event,
            basename(str_replace('\\', '/', get_class($message))),
            \get_class($message),
            json_encode($message, JSON_THROW_ON_ERROR | JSON_UNESCAPED_SLASHES)
        );

        /** @var UserSecurity|null $user */
        $user = $this->security->getUser();
        if ($user) {
            $eventLog->setAuthUserId($user->getRootId());
        }

        // Odkomentować jeżeli pojawią się zbędne duplikaty w tabeli logów
        //        if ($this->eventLogRepository->findByCorrelationIdAndPath($eventLog->getCorrelationId(), $eventLog->getPath())) {
        //            return $stack->next()->handle($envelope, $stack);
        //        }

        $this->eventLogRepository->add($eventLog);

        return $stack->next()->handle($envelope, $stack);
    }
}
