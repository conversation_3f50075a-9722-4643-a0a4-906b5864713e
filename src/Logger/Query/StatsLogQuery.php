<?php

declare(strict_types=1);

namespace App\Logger\Query;

use Doctrine\DBAL\Connection;

final class StatsLogQuery
{
    private const string TABLE_NAME = 'logger_log';

    public function __construct(
        private Connection $connection,
    ) {
    }

    public function countOfPatientsAddedInLast30DaysByAccount(string $accountId): int
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('COUNT(l.id)')
            ->from(self::TABLE_NAME, 'l')
            ->where('l.path = :path')
            ->andWhere('l.action = :action')
            ->andWhere('l.authUserId = :authUserId')
            ->andWhere('l.createdAt > :date')
            ->setParameter('path', 'App\Patient\Domain\Patient')
            ->setParameter('action', 'CREATE')
            ->setParameter('authUserId', $accountId)
            ->setParameter('date', (new \DateTime('-30 days'))->format('Y-m-d H:i:s'));

        return (int) $this->connection->fetchOne($queryBuilder->getSQL(), $queryBuilder->getParameters());
    }

    public function countOfUpdatedFormsInLast30DaysByAccount(string $accountId): int
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('COUNT(DISTINCT l.objectId)')
            ->from(self::TABLE_NAME, 'l')
            ->where('l.path = :path')
            ->andWhere('l.authUserId = :authUserId')
            ->andWhere('l.createdAt > :date')
            ->setParameter('path', 'App\Form\ProtocolFormData')
            ->setParameter('authUserId', $accountId)
            ->setParameter('date', (new \DateTime('-30 days'))->format('Y-m-d H:i:s'));

        return (int) $this->connection->fetchOne($queryBuilder->getSQL(), $queryBuilder->getParameters());
    }

    /** @return array<string> */
    public function listLastPatientIdsOfUpdatedFormsByAccount(string $accountId): array
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('TRIM(BOTH \'"\' FROM JSON_EXTRACT(l.objectDetails, "$.patientId")) as patientId')
            ->from(self::TABLE_NAME, 'l')
            ->where('l.path = :path')
            ->andWhere('l.authUserId = :authUserId')
            ->setParameter('path', 'App\Form\ProtocolFormData')
            ->setParameter('authUserId', $accountId)
            ->orderBy('l.createdAt', 'DESC');

        $data = $this->connection->fetchAllAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        return array_unique(array_column($data, 'patientId'));
    }
}
