<?php

declare(strict_types=1);

namespace App\Logger\Query;

use App\Common\QueryView;

class LogView implements QueryView
{
    private int $id;
    private string $action;
    private string $path;
    private string $objectId;
    /** @var array<string, mixed>|null */
    private ?array $objectDetails;
    private ?string $accountFirstName;
    private ?string $accountLastName;
    private string $createdAt;

    /** @param array<string, mixed>|null $objectDetails */
    public function __construct(
        int $id,
        string $action,
        string $path,
        string $objectId,
        ?array $objectDetails,
        ?string $accountFirstName,
        ?string $accountLastName,
        string $createdAt,
    ) {
        $this->id = $id;
        $this->action = $action;
        $this->path = $path;
        $this->objectId = $objectId;
        $this->objectDetails = $objectDetails;
        $this->accountFirstName = $accountFirstName;
        $this->accountLastName = $accountLastName;
        $this->createdAt = $createdAt;
    }

    /** @param array<string, mixed> $data */
    public static function deserialize(array $data): self
    {
        return new self(
            $data['id'],
            $data['action'],
            $data['path'],
            $data['objectId'],
            json_decode($data['objectDetails'] ?? null, true, 512, JSON_THROW_ON_ERROR),
            $data['accountFirstName'] ?? null,
            $data['accountLastName'] ?? null,
            $data['createdAt'],
        );
    }

    /** @return array<string, mixed> */
    public function jsonSerialize(): array
    {
        return [
            'id' => $this->id,
            'action' => $this->action,
            'path' => $this->path,
            'objectId' => $this->objectId,
            'objectDetails' => $this->objectDetails,
            'accountFirstName' => $this->accountFirstName,
            'accountLastName' => $this->accountLastName,
            'createdAt' => $this->createdAt,
        ];
    }
}
