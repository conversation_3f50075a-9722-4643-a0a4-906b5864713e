<?php

declare(strict_types=1);

namespace App\Logger\Query;

use App\Common\Query\GridConfiguration;
use App\Common\Query\GridResult;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Query\QueryBuilder;

class LogQuery
{
    private const string TABLE_NAME = 'logger_log';
    private const string TABLE_NAME_ACCOUNT = 'account_account';

    public function __construct(
        private Connection $connection,
    ) {
    }

    /** @return GridResult<int, LogView> */
    public function findForGrid(GridConfiguration $gridConfiguration): GridResult
    {
        $baseQueryBuilder = $this->prepareQueryForGrid($gridConfiguration);

        // Count total items for pagination
        $queryBuilderTotalItems = clone $baseQueryBuilder;
        $queryBuilderTotalItems->resetOrderBy()
            ->select('COUNT(DISTINCT l.id)');
        $totalItems = (int) $this->connection->fetchOne($queryBuilderTotalItems->getSQL(), $queryBuilderTotalItems->getParameters());

        // Add pagination
        $queryBuilder = clone $baseQueryBuilder;
        $queryBuilder->setFirstResult(($gridConfiguration->page() - 1) * $gridConfiguration->itemsPerPage())
            ->setMaxResults($gridConfiguration->itemsPerPage());

        $data = $this->connection->fetchAllAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());
        $collection = new ArrayCollection();

        foreach ($data as $row) {
            $collection->set($row['id'], LogView::deserialize($row));
        }

        return new GridResult($collection,
            $totalItems,
            $gridConfiguration->page(),
            $gridConfiguration->itemsPerPage()
        );
    }

    private function prepareQueryForGrid(GridConfiguration $gridConfiguration): QueryBuilder
    {
        $fieldDatabaseMapping = [
            'action' => 'l.action',
            'path' => 'l.path',
            'objectId' => 'l.objectId',
            'objectDetails' => 'l.objectDetails',
            'accountFirstName' => 'a.firstName',
            'accountLastName' => 'a.lastName',
            'createdAt' => 'l.createdAt',
        ];

        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('id', 'path', 'action', 'l.createdAt', 'objectId', 'objectDetails', 'a.firstName as accountFirstName', 'a.lastName as accountLastName')
            ->from(self::TABLE_NAME, 'l')
            ->leftJoin('l', self::TABLE_NAME_ACCOUNT, 'a', 'l.authUserId = a.accountId')
            ->andWhere('l.logType = :logType')
            ->setParameter('logType', 'entity')
        ;

        $gridConfiguration->addSortAndFilterToDbalQueryBuilder($queryBuilder, $fieldDatabaseMapping);

        return $queryBuilder;
    }
}
