<?php

declare(strict_types=1);

namespace App\Logger;

use App\Common\Query\GridConfiguration;
use App\Common\Query\GridResult;
use App\Logger\Query\LogQuery;
use App\Logger\Query\LogView;

readonly class LoggerFacade
{
    public function __construct(
        private LoggerService $loggerService,
        private LogQuery $logQuery,
    ) {
    }

    /** @param array<string, mixed> $objectDetails */
    public function createCustomLog(LogType $logType, string $action, string $path, string $payload, string $objectId, array $objectDetails): void
    {
        $this->loggerService->createCustomLog($logType, $action, $path, $payload, $objectId, $objectDetails);
    }

    /** @return GridResult<int, LogView> */
    public function findForGrid(GridConfiguration $gridConfiguration): GridResult
    {
        return $this->logQuery->findForGrid($gridConfiguration);
    }
}
