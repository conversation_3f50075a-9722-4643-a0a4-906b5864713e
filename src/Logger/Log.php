<?php

declare(strict_types=1);

namespace App\Logger;

use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\Index;
use Doctrine\ORM\Mapping\Table;

#[Entity]
#[Table(name: 'logger_log')]
#[Index(columns: ['path'])]
#[Index(columns: ['logType'])]
#[Index(columns: ['objectId'])]
#[Index(columns: ['createdAt'])]
#[Index(columns: ['authUserId'])]
#[Index(columns: ['correlationId', 'path'])]
class Log
{
    #[ORM\Id, ORM\GeneratedValue, ORM\Column(type: 'integer')]
    private int $id;

    #[ORM\Column(type: 'string', length: 36)]
    private string $correlationId;

    #[ORM\Column(type: 'string', enumType: LogType::class)]
    private LogType $logType;

    #[ORM\Column(type: 'string', length: 255)]
    private string $action;

    #[ORM\Column(type: 'string', length: 500)]
    private string $path;

    #[ORM\Column(type: 'string', length: 40, nullable: true)]
    private ?string $objectId;

    #[ORM\Column(type: 'string', length: 5000, nullable: true)]
    private ?string $objectDetails;

    #[ORM\Column(type: 'text')]
    private string $payload;

    #[ORM\Column(type: 'string', nullable: true)]
    private ?string $authUserId;
    #[ORM\Column(type: 'string', length: 45, nullable: true)]
    private ?string $ip;

    #[ORM\Column(type: 'string', length: 2048, nullable: true)]
    private ?string $referer;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $userAgent;

    #[ORM\Column(type: 'datetime_immutable')]
    private \DateTimeImmutable $createdAt;

    private function __construct(
        string $correlationId,
        LogType $logType,
        string $action,
        string $path,
        string $payload,
        ?string $ip,
        ?string $referer,
        ?string $userAgent,
    ) {
        $this->correlationId = $correlationId;
        $this->logType = $logType;
        $this->action = $action;
        $this->path = $path;
        $this->payload = $payload;
        $this->createdAt = new \DateTimeImmutable();
        $this->ip = $ip;
        $this->referer = $referer;
        $this->userAgent = $userAgent ? mb_substr($userAgent, 0, 255) : null;
    }

    public static function create(
        string $correlationId,
        LogType $eventLogType,
        string $action,
        string $path,
        string $payload,
        ?string $ip = null,
        ?string $referer = null,
        ?string $useAgent = null,
    ): self {
        return new self(
            $correlationId,
            $eventLogType,
            $action,
            $path,
            $payload,
            $ip,
            $referer,
            $useAgent
        );
    }

    public function setAuthUserId(?string $authUserId): void
    {
        $this->authUserId = $authUserId;
    }

    public function setObjectId(string $objectId): void
    {
        $this->objectId = $objectId;
    }

    /** @param array<string, mixed>|null $objectDetails */
    public function setObjectDetails(?array $objectDetails): void
    {
        $this->objectDetails = json_encode($objectDetails, JSON_THROW_ON_ERROR | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES, 10);
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getCorrelationId(): string
    {
        return $this->correlationId;
    }

    public function getLogType(): LogType
    {
        return $this->logType;
    }

    public function getAction(): string
    {
        return $this->action;
    }

    public function getPath(): string
    {
        return $this->path;
    }

    public function getPayload(): string
    {
        return $this->payload;
    }

    public function getCreatedAt(): \DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getAuthUserId(): ?string
    {
        return $this->authUserId;
    }

    public function getIp(): ?string
    {
        return $this->ip;
    }

    public function getReferer(): ?string
    {
        return $this->referer;
    }

    public function getUserAgent(): ?string
    {
        return $this->userAgent;
    }

    public function getObjectId(): ?string
    {
        return $this->objectId;
    }

    /** @return array<string, mixed>|null */
    public function getObjectDetails(): ?array
    {
        return json_decode((string) $this->objectDetails, true, 10, JSON_THROW_ON_ERROR);
    }
}
