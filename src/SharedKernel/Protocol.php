<?php

declare(strict_types=1);

namespace App\SharedKernel;

// Ensure ProtocolType is imported if in the same directory, otherwise use full namespace
// Assuming it's in the same namespace based on previous step

enum Protocol: string
{
    case STS = 'STS'; // Mięsaki tkanek miękkich
    case WT = 'WT'; // Nefroblastoma
    case NHLB = 'NHLB'; // Chłoniak nieziarniczy B-komórkowy
    case ALCL = 'ALCL'; // Chłoniak nieziarniczy wielkokomórkowy
    case LBL = 'LBL'; // Chłoniak nieziarniczy T/pre-B-komórkowy
    case RBL = 'RBL'; // Retinoblastoma
    case EGCT = 'EGCT'; // Guzy germinalne
    case NBL = 'NBL'; // Neuroblastoma
    case OUN = 'OUN'; // Nowotwory ośrodkowego układu nerwowego
    case HL = 'HL'; // Chłoniak Hodgkina
    case ALL = 'ALL'; // Ostra białaczka limfoblastyczna
    case AML = 'AML'; // Ostra białaczka szpikowa
    case CML = 'CML'; // Przewlekła białaczka szpikowa
    case MPAL = 'MPAL'; // Ostra białaczka o mieszanym fenotypie
    case BT = 'BT'; // Guzy kości
    case HBL = 'HBL'; // Hepatoblastoma
    case MDS = 'MDS'; // Zespoły mielodysplastyczne
    case LCH = 'LCH'; // Histiocytozy
    case INNE = 'INNE'; // Inne
    case FOLLOWUP = 'FOLLOWUP'; // Wznowa
    case TEST = 'TEST'; // Test

    /** @return array<string> */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function isEqualsTo(self $other): bool
    {
        return $this->value === $other->value;
    }

    // Updated method to include the logic directly
    public function getProtocolType(): ProtocolType
    {
        // Move the match logic here
        $isExtended = match ($this) {
            self::NHLB, self::LBL, self::ALCL, self::STS, self::WT, self::NBL, self::RBL, self::EGCT => true,
            default => false,
        };

        return $isExtended ? ProtocolType::EXTENDED : ProtocolType::BASIC;
    }

    /**
     * Returns an array of protocols matching the given type.
     *
     * @param ProtocolType $type the type of protocols to retrieve (BASIC or EXTENDED)
     *
     * @return array<Protocol> an array of Protocol enum cases
     */
    public static function getProtocolsByType(ProtocolType $type): array
    {
        return array_filter(self::cases(), fn (Protocol $protocol) => $protocol->getProtocolType() === $type);
    }
}
