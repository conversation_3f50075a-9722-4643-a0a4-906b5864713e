<?php

declare(strict_types=1);

namespace App\SharedKernel;

class FormFieldsType
{
    public const array ALLOW_ROOT = [
        'text-input-field',
        'select-input-field',
        'three-state-level',
        'fields-group',
        'number-input-field',
        'date-picker-field',
        'textarea-input-field',
        'ckeditor-input-field',
        'fields-group-table',
        'live-select-field',
        'computed-field',
        'fields-set',
    ];

    public const array ALLOW_CHILDREN = [
        'text-input-field',
        'select-input-field',
        'three-state-level',
        'fields-group',
        'number-input-field',
        'date-picker-field',
        'textarea-input-field',
        'fields-group-table',
        'ckeditor-input-field',
        'live-select-field',
        'computed-field',
        'fields-set',
    ];
}
