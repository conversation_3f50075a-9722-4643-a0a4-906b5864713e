<?php

declare(strict_types=1);

namespace App\SharedKernel;

enum DictionaryType: string
{
    case ICD10 = 'ICD10';
    case ICCC = 'ICCC';
    case ICD_DEATHS = 'ICD_DEATHS';
    case MOLECULAR_MARKERS = 'MOLECULAR_MARKERS';
    case CHROMOSOMAL_ABERRATIONS = 'CHROMOSOMAL_ABERRATIONS';
    case WT_DIAGNOSIS_01 = 'WT-DIAGNOSIS-01';
    case WT_PROTOCOL = 'WT-PROTOCOL';
    case STS_PROTOCOL = 'STS-PROTOCOL';
    case STS_DIAGNOSIS_01 = 'STS-DIAGNOSIS-01';
    case STS_DIAGNOSIS_02 = 'STS-DIAGNOSIS-02';
    case STS_DIAGNOSIS_03 = 'STS-DIAGNOSIS-03';
    case STS_THERAPY_ARM = 'STS-THERAPY-ARM';
    case STS_CYCLE = 'STS-CYCLE';
    case STS_GENFUZ = 'STS-GENFUZ';
    case STS_MARKER = 'STS-MARKER';
    case NHLB_MARKER = 'NHLB-MARKER';
    case NHLB_THERAPY_ARMS = 'NHLB-THERAPY-ARMS';
    case NHLB_CYCLE = 'NHLB-CYCLE';
    case NHLB_PROTOCOL = 'NHLB-PROTOCOL';
    case NHLB_DIAGNOSIS_01 = 'NHLB-DIAGNOSIS-01';
    case LBL_MARKER = 'LBL-MARKER';
    case LBL_PROTOCOL = 'LBL-PROTOCOL';
    case LBL_SECOND_LINE = 'LBL-SECOND-LINE';
    case LBL_THERAPY_ARM = 'LBL-THERAPY-ARM';
    case ALCL_PROTOCOL = 'ALCL-PROTOCOL';
    case ALCL_SECOND_LINE = 'ALCL-SECOND-LINE';
    case RBL_PROTOCOL = 'RBL-PROTOCOL';
    case NBL_DIAGNOSIS_01 = 'NBL-DIAGNOSIS-01';
    case NBL_PROTOCOL1 = 'NBL-PROTOCOL';
    case NBL_CHEM_01 = 'NBL-CHEM-01';
    case EGCT_PROTOCOL = 'EGCT-PROTOCOL';

    case OUN_PROTOCOL = 'OUN-PROTOCOL';
    case HL_PROTOCOL = 'HL-PROTOCOL';
    case ALL_PROTOCOL = 'ALL-PROTOCOL';
    case AML_PROTOCOL = 'AML-PROTOCOL';
    case CML_PROTOCOL = 'CML-PROTOCOL';
    case MPAL_PROTOCOL = 'MPAL-PROTOCOL';
    case BT_PROTOCOL = 'BT-PROTOCOL';
    case HBL_PROTOCOL = 'HBL-PROTOCOL';
    case MDS_PROTOCOL = 'MDS-PROTOCOL';
    case LCH_PROTOCOL = 'LCH-PROTOCOL';
    case INNE_PROTOCOL = 'INNE-PROTOCOL';

    public static function isValid(string $value): bool
    {
        static $cases = null;

        if ($cases === null) {
            $cases = array_map(static function ($case) {
                return $case->value;
            }, self::cases());
        }

        return in_array($value, $cases, true);
    }

    /** @return array<string> */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }
}
