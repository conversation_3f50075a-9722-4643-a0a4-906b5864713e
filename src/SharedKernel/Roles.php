<?php

declare(strict_types=1);

namespace App\SharedKernel;

enum Roles: string
{
    case ROLE_CENTRAL_ADMINISTRATOR = 'ROLE_CENTRAL_ADMINISTRATOR';
    case ROLE_REPORTER_PHYSICIAN = 'ROLE_REPORTER_PHYSICIAN';
    case ROLE_COORDINATOR = 'ROLE_COORDINATOR';
    case ROLE_DATA_ADMINISTRATOR = 'ROLE_DATA_ADMINISTRATOR';

    /** @return array<string> */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }
}
