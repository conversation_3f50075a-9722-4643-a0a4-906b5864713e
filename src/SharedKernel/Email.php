<?php

declare(strict_types=1);

namespace App\SharedKernel;

use App\Common\ValueObject;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Embeddable]
class Email implements ValueObject
{
    private string $email;

    private function __construct(string $email)
    {
        $this->validateEmail($email);
        $this->email = mb_strtolower($email);
    }

    public static function create(string $email): self
    {
        return new self($email);
    }

    private function validateEmail(string $email): void
    {
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new \InvalidArgumentException(sprintf('The provided email "%s" is not a valid email address.', $email));
        }
    }

    public function equals(Email $email): bool
    {
        return $this->valueString() === $email->valueString();
    }

    public function __toString(): string
    {
        return $this->email;
    }

    public function valueString(): string
    {
        return $this->email;
    }

    public function jsonSerialize(): mixed
    {
        return $this->email;
    }
}
