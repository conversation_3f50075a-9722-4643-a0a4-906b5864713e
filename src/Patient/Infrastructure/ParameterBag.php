<?php

declare(strict_types=1);

namespace App\Patient\Infrastructure;

use App\Patient\Application\Service\ParameterBagInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface as SymfonyParameterBagInterface;

readonly class ParameterBag implements ParameterBagInterface
{
    public function __construct(
        private SymfonyParameterBagInterface $parameterBag)
    {
    }

    public function getPatientRodoConsentUploadDir(string $patientId): string
    {
        return $this->parameterBag->get('kernel.project_dir')."/upload/patient/{$patientId}/rodo-consents";
    }

    public function getPatientAttachmentUploadDir(string $patientId): string
    {
        // Użyjemy innej ścieżki dla załączników
        return $this->parameterBag->get('kernel.project_dir')."/upload/patient/{$patientId}/attachments";
    }
}
