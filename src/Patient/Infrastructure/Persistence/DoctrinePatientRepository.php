<?php

declare(strict_types=1);

namespace App\Patient\Infrastructure\Persistence;

use App\Common\Uuid;
use App\Patient\Domain\Exception\PatientNotFoundException;
use App\Patient\Domain\PassportNumber;
use App\Patient\Domain\Patient;
use App\Patient\Domain\PatientPublicId;
use App\Patient\Domain\PatientRepositoryInterface;
use App\Patient\Domain\Pesel;
use Doctrine\ORM\EntityManagerInterface;

readonly class DoctrinePatientRepository implements PatientRepositoryInterface
{
    public function __construct(private EntityManagerInterface $em)
    {
    }

    public function add(Patient $patient): void
    {
        $this->em->persist($patient);
        $this->em->flush();
    }

    public function update(Patient $patient): void
    {
        $this->em->flush();
    }

    public function getByPatientId(Uuid $patientId): Patient
    {
        $patient = $this->em->find(Patient::class, $patientId);

        if (!$patient instanceof Patient) {
            throw PatientNotFoundException::withId($patientId->toString());
        }

        return $patient;
    }

    public function patientPublicIdExists(PatientPublicId $patientPublicId): bool
    {
        $qb = $this->em->createQueryBuilder();

        $qb->select('p')
            ->from(Patient::class, 'p')
            ->where('p.patientPublicId.patientPublicId = :patientPublicId')
            ->setParameter('patientPublicId', $patientPublicId->patientPublicId());

        $query = $qb->getQuery();

        $result = $query->getOneOrNullResult();

        return $result !== null;
    }

    public function findByPesel(Pesel $pesel, ?Uuid $excludePatientId = null): ?Patient
    {
        $qb = $this->em->createQueryBuilder();

        $qb->select('p')
            ->from(Patient::class, 'p')
            ->andWhere('p.pesel.pesel = :pesel')
            ->setParameter('pesel', $pesel->pesel());

        if ($excludePatientId !== null) {
            $qb->andWhere('p.patientId != :patientId')
                ->setParameter('patientId', $excludePatientId->valueString());
        }

        return $qb->getQuery()->getOneOrNullResult();
    }

    public function findByPassportNumber(PassportNumber $passportNumber, ?Uuid $excludePatientId = null): ?Patient
    {
        $qb = $this->em->createQueryBuilder();

        $qb->select('p')
            ->from(Patient::class, 'p')
            ->andWhere('p.passportNumber.passportNumber = :passportNumber')
            ->setParameter('passportNumber', $passportNumber->passportNumber());

        if ($excludePatientId !== null) {
            $qb->andWhere('p.patientId != :patientId')
                ->setParameter('patientId', $excludePatientId->valueString());
        }

        return $qb->getQuery()->getOneOrNullResult();
    }
}
