<?php

declare(strict_types=1);

namespace App\Patient\Infrastructure\Persistence;

use App\Common\Uuid;
use App\Patient\Domain\Exception\PatientCorrectionFlagNotFoundException;
use App\Patient\Domain\PatientCorrectionFlag;
use App\Patient\Domain\PatientCorrectionFlagRepositoryInterface;
use Doctrine\ORM\EntityManagerInterface;

class PatientCorrectionFlagRepository implements PatientCorrectionFlagRepositoryInterface
{
    private EntityManagerInterface $em;

    public function __construct(EntityManagerInterface $em)
    {
        $this->em = $em;
    }

    public function save(PatientCorrectionFlag $flag): void
    {
        $this->em->persist($flag);
        $this->em->flush();
    }

    public function findById(Uuid $id): ?PatientCorrectionFlag
    {
        return $this->em->find(PatientCorrectionFlag::class, $id);
    }

    public function getById(Uuid $id): PatientCorrectionFlag
    {
        $flag = $this->findById($id);
        if ($flag === null) {
            throw PatientCorrectionFlagNotFoundException::withId($id->toString());
        }

        return $flag;
    }
}
