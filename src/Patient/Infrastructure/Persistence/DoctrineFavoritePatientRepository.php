<?php

declare(strict_types=1);

namespace App\Patient\Infrastructure\Persistence;

use App\Common\Uuid;
use App\Patient\Domain\FavoritePatient;
use App\Patient\Domain\FavoritePatientRepositoryInterface;
use Doctrine\ORM\EntityManagerInterface;

class DoctrineFavoritePatientRepository implements FavoritePatientRepositoryInterface
{
    private EntityManagerInterface $em;

    public function __construct(EntityManagerInterface $em)
    {
        $this->em = $em;
    }

    public function add(FavoritePatient $favoritePatient): void
    {
        $this->em->persist($favoritePatient);
        $this->em->flush();
    }

    public function remove(FavoritePatient $favoritePatient): void
    {
        $this->em->remove($favoritePatient);
        $this->em->flush();
    }

    public function findByAuthUserAndPatient(Uuid $authUserId, Uuid $patientId): ?FavoritePatient
    {
        $qb = $this->em->createQueryBuilder();
        $qb->select('fp')
           ->from(FavoritePatient::class, 'fp')
           ->where('fp.authUserId = :authUserId')
           ->andWhere('fp.patientId = :patientId')
           ->setParameter('authUserId', $authUserId->toString())
           ->setParameter('patientId', $patientId->toString())
           ->setMaxResults(1);

        return $qb->getQuery()->getOneOrNullResult();
    }
}
