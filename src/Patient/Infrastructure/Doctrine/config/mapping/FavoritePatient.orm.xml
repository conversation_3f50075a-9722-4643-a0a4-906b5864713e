<?xml version="1.0" encoding="UTF-8"?>
<doctrine-mapping xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping
                          https://www.doctrine-project.org/schemas/orm/doctrine-mapping.xsd">

    <entity name="App\Patient\Domain\FavoritePatient" table="patient_favorite_patients">
        <id name="id" type="integer" column="id">
            <generator strategy="AUTO"/>
        </id>

        <field name="authUserId" type="uuid_symfony"/>
        <field name="patientId" type="uuid_symfony"/>
        <field name="createdAt" type="datetime_immutable"/>

        <unique-constraints>
            <unique-constraint columns="authUserId,patientId" name="unique_auth_user_patient"/>
        </unique-constraints>

        <indexes>
            <index name="idx_createdAt" columns="createdAt"/>
        </indexes>
    </entity>

</doctrine-mapping>
