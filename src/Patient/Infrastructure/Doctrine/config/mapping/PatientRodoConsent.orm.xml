<?xml version="1.0" encoding="UTF-8"?>
<doctrine-mapping xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping
                  http://doctrine-project.org/schemas/orm/doctrine-mapping.xsd">

    <entity name="App\Patient\Domain\PatientRodoConsent" table="patient_patient_rodo_consent">
        <id name="patientRodoConsentId" type="uuid_symfony">
            <generator strategy="NONE"/>
        </id>

        <field name="fileName" length="255"/>
        <field name="extension" length="4"/>
        <field name="createdAt" type="datetime_immutable"/>

        <many-to-one field="patient" target-entity="Patient" inversed-by="rodoConsent">
            <join-column name="patientId" referenced-column-name="patientId" nullable="false"/>
        </many-to-one>
    </entity>

</doctrine-mapping>
