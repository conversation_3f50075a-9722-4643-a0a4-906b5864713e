<?xml version="1.0" encoding="UTF-8"?>
<doctrine-mapping xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping
                  https://www.doctrine-project.org/schemas/orm/doctrine-mapping.xsd">

    <entity name="App\Patient\Domain\Patient" table="patient_patient">

        <id name="patientId" type="uuid_symfony">
            <generator strategy="NONE"/>
        </id>

        <unique-constraints>
            <unique-constraint columns="patientPublicId"/>
            <unique-constraint columns="pesel_pesel"/>
            <unique-constraint columns="passportNumber_passportNumber"/>
        </unique-constraints>

        <embedded name="patientPublicId" class="App\Patient\Domain\PatientPublicId" use-column-prefix="false"/>
        <field name="firstName" length="50" />
        <field name="lastName" length="50" />
        <field name="citizenship" enum-type="App\Patient\Domain\Citizenship" />
        <field name="isRegisteredAddressSameAsResidence" type="boolean" />
        <field name="isCorrespondenceAddressSameAsResidence" type="boolean" />
        <embedded name="residenceAddress" class="App\Patient\Domain\Address"/>
        <embedded name="registeredAddress" class="App\Patient\Domain\Address"/>
        <embedded name="correspondenceAddress" class="App\Patient\Domain\Address"/>
        <field name="email" length="100" nullable="true" />
        <field name="contactNumber" length="20" nullable="true" />
        <field name="isPatientIdentified" type="boolean" />
        <embedded name="pesel" class="App\Patient\Domain\Pesel"/>
        <embedded name="passportNumber" class="App\Patient\Domain\PassportNumber"/>
        <embedded name="legalRepresentativePesel" class="App\Patient\Domain\Pesel"/>
        <embedded name="legalRepresentativePassportNumber" class="App\Patient\Domain\PassportNumber"/>
        <field name="gender" enum-type="App\Patient\Domain\Gender"/>
        <embedded name="birthDate" class="App\Patient\Domain\BirthDate" use-column-prefix="false"/>
        <field name="isActive" type="boolean" />
        <field name="deactivationReason" length="1000" nullable="true" />
        <field name="isDeceased" type="boolean" />
        <field name="createdAt" type="datetime_immutable"/>
        <field name="updatedAt" type="datetime_immutable" nullable="true" />
        <field name="followUpDateOfCompletionForm" type="date_immutable" nullable="true" />

        <field name="hospitals" type="json"/>
        <one-to-many field="protocols" target-entity="App\Patient\Domain\PatientProtocol" mapped-by="patient" fetch="EAGER"
                     orphan-removal="true">
            <cascade>
                <cascade-persist/>
                <cascade-remove/>
            </cascade>
        </one-to-many>

        <one-to-many field="rodoConsents" target-entity="App\Patient\Domain\PatientRodoConsent" mapped-by="patient" fetch="EAGER"
                     orphan-removal="true">
            <cascade>
                <cascade-persist/>
                <cascade-remove/>
            </cascade>
        </one-to-many>

        <one-to-many field="attachments" target-entity="App\Patient\Domain\PatientAttachment" mapped-by="patient" fetch="EAGER"
                     orphan-removal="true" index-by="patientAttachmentId">
            <cascade>
                <cascade-persist/>
                <cascade-remove/>
            </cascade>
        </one-to-many>
    </entity>
</doctrine-mapping>
