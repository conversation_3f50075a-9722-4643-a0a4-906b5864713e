<?xml version="1.0" encoding="UTF-8"?>
<doctrine-mapping xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping
                          https://www.doctrine-project.org/schemas/orm/doctrine-mapping.xsd">

    <entity name="App\Patient\Domain\PatientCorrectionFlag"
            table="patient_correction_flags"
            repository-class="App\Patient\Infrastructure\Persistence\PatientCorrectionFlagRepository">

        <id name="id" type="uuid_symfony" column="id"/>

        <field name="patientId" type="uuid_symfony"/>
        <field name="hospitalId" type="uuid_symfony"/>
        <field name="comment" length="1000"/>
        <field name="createdAt" type="datetime_immutable"/>
        <field name="resolvedById" type="uuid_symfony" nullable="true"/>
        <field name="resolvedAt" type="datetime_immutable" nullable="true"/>
        <field name="version" type="integer" version="true"/>

        <indexes>
            <index columns="patientId"/>
            <index columns="hospitalId"/>
            <index columns="resolvedAt"/>
        </indexes>
    </entity>

</doctrine-mapping> 