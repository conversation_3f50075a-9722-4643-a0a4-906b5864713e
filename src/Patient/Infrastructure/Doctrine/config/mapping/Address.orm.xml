<?xml version="1.0" encoding="UTF-8"?>
<doctrine-mapping xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping
                  https://www.doctrine-project.org/schemas/orm/doctrine-mapping.xsd">

    <embeddable name="App\Patient\Domain\Address">
        <field name="streetWithNumber" length="100" nullable="true" />
        <field name="city" length="40" nullable="true"/>
        <field name="postCode" length="6" nullable="true"/>
    </embeddable>

</doctrine-mapping>
