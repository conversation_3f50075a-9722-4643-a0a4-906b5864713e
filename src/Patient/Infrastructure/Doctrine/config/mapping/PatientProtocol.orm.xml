<?xml version="1.0" encoding="UTF-8"?>
<doctrine-mapping xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping
                  https://www.doctrine-project.org/schemas/orm/doctrine-mapping.xsd">

    <entity name="App\Patient\Domain\PatientProtocol" table="patient_patient_protocol">
        <id name="patientProtocolId" type="uuid_symfony">
            <generator strategy="NONE"/>
        </id>

        <many-to-one field="patient" target-entity="App\Patient\Domain\Patient" inversed-by="patientProtocols">
            <join-columns>
                <join-column name="patientId" referenced-column-name="patientId" nullable="false"/>
            </join-columns>
        </many-to-one>
        <field name="protocol" length="70"/>
        <field name="isActive" type="boolean" />
        <field name="treatmentStartDate" type="date_immutable" nullable="true" />
        <field name="icd10Code" length="50" nullable="true" />
    </entity>

</doctrine-mapping>
