<?xml version="1.0" encoding="UTF-8"?>
<doctrine-mapping xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping
                  https://www.doctrine-project.org/schemas/orm/doctrine-mapping.xsd">

    <entity name="App\Patient\Domain\PatientAttachment" table="patient_patient_attachment">
        <id name="patientAttachmentId" type="uuid_symfony">
            <generator strategy="NONE"/>
        </id>

        <field name="type" enum-type="App\Patient\Domain\PatientAttachmentType"/>
        <field name="fileName" length="255"/>
        <field name="extension" length="10"/>
        <field name="createdAt" type="datetime_immutable"/>

        <many-to-one field="patient" target-entity="App\Patient\Domain\Patient" inversed-by="attachments">
            <join-column name="patientId" referenced-column-name="patientId" nullable="false"/>
        </many-to-one>
    </entity>

</doctrine-mapping> 