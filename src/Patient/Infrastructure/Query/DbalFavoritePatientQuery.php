<?php

declare(strict_types=1);

namespace App\Patient\Infrastructure\Query;

use App\Common\Query\GridConfiguration;
use App\Common\Query\GridResult;
use App\Patient\Application\Query\FavoritePatientGridView;
use App\Patient\Application\Query\FavoritePatientQueryInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Query\QueryBuilder;

final class DbalFavoritePatientQuery implements FavoritePatientQueryInterface
{
    private const string TABLE_NAME = 'patient_favorite_patients';

    public function __construct(
        private readonly Connection $connection,
    ) {
    }

    public function isPatientFavorite(string $authUserId, string $patientId): bool
    {
        $queryBuilder = $this->connection->createQueryBuilder();

        $queryBuilder
            ->select('1')
            ->from(self::TABLE_NAME, 'fp')
            ->andWhere('fp.authUserId = :authUserId')
            ->andWhere('fp.patientId = :patientId')
            ->setParameter('authUserId', $authUserId)
            ->setParameter('patientId', $patientId);

        $result = $this->connection->fetchOne($queryBuilder->getSQL(), $queryBuilder->getParameters());

        return $result !== false;
    }

    /**
     * @param array<string>|null $onlyAllowHospitals
     * @param array<string>|null $onlyAllowProtocols
     */
    public function findForGrid(GridConfiguration $gridConfiguration, string $authUserId, ?array $onlyAllowHospitals, ?array $onlyAllowProtocols): GridResult
    {
        $baseQueryBuilder = $this->prepareQueryForGrid(
            $gridConfiguration,
            $authUserId,
            $onlyAllowHospitals,
            $onlyAllowProtocols
        );

        // Count total items for pagination
        $queryBuilderTotalItems = clone $baseQueryBuilder;
        $queryBuilderTotalItems->resetOrderBy()
            ->select('DISTINCT fp.patientId');
        $totalItems = count($this->connection->fetchFirstColumn($queryBuilderTotalItems->getSQL(), $queryBuilderTotalItems->getParameters()));

        // Add pagination
        $queryBuilder = clone $baseQueryBuilder;
        $queryBuilder->setFirstResult(($gridConfiguration->page() - 1) * $gridConfiguration->itemsPerPage())
            ->setMaxResults($gridConfiguration->itemsPerPage());

        $data = $this->connection->fetchAllAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());
        $collection = new ArrayCollection();

        foreach ($data as $row) {
            $collection->set($row['patientId'], FavoritePatientGridView::deserialize($row));
        }

        return new GridResult($collection, $totalItems, $gridConfiguration->page(), $gridConfiguration->itemsPerPage());
    }

    /**
     * @param array<string>|null $onlyAllowHospitals
     * @param array<string>|null $onlyAllowProtocols
     */
    private function prepareQueryForGrid(GridConfiguration $gridConfiguration, string $authUserId, ?array $onlyAllowHospitals, ?array $onlyAllowProtocols): QueryBuilder
    {
        $queryBuilder = $this->connection->createQueryBuilder();

        $fieldDatabaseMapping = [
            'patientId' => 'fp.patientId',
            'patientPublicId' => 'p.patientPublicId',
            'firstName' => 'p.firstName',
            'lastName' => 'p.lastName',
        ];

        $queryBuilder
            ->select('fp.patientId', 'p.patientPublicId', 'p.firstName', 'p.lastName')
            ->from(self::TABLE_NAME, 'fp')
            ->innerJoin('fp', 'patient_patient', 'p', 'fp.patientId = p.patientId')
            ->leftJoin('p', DbalPatientQuery::TABLE_NAME_PATIENT_PROTOCOL, 'pp', 'p.patientId = pp.patientId')
            ->andWhere('fp.authUserId = :authUserId')
            ->setParameter('authUserId', $authUserId)
            ->groupBy('fp.patientId', 'p.patientPublicId', 'p.firstName', 'p.lastName');

        // IMPORTANT: This is a security filter
        DbalPatientQuery::permissionFilterByHospitalsAndProtocols($this->connection, $queryBuilder, $onlyAllowHospitals, $onlyAllowProtocols);

        $gridConfiguration->addSortAndFilterToDbalQueryBuilder($queryBuilder, $fieldDatabaseMapping);

        if (count($gridConfiguration->sorts()) === 0) {
            $queryBuilder->addOrderBy('fp.createdAt', 'DESC');
        }

        return $queryBuilder;
    }
}
