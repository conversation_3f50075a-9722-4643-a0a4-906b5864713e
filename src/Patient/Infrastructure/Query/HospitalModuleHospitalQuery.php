<?php

declare(strict_types=1);

namespace App\Patient\Infrastructure\Query;

use App\Hospital\Application\HospitalFacade;
use App\Patient\Application\Query\HospitalQueryInterface;
use App\Patient\Application\Query\HospitalSelectView;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;

readonly class HospitalModuleHospitalQuery implements HospitalQueryInterface
{
    public function __construct(private HospitalFacade $hospitalFacade)
    {
    }

    /**
     * @param array<string> $hospitalIds
     *
     * @return Collection<string, HospitalSelectView>
     */
    public function findAllActiveHospitalSelect(array $hospitalIds = []): Collection
    {
        $activeHospitals = $this->hospitalFacade->findAllActiveHospitalSelect($hospitalIds);
        $hospitalViews = new ArrayCollection();

        foreach ($activeHospitals as $hospital) {
            $hospitalView = new HospitalSelectView(
                $hospital->hospitalId(),
                $hospital->shortName(),
                $hospital->fullName()
            );

            $hospitalViews->set($hospital->hospitalId(), $hospitalView);
        }

        return $hospitalViews;
    }
}
