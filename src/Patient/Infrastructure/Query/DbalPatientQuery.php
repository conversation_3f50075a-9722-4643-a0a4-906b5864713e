<?php

declare(strict_types=1);

namespace App\Patient\Infrastructure\Query;

use App\Common\Query\GridConfiguration;
use App\Common\Query\GridResult;
use App\Patient\Application\Query\PatientGridView;
use App\Patient\Application\Query\PatientQueryInterface;
use App\Patient\Application\Query\PatientView;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Query\QueryBuilder;

class DbalPatientQuery implements PatientQueryInterface
{
    private const string TABLE_NAME = 'patient_patient';
    private const string TABLE_NAME_PATIENT_RODO_CONSENT = 'patient_patient_rodo_consent';
    public const string TABLE_NAME_PATIENT_PROTOCOL = 'patient_patient_protocol';
    private const string TABLE_NAME_PATIENT_ATTACHMENT = 'patient_patient_attachment';

    public function __construct(
        private Connection $connection,
    ) {
    }

    public function findByPatientId(string $patientId): ?PatientView
    {
        $queryBuilder = $this->connection->createQueryBuilder();

        $queryBuilder
            ->select('p.*')
            ->from(self::TABLE_NAME, 'p')
            ->where('p.patientId = :patientId')
            ->setParameter('patientId', $patientId);

        $data = $this->connection->fetchAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        if (false === $data) {
            return null;
        }

        return $this->preparePatientData($data);
    }

    public function findAll(?\DateTimeImmutable $changedSince = null): array
    {
        $queryBuilder = $this->connection->createQueryBuilder();

        $queryBuilder
            ->select('p.*')
            ->from(self::TABLE_NAME, 'p');

        if ($changedSince !== null) {
            $queryBuilder
                ->andWhere('p.createdAt >= :changedSince OR p.updatedAt >= :changedSince')
                ->setParameter('changedSince', $changedSince->format('Y-m-d H:i:s'));
        }

        $data = $this->connection->fetchAllAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        $patients = [];
        foreach ($data as $patientData) {
            $patients[] = $this->preparePatientData($patientData);
        }

        return $patients;
    }

    public function findByPatientProtocolId(string $patientProtocolId): ?PatientView
    {
        $patientId = $this->findPatientIdByPatientProtocolId($patientProtocolId);

        if ($patientId === null) {
            return null;
        }

        return $this->findByPatientId($patientId);
    }

    public function hospitalIsUsed(string $hospitalId): bool
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('COUNT(*)')
            ->from(self::TABLE_NAME, 'p')
            ->andWhere('JSON_CONTAINS(p.hospitals, :hospitalId)')
            ->setParameter('hospitalId', json_encode([$hospitalId]));

        $count = (int) $this->connection->fetchOne($queryBuilder->getSQL(), $queryBuilder->getParameters());

        return $count > 0;
    }

    /**
     * @param ?array<string> $onlyAllowHospitals
     * @param ?array<string> $onlyAllowProtocols
     *
     * @return GridResult<string, PatientView>
     */
    public function findForGrid(GridConfiguration $gridConfiguration, ?array $onlyAllowHospitals, ?array $onlyAllowProtocols): GridResult
    {
        $baseQueryBuilder = $this->prepareQueryForGrid($gridConfiguration, $onlyAllowHospitals, $onlyAllowProtocols);

        // Count total items for pagination
        $queryBuilderTotalItems = clone $baseQueryBuilder;
        $queryBuilderTotalItems->resetOrderBy()
            ->select('DISTINCT p.patientId');
        $totalItems = count($this->connection->fetchFirstColumn($queryBuilderTotalItems->getSQL(), $queryBuilderTotalItems->getParameters()));

        // Add pagination
        $queryBuilder = clone $baseQueryBuilder;
        $queryBuilder->setFirstResult(($gridConfiguration->page() - 1) * $gridConfiguration->itemsPerPage())
            ->setMaxResults($gridConfiguration->itemsPerPage());

        $data = $this->connection->fetchAllAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());
        $collection = new ArrayCollection();

        foreach ($data as $row) {
            $collection->set($row['patientId'], PatientGridView::deserialize($row));
        }

        return new GridResult($collection,
            $totalItems,
            $gridConfiguration->page(),
            $gridConfiguration->itemsPerPage()
        );
    }

    /** @param array<string, mixed> $patientData */
    private function preparePatientData(array $patientData): PatientView
    {
        // Prepare the data for the PatientView
        $patientData['residenceAddress'] = [
            'streetWithNumber' => $patientData['residenceAddress_streetWithNumber'],
            'city' => $patientData['residenceAddress_city'],
            'postCode' => $patientData['residenceAddress_postCode'],
        ];

        $patientData['registeredAddress'] = [
            'streetWithNumber' => $patientData['registeredAddress_streetWithNumber'],
            'city' => $patientData['registeredAddress_city'],
            'postCode' => $patientData['registeredAddress_postCode'],
        ];

        $patientData['correspondenceAddress'] = [
            'streetWithNumber' => $patientData['correspondenceAddress_streetWithNumber'],
            'city' => $patientData['correspondenceAddress_city'],
            'postCode' => $patientData['correspondenceAddress_postCode'],
        ];

        // Query for patient_patient_rodo_consent
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('prc.*')
            ->from(self::TABLE_NAME_PATIENT_RODO_CONSENT, 'prc')
            ->where('prc.patientId = :patientId')
            ->setParameter('patientId', $patientData['patientId']);

        $rodoConsents = $this->connection->fetchAllAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        $patientData['rodoConsents'] = [];
        foreach ($rodoConsents as $rodoConsent) {
            $patientData['rodoConsents'][] = [
                'patientRodoConsentId' => $rodoConsent['patientRodoConsentId'],
                'fileName' => $rodoConsent['fileName'],
                'extension' => $rodoConsent['extension'],
                'createdAt' => $rodoConsent['createdAt'],
            ];
        }

        // Query for patient_patient_protocol
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('pp.*')
            ->from(self::TABLE_NAME_PATIENT_PROTOCOL, 'pp')
            ->where('pp.patientId = :patientId')
            ->setParameter('patientId', $patientData['patientId']);

        $protocols = $this->connection->fetchAllAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        $patientData['protocols'] = [];
        foreach ($protocols as $protocol) {
            $patientData['protocols'][] = [
                'patientProtocolId' => $protocol['patientProtocolId'],
                'protocol' => $protocol['protocol'],
                'isActive' => $protocol['isActive'],
                'icd10Code' => $protocol['icd10Code'],
                'treatmentStartDate' => $protocol['treatmentStartDate'],
            ];
        }

        // Query for patient_patient_attachment
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('pa.*')
            ->from(self::TABLE_NAME_PATIENT_ATTACHMENT, 'pa')
            ->where('pa.patientId = :patientId')
            ->setParameter('patientId', $patientData['patientId']);

        $attachments = $this->connection->fetchAllAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        $patientData['attachments'] = [];
        foreach ($attachments as $attachment) {
            $patientData['attachments'][] = [
                'patientAttachmentId' => $attachment['patientAttachmentId'],
                'type' => $attachment['type'],
                'fileName' => $attachment['fileName'],
                'extension' => $attachment['extension'],
                'createdAt' => $attachment['createdAt'],
            ];
        }

        return PatientView::deserialize($patientData);
    }

    /**
     * @param ?array<string> $onlyAllowHospitals
     * @param ?array<string> $onlyAllowProtocols
     */
    private function prepareQueryForGrid(GridConfiguration $gridConfiguration, ?array $onlyAllowHospitals, ?array $onlyAllowProtocols): QueryBuilder
    {
        $queryBuilder = $this->connection->createQueryBuilder();

        $fieldDatabaseMapping = [
            'patientId' => 'p.patientId',
            'patientPublicId' => 'p.patientPublicId',
            'firstName' => 'p.firstName',
            'lastName' => 'p.lastName',
            'gender' => 'p.gender',
            'birthDate' => 'p.birthDate',
            'isActive' => 'p.isActive',
            'isDeceased' => 'p.isDeceased',
            'protocols' => 'pp.protocol',
            'hospitals' => 'p.hospitals',
            'followUpDateOfCompletionForm' => 'p.followUpDateOfCompletionForm',
        ];

        $queryBuilder
            ->select('p.patientId', 'p.patientPublicId', 'p.firstName', 'p.lastName', 'p.gender', 'p.birthDate', 'p.hospitals', 'p.isActive', 'p.isDeceased', 'p.followUpDateOfCompletionForm')
            ->from(self::TABLE_NAME, 'p')
            ->leftJoin('p', self::TABLE_NAME_PATIENT_PROTOCOL, 'pp', 'p.patientId = pp.patientId')
            ->andWhere('pp.isActive = 1')
            ->groupBy('p.patientId');

        // IMPORTANT: This is a security filter
        self::permissionFilterByHospitalsAndProtocols($this->connection, $queryBuilder, $onlyAllowHospitals, $onlyAllowProtocols);

        $protocolQueryBuilder = $this->connection->createQueryBuilder();
        $protocolQueryBuilder
            ->select('JSON_ARRAYAGG(pp.protocol) as protocols')
            ->from(self::TABLE_NAME_PATIENT_PROTOCOL, 'pp')
            ->andWhere('pp.patientId = p.patientId')
            ->andWhere('pp.isActive = 1')
            ->groupBy('pp.patientId');

        // Combine the two queries
        $queryBuilder->addSelect('('.$protocolQueryBuilder->getSQL().') as protocols');

        $gridConfiguration->addSortAndFilterToDbalQueryBuilder($queryBuilder, $fieldDatabaseMapping);

        if (count($gridConfiguration->sorts()) === 0) {
            $queryBuilder->addOrderBy('p.createdAt', 'DESC');
        }

        return $queryBuilder;
    }

    private function findPatientIdByPatientProtocolId(string $patientProtocolId): ?string
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('patientId')
            ->from(self::TABLE_NAME_PATIENT_PROTOCOL)
            ->where('patientProtocolId = :patientProtocolId')
            ->setParameter('patientProtocolId', $patientProtocolId);

        $patientId = $this->connection->fetchOne($queryBuilder->getSQL(), $queryBuilder->getParameters());

        if (false === $patientId) {
            return null;
        }

        return $patientId;
    }

    /**
     * @param ?array<string> $onlyAllowHospitals
     * @param ?array<string> $onlyAllowProtocols
     */
    public static function permissionFilterByHospitalsAndProtocols(
        Connection $connection,
        QueryBuilder $queryBuilder,
        ?array $onlyAllowHospitals,
        ?array $onlyAllowProtocols): void
    {
        if (!empty($onlyAllowHospitals)) {
            $orX = $queryBuilder->expr()->orX();
            foreach ($onlyAllowHospitals as $index => $hospital) {
                $orX->add($queryBuilder->expr()->eq(sprintf('JSON_CONTAINS(p.hospitals, :hospital%d)', $index), 1));
                $queryBuilder->setParameter(sprintf('hospital%d', $index), json_encode([$hospital]));
            }
            $queryBuilder->andWhere($orX);
        }

        if (!empty($onlyAllowProtocols)) {
            $protocolIds = array_map([$connection, 'quote'], $onlyAllowProtocols);
            $queryBuilder
                ->andWhere($queryBuilder->expr()->in('pp.protocol', $protocolIds));
        }

        // Show only active patients with active protocols (works for roles other than CENTRAL_ADMIN)
        if (!empty($onlyAllowHospitals) || !empty($onlyAllowProtocols)) {
            $activeProtocolSubquery = $connection->createQueryBuilder();
            $activeProtocolSubquery
                ->select('1')
                ->from(self::TABLE_NAME_PATIENT_PROTOCOL, 'app')
                ->andWhere('app.patientId = p.patientId')
                ->andWhere('app.protocol != "FOLLOWUP"')
                ->andWhere('app.isActive = 1');

            $queryBuilder->andWhere(sprintf('EXISTS (%s)', $activeProtocolSubquery->getSQL()));
            $queryBuilder->andWhere('p.isActive = 1');
        }
    }
}
