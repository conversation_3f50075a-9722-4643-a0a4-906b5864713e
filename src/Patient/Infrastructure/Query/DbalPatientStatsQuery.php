<?php

declare(strict_types=1);

namespace App\Patient\Infrastructure\Query;

use App\Patient\Application\Query\Stats\PatientsCountByGenderView;
use App\Patient\Application\Query\Stats\PatientsCountByHospitalView;
use App\Patient\Application\Query\Stats\PatientsCountByProtocolTypeView;
use App\Patient\Application\Query\Stats\PatientsCountByProtocolView;
use App\Patient\Application\Query\Stats\PatientStatsQueryInterface;
use App\SharedKernel\Protocol;
use App\SharedKernel\ProtocolType;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Connection;

class DbalPatientStatsQuery implements PatientStatsQueryInterface
{
    private const string TABLE_NAME = 'patient_patient';
    private const string TABLE_NAME_PATIENT_PROTOCOL = 'patient_patient_protocol';

    public function __construct(
        private readonly Connection $connection,
    ) {
    }

    /** @return Collection<int, int> */
    public function getUniqueTreatmentStartYears(): Collection
    {
        $queryBuilder = $this->connection->createQueryBuilder();

        $queryBuilder
            ->select('DISTINCT YEAR(pp.treatmentStartDate) as year')
            ->from(self::TABLE_NAME_PATIENT_PROTOCOL, 'pp')
            ->leftJoin('pp', self::TABLE_NAME, 'p', 'p.patientId = pp.patientId')
            ->andWhere('pp.isActive = 1')
            ->andWhere('p.isActive = 1')
            ->andWhere('pp.treatmentStartDate IS NOT NULL')
            ->orderBy('year', 'DESC');

        $data = $this->connection->fetchAllAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        $years = array_map(fn ($row) => (int) $row['year'], $data);

        return new ArrayCollection($years);
    }

    /** @return Collection<int, PatientsCountByHospitalView> */
    public function countOfPatientsByHospitals(?int $year = null): Collection
    {
        $queryBuilder = $this->connection->createQueryBuilder();

        $queryBuilder
            ->select('JSON_UNQUOTE(h.hospital) as hospital', 'COUNT(DISTINCT p.patientId) as count')
            ->from(self::TABLE_NAME, 'p')
            ->join('p', 'JSON_TABLE(p.hospitals, "$[*]" COLUMNS (hospital VARCHAR(255) PATH "$"))', 'h')
            ->andWhere('p.isActive = 1')
            ->groupBy('hospital');

        if (null !== $year) {
            $queryBuilder
                ->leftJoin('p', self::TABLE_NAME_PATIENT_PROTOCOL, 'pp', 'p.patientId = pp.patientId')
                ->andWhere('pp.isActive = 1')
                ->andWhere('YEAR(pp.treatmentStartDate) = :year')
                ->setParameter('year', $year);
        }

        $data = $this->connection->fetchAllAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        $collection = new ArrayCollection();
        foreach ($data as $row) {
            $collection->add(PatientsCountByHospitalView::deserialize($row));
        }

        return $collection;
    }

    /** @return Collection<int, PatientsCountByGenderView> */
    public function countOfPatientsByGender(?int $year = null): Collection
    {
        $queryBuilder = $this->connection->createQueryBuilder();

        $queryBuilder
            ->select('p.gender', 'COUNT(DISTINCT p.patientId) as count')
            ->from(self::TABLE_NAME, 'p')
            ->andWhere('p.isActive = 1')
            ->groupBy('p.gender');

        if (null !== $year) {
            $queryBuilder
                ->leftJoin('p', self::TABLE_NAME_PATIENT_PROTOCOL, 'pp', 'p.patientId = pp.patientId')
                ->andWhere('pp.isActive = 1')
                ->andWhere('YEAR(pp.treatmentStartDate) = :year')
                ->setParameter('year', $year);
        }

        $data = $this->connection->fetchAllAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        $collection = new ArrayCollection();
        foreach ($data as $row) {
            $collection->add(PatientsCountByGenderView::deserialize($row));
        }

        return $collection;
    }

    /** @return Collection<int, PatientsCountByProtocolView> */
    public function countOfPatientsByProtocols(?int $year = null): Collection
    {
        $queryBuilder = $this->connection->createQueryBuilder();

        $queryBuilder
            ->select('pp.protocol', 'COUNT(pp.patientId) as count')
            ->from(self::TABLE_NAME_PATIENT_PROTOCOL, 'pp')
            ->leftJoin('pp', self::TABLE_NAME, 'p', 'p.patientId = pp.patientId')
            ->andWhere('pp.isActive = 1')
            ->andWhere('p.isActive = 1')
            ->groupBy('pp.protocol');

        if (null !== $year) {
            $queryBuilder
                ->andWhere('YEAR(pp.treatmentStartDate) = :year')
                ->setParameter('year', $year);
        }

        $data = $this->connection->fetchAllAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        $collection = new ArrayCollection();
        foreach ($data as $row) {
            $collection->add(PatientsCountByProtocolView::deserialize($row));
        }

        return $collection;
    }

    /** @return Collection<int, PatientsCountByProtocolTypeView> */
    public function countAllProtocolsByProtocolType(?int $year = null): Collection
    {
        $queryBuilder = $this->connection->createQueryBuilder();

        // Get the list of extended protocol values for the CASE statement
        $extendedProtocolValues = array_map(
            fn (Protocol $protocol) => $protocol->value,
            Protocol::getProtocolsByType(ProtocolType::EXTENDED)
        );

        // Build the CASE statement string dynamically
        $caseConditions = [];
        $params = [];
        foreach ($extendedProtocolValues as $index => $value) {
            $paramName = 'ext_proto_'.$index;
            $caseConditions[] = $queryBuilder->expr()->eq('pp.protocol', ':'.$paramName);
            $params[$paramName] = $value;
        }
        $caseStatement = sprintf(
            'CASE WHEN %s THEN :typeExtended ELSE :typeBasic END',
            implode(' OR ', $caseConditions)
        );
        $params['typeExtended'] = ProtocolType::EXTENDED->value;
        $params['typeBasic'] = ProtocolType::BASIC->value;

        $queryBuilder
            ->select(sprintf('(%s) as protocolType', $caseStatement), 'COUNT(pp.patientId) as count')
            ->from(self::TABLE_NAME_PATIENT_PROTOCOL, 'pp')
            ->leftJoin('pp', self::TABLE_NAME, 'p', 'p.patientId = pp.patientId')
            ->andWhere('pp.isActive = 1')
            ->andWhere('p.isActive = 1')
            ->andWhere('pp.protocol != "FOLLOWUP"')
            ->groupBy('protocolType');

        // Apply parameters for the CASE statement
        $queryBuilder->setParameters($params);

        if (null !== $year) {
            $queryBuilder
                ->andWhere('YEAR(pp.treatmentStartDate) = :year')
                ->setParameter('year', $year);
        }

        $data = $this->connection->fetchAllAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        $collection = new ArrayCollection();
        foreach ($data as $row) {
            // Create the specific array shape expected by deserialize
            $deserializationData = [
                'protocolType' => (string) $row['protocolType'], // Ensure protocolType is string
                'count' => (int) $row['count'], // Ensure count is integer
            ];
            $collection->add(PatientsCountByProtocolTypeView::deserialize($deserializationData));
        }

        return $collection;
    }

    /**
     * @param ?array<string> $onlyAllowHospitals
     * @param ?array<string> $onlyAllowProtocols
     */
    public function countOfVisiblePatientsForAccount(?array $onlyAllowHospitals, ?array $onlyAllowProtocols): int
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('COUNT(*)')
            ->from(self::TABLE_NAME, 'p')
            ->leftJoin('p', self::TABLE_NAME_PATIENT_PROTOCOL, 'pp', 'p.patientId = pp.patientId')
            ->andWhere('p.isActive = 1')
            ->groupBy('p.patientId');

        if (!empty($onlyAllowHospitals)) {
            foreach ($onlyAllowHospitals as $index => $hospital) {
                $queryBuilder
                    ->orWhere(sprintf('JSON_CONTAINS(p.hospitals, :hospital%d)', $index))
                    ->setParameter(sprintf('hospital%d', $index), json_encode([$hospital]));
            }
        }

        if (!empty($onlyAllowProtocols)) {
            $protocolIds = array_map([$this->connection, 'quote'], $onlyAllowProtocols);
            $queryBuilder
                ->andWhere($queryBuilder->expr()->in('pp.protocol', $protocolIds));
        }

        return count($this->connection->fetchAllAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters()));
    }
}
