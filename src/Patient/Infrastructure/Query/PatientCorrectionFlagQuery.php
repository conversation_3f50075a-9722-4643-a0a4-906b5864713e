<?php

declare(strict_types=1);

namespace App\Patient\Infrastructure\Query;

use App\Common\Query\GridConfiguration;
use App\Common\Query\GridResult;
use App\Patient\Application\Query\PatientCorrectionFlagQueryInterface;
use App\Patient\Application\Query\PatientCorrectionFlagView;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Query\QueryBuilder;

final class PatientCorrectionFlagQuery implements PatientCorrectionFlagQueryInterface
{
    private const string TABLE_CORRECTION_FLAG = 'patient_correction_flags';
    private const string TABLE_PATIENT = 'patient_patient';
    private const string TABLE_PATIENT_PROTOCOL = 'patient_patient_protocol';

    public function __construct(
        private readonly Connection $connection,
    ) {
    }

    public function findById(string $flagId): ?PatientCorrectionFlagView
    {
        $qb = $this->prepareBaseQueryBuilder();
        $qb->resetWhere();

        $qb->andWhere('pcf.id = :flagId')
           ->setParameter('flagId', $flagId);

        $data = $this->connection->fetchAssociative($qb->getSQL(), $qb->getParameters());

        return $data ? PatientCorrectionFlagView::deserialize($data) : null;
    }

    /**
     * @param array<string> $onlyAllowHospitals
     * @param array<string> $onlyAllowProtocols
     */
    public function findForGrid(GridConfiguration $gridConfiguration, array $onlyAllowHospitals, array $onlyAllowProtocols): GridResult
    {
        $baseQueryBuilder = $this->prepareBaseQueryBuilder();

        // Pass renamed parameters
        $this->applyPermissionFilters($baseQueryBuilder, $onlyAllowHospitals, $onlyAllowProtocols);

        // Count total items before applying pagination/sorting specific to the grid view
        $totalItems = $this->countTotalItems(clone $baseQueryBuilder); // Clone before adding grid config

        // Add grid sorting and filtering
        $this->addGridConfiguration($baseQueryBuilder, $gridConfiguration);

        // Add pagination
        $baseQueryBuilder->setFirstResult(($gridConfiguration->page() - 1) * $gridConfiguration->itemsPerPage())
                      ->setMaxResults($gridConfiguration->itemsPerPage());

        $data = $this->connection->fetchAllAssociative($baseQueryBuilder->getSQL(), $baseQueryBuilder->getParameters());
        $collection = new ArrayCollection();

        foreach ($data as $row) {
            $collection->set($row['flagId'], PatientCorrectionFlagView::deserialize($row));
        }

        return new GridResult($collection, $totalItems, $gridConfiguration->page(), $gridConfiguration->itemsPerPage());
    }

    private function prepareBaseQueryBuilder(): QueryBuilder
    {
        $qb = $this->connection->createQueryBuilder();

        $qb->select(
            'pcf.id AS flagId',
            'pcf.patientId AS patientId',
            'p.firstName AS patientFirstName',
            'p.lastName AS patientLastName',
            'p.patientPublicId AS patientPublicId',
            'pcf.hospitalId AS hospitalId',
            'pcf.comment AS comment',
            'pcf.createdAt AS createdAt'
        )
           ->from(self::TABLE_CORRECTION_FLAG, 'pcf')
           ->leftJoin('pcf', self::TABLE_PATIENT, 'p', 'pcf.patientId = p.patientId')
           ->where('pcf.resolvedAt IS NULL');

        return $qb;
    }

    /**
     * @param array<string> $onlyAllowHospitals
     * @param array<string> $onlyAllowProtocols
     */
    private function applyPermissionFilters(QueryBuilder $qb, array $onlyAllowHospitals, array $onlyAllowProtocols): void
    {
        $params = [];

        if (!empty($onlyAllowHospitals)) {
            $hospitalParams = [];
            foreach ($onlyAllowHospitals as $i => $hospitalId) {
                $paramName = 'hospital'.$i;
                $hospitalParams[$paramName] = $hospitalId;
                $params[$paramName] = $hospitalId;
            }
            $qb->andWhere($qb->expr()->in('pcf.hospitalId', ':'.implode(', :', array_keys($hospitalParams))));
        }

        if (!empty($onlyAllowProtocols)) {
            $protocolParams = [];
            foreach ($onlyAllowProtocols as $i => $protocolId) {
                $paramName = 'protocol'.$i;
                $protocolParams[$paramName] = $protocolId;
                $params[$paramName] = $protocolId;
            }

            $subQuery = $this->connection->createQueryBuilder();
            $subQuery->select('1')
                     ->from(self::TABLE_PATIENT_PROTOCOL, 'pp_perm')
                     ->where('pp_perm.patientId = pcf.patientId')
                     ->andWhere('pp_perm.isActive = 1')
                     ->andWhere($subQuery->expr()->in('pp_perm.protocol', ':'.implode(', :', array_keys($protocolParams))));

            $qb->andWhere(sprintf('EXISTS (%s)', $subQuery->getSQL()));
        }

        if (!empty($params)) {
            $qb->setParameters($params);
        }
    }

    private function countTotalItems(QueryBuilder $qb): int
    {
        // Reset parts not needed for count
        $qb->select('COUNT(DISTINCT pcf.id)')
           ->resetOrderBy()
           ->resetGroupBy()
           ->setMaxResults(null); // Remove limit for counting

        return (int) $this->connection->fetchOne($qb->getSQL(), $qb->getParameters());
    }

    private function addGridConfiguration(QueryBuilder $qb, GridConfiguration $gridConfiguration): void
    {
        $fieldMap = [
            'flagId' => 'pcf.id',
            'patientId' => 'pcf.patientId',
            'patientFirstName' => 'p.firstName',
            'patientLastName' => 'p.lastName',
            'patientPublicId' => 'p.patientPublicId',
            'hospitalId' => 'pcf.hospitalId',
            'comment' => 'pcf.comment',
            'createdAt' => 'pcf.createdAt',
        ];

        $gridConfiguration->addSortAndFilterToDbalQueryBuilder($qb, $fieldMap);

        if (count($gridConfiguration->sorts()) === 0) {
            $qb->addOrderBy('pcf.createdAt', 'DESC');
        }
    }

    /** @return iterable<PatientCorrectionFlagView> */
    public function findAll(?\DateTimeImmutable $createdSince = null): iterable
    {
        $qb = $this->connection->createQueryBuilder();

        $qb->select(
            'pcf.id AS flagId',
            'pcf.patientId AS patientId',
            'p.firstName AS patientFirstName',
            'p.lastName AS patientLastName',
            'p.patientPublicId AS patientPublicId',
            'pcf.hospitalId AS hospitalId',
            'pcf.comment AS comment',
            'pcf.createdAt AS createdAt',
            'pcf.resolvedAt AS resolvedAt',
            'pcf.resolvedById AS resolvedById'
        )
           ->from(self::TABLE_CORRECTION_FLAG, 'pcf')
           ->leftJoin('pcf', self::TABLE_PATIENT, 'p', 'pcf.patientId = p.patientId');

        if ($createdSince !== null) {
            $qb->andWhere('pcf.createdAt >= :createdSince')
               ->setParameter('createdSince', $createdSince->format('Y-m-d H:i:s'));
        }

        $qb->orderBy('pcf.createdAt', 'DESC');

        $results = $this->connection->iterateAssociative($qb->getSQL(), $qb->getParameters());

        foreach ($results as $row) {
            yield PatientCorrectionFlagView::deserialize($row);
        }
    }
}
