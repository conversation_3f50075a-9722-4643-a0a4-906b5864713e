<?php

declare(strict_types=1);

namespace App\Patient\Domain;

use App\Common\ValueObject;
use Webmozart\Assert\Assert;

class PatientPublicId implements ValueObject
{
    private string $patientPublicId;

    private function __construct(string $patientPublicId)
    {
        $this->patientPublicId = $patientPublicId;
    }

    public static function create(string $firstName, string $lastName, ?string $number = null): self
    {
        if ($number !== null) {
            Assert::length($number, 6, 'Number must be exactly 6 characters long.');
            Assert::numeric($number, 'Number must contain only digits.');
        }

        if ($number === null) {
            $number = random_int(1, 999999);
            $number = str_pad((string) $number, 6, '0', STR_PAD_LEFT);
        }

        $initials = mb_substr(trim($firstName), 0, 1, 'UTF-8').mb_substr(trim($lastName), 0, 1, 'UTF-8');

        // Convert initials to uppercase
        $initials = strtoupper($initials);

        // Replace Polish characters
        $unwanted_array = ['ą' => 'a', 'ć' => 'c', 'ę' => 'e', 'ł' => 'l', 'ń' => 'n', 'ó' => 'o', 'ś' => 's', 'ź' => 'z', 'ż' => 'z',
            'Ą' => 'A', 'Ć' => 'C', 'Ę' => 'E', 'Ł' => 'L', 'Ń' => 'N', 'Ó' => 'O', 'Ś' => 'S', 'Ź' => 'Z', 'Ż' => 'Z'];
        $initials = strtr($initials, $unwanted_array);

        $id = $initials.$number;

        return new self($id);
    }

    public function patientPublicId(): string
    {
        return $this->patientPublicId;
    }

    public function jsonSerialize(): mixed
    {
        return $this->patientPublicId;
    }
}
