<?php

declare(strict_types=1);

namespace App\Patient\Domain;

use App\Common\Attribute\TrackEntityChange;
use App\Common\Uuid;

#[TrackEntityChange(objectLogDetailsFunction: 'logDetails')]
class PatientAttachment
{
    private Uuid $patientAttachmentId;
    private Patient $patient;
    private PatientAttachmentType $type;
    private string $fileName;
    private string $extension;
    private \DateTimeImmutable $createdAt;

    private function __construct(Uuid $patientAttachmentId, Patient $patient, PatientAttachmentType $type, string $fileName, string $extension)
    {
        $this->patientAttachmentId = $patientAttachmentId;
        $this->patient = $patient;
        $this->type = $type;
        $this->fileName = $fileName;
        $this->extension = $extension;
        $this->createdAt = new \DateTimeImmutable();
    }

    public static function create(Uuid $patientAttachmentId, Patient $patient, PatientAttachmentType $type, string $fileName, string $extension): self
    {
        return new self($patientAttachmentId, $patient, $type, $fileName, $extension);
    }

    /** @return array<string, string> */
    public function logDetails(): array
    {
        return array_merge($this->patient->logDetails(), [
            'patientAttachmentId' => $this->patientAttachmentId->toString(),
            'fileName' => $this->fileName,
            'type' => $this->type->value,
        ]);
    }

    public function patientAttachmentId(): Uuid
    {
        return $this->patientAttachmentId;
    }

    public function patient(): Patient
    {
        return $this->patient;
    }

    public function type(): PatientAttachmentType
    {
        return $this->type;
    }

    public function fileName(): string
    {
        return $this->fileName;
    }

    public function extension(): string
    {
        return $this->extension;
    }

    public function createdAt(): \DateTimeImmutable
    {
        return $this->createdAt;
    }
}
