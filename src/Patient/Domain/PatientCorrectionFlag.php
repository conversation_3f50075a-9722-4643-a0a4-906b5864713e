<?php

declare(strict_types=1);

namespace App\Patient\Domain;

use App\Common\Uuid;
use App\Patient\Domain\Exception\FlagAlreadyResolvedException;

class PatientCorrectionFlag
{
    private Uuid $id;
    private Uuid $patientId;
    private Uuid $hospitalId;
    private string $comment;
    private \DateTimeImmutable $createdAt;
    private ?Uuid $resolvedById = null;
    private ?\DateTimeImmutable $resolvedAt = null;
    private int $version;

    public function __construct(
        Uuid $patientId,
        Uuid $hospitalId,
        string $comment,
    ) {
        if (empty(trim($comment))) {
            throw new \InvalidArgumentException('Correction flag comment cannot be empty.');
        }

        $this->id = Uuid::generate();
        $this->patientId = $patientId;
        $this->hospitalId = $hospitalId;
        $this->comment = $comment;
        $this->createdAt = new \DateTimeImmutable();
    }

    public function resolve(Uuid $resolverUserId): void
    {
        if ($this->isResolved()) {
            throw FlagAlreadyResolvedException::withId($this->id->toString());
        }

        $this->resolvedById = $resolverUserId;
        $this->resolvedAt = new \DateTimeImmutable();
    }

    public function isResolved(): bool
    {
        return $this->resolvedAt !== null;
    }

    public function id(): Uuid
    {
        return $this->id;
    }

    public function patientId(): Uuid
    {
        return $this->patientId;
    }

    public function hospitalId(): Uuid
    {
        return $this->hospitalId;
    }

    public function comment(): string
    {
        return $this->comment;
    }

    public function createdAt(): \DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function resolvedById(): ?Uuid
    {
        return $this->resolvedById;
    }

    public function resolvedAt(): ?\DateTimeImmutable
    {
        return $this->resolvedAt;
    }
}
