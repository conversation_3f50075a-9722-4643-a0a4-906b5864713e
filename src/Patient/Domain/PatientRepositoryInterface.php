<?php

declare(strict_types=1);

namespace App\Patient\Domain;

use App\Common\Uuid;

interface PatientRepositoryInterface
{
    public function add(Patient $patient): void;

    public function update(Patient $patient): void;

    public function getByPatientId(Uuid $patientId): Patient;

    public function findByPesel(Pesel $pesel, ?Uuid $excludePatientId = null): ?Patient;

    public function findByPassportNumber(PassportNumber $passportNumber, ?Uuid $excludePatientId = null): ?Patient;

    public function patientPublicIdExists(PatientPublicId $patientPublicId): bool;
}
