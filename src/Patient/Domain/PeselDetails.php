<?php

declare(strict_types=1);

namespace App\Patient\Domain;

use App\Common\ValueObject;

class PeselDetails implements ValueObject
{
    private Pesel $pesel;
    private BirthDate $birthDate;
    private Gender $gender;

    private function __construct(Pesel $pesel, BirthDate $birthDate, Gender $gender)
    {
        $this->pesel = $pesel;
        $this->birthDate = $birthDate;
        $this->gender = $gender;
    }

    public static function create(Pesel $pesel, BirthDate $birthDate, Gender $gender): self
    {
        return new self($pesel, $birthDate, $gender);
    }

    /** @return array<string, mixed> */
    public function jsonSerialize(): array
    {
        return [
            'pesel' => $this->pesel->pesel(),
            'birthDate' => $this->birthDate->birthDateString(),
            'gender' => $this->gender->value,
        ];
    }

    public function isEqualsTo(PeselDetails $other): bool
    {
        return $this->pesel->pesel() === $other->pesel->pesel()
            && $this->birthDate->birthDateString() === $other->birthDate->birthDateString()
            && $this->gender->value === $other->gender->value;
    }

    public function pesel(): Pesel
    {
        return $this->pesel;
    }

    public function birthDate(): BirthDate
    {
        return $this->birthDate;
    }

    public function gender(): Gender
    {
        return $this->gender;
    }
}
