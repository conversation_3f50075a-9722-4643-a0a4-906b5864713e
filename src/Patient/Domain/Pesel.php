<?php

declare(strict_types=1);

namespace App\Patient\Domain;

use App\Common\ValueObject;

class Pesel implements ValueObject
{
    private ?string $pesel;

    private function __construct(string $pesel)
    {
        if (!$this->isValidPesel($pesel)) {
            throw new \InvalidArgumentException('Podany numer PESEL jest nieprawidłowy.', 422);
        }

        $this->pesel = $pesel;
    }

    public static function create(string $pesel): self
    {
        return new self($pesel);
    }

    public function gender(): Gender
    {
        if ($this->pesel === null) {
            throw new \RuntimeException('Numer PESEL nie został ustawiony.', 500);
        }

        return (int) $this->pesel[9] % 2 === 0 ? Gender::FEMALE : Gender::MALE;
    }

    public function birthDate(): BirthDate
    {
        if ($this->pesel === null) {
            throw new \RuntimeException('Numer PESEL nie został ustawiony.', 500);
        }

        $year = substr($this->pesel, 0, 2);
        $month = (int) substr($this->pesel, 2, 2);
        $day = substr($this->pesel, 4, 2);

        if ($month > 20) {
            $year = '20'.$year;
            $month = $month - 20;
        } else {
            $year = '19'.$year;
        }

        $birthDate = $year.'-'.str_pad((string) $month, 2, '0', STR_PAD_LEFT).'-'.$day;

        return BirthDate::createFromString($birthDate);
    }

    public function isEqualsTo(Pesel $other): bool
    {
        return $this->pesel === $other->pesel();
    }

    public function pesel(): string
    {
        if ($this->pesel === null) {
            throw new \RuntimeException('Numer PESEL nie został ustawiony.', 500);
        }

        return $this->pesel;
    }

    public function peselDetails(): PeselDetails
    {
        return PeselDetails::create($this, $this->birthDate(), $this->gender());
    }

    private function isValidPesel(string $pesel): bool
    {
        if (strlen($pesel) !== 11 || !ctype_digit($pesel)) {
            return false;
        }

        $weights = [1, 3, 7, 9, 1, 3, 7, 9, 1, 3];
        $sum = 0;

        for ($i = 0; $i < 10; ++$i) {
            $sum += $weights[$i] * (int) $pesel[$i];
        }

        $checksum = (10 - $sum % 10) % 10;

        if ($checksum !== (int) $pesel[10]) {
            return false;
        }

        // Dodatkowa walidacja daty urodzenia
        if (!$this->isValidBirthDate($pesel)) {
            return false;
        }

        return true;
    }

    private function isValidBirthDate(string $pesel): bool
    {
        $year = (int) substr($pesel, 0, 2);
        $month = (int) substr($pesel, 2, 2);
        $day = (int) substr($pesel, 4, 2);

        if ($month > 20) {
            $year += 2000;
            $month -= 20;
        } else {
            $year += 1900;
        }

        return checkdate($month, $day, $year);
    }

    public function jsonSerialize(): ?string
    {
        return $this->pesel;
    }
}
