<?php

declare(strict_types=1);

namespace App\Patient\Domain;

use App\Common\Uuid;

class FavoritePatient
{
    private int $id;
    private Uuid $authUserId;
    private Uuid $patientId;
    private \DateTimeImmutable $createdAt;

    public function __construct(Uuid $authUserId, Uuid $patientId)
    {
        $this->authUserId = $authUserId;
        $this->patientId = $patientId;
        $this->createdAt = new \DateTimeImmutable();
    }

    public function authUserId(): Uuid
    {
        return $this->authUserId;
    }

    public function patientId(): Uuid
    {
        return $this->patientId;
    }

    public function createdAt(): \DateTimeImmutable
    {
        return $this->createdAt;
    }
}
