<?php

declare(strict_types=1);

namespace App\Patient\Domain;

use App\Common\Uuid;
use App\Patient\Domain\Exception\PatientCorrectionFlagNotFoundException; // Use specific exception

interface PatientCorrectionFlagRepositoryInterface
{
    public function save(PatientCorrectionFlag $flag): void;

    public function findById(Uuid $id): ?PatientCorrectionFlag;

    /** @throws PatientCorrectionFlagNotFoundException */
    public function getById(Uuid $id): PatientCorrectionFlag;
}
