<?php

declare(strict_types=1);

namespace App\Patient\Domain;

use App\Common\ValueObject;
use Webmozart\Assert\Assert;

class Address implements ValueObject
{
    /** @phpstan-ignore-next-line */
    private string $streetWithNumber;
    /** @phpstan-ignore-next-line */
    private string $city;
    /** @phpstan-ignore-next-line */
    private string $postCode;

    private function __construct(string $streetWithNumber, string $city, string $postCode)
    {
        Assert::minLength($streetWithNumber, 3, 'Ulica i numer domu muszą składać się z co najmniej 3 znaków.');
        Assert::minLength($city, 3, 'Nazwa miasta musi składać się z co najmniej 3 znaków.');
        Assert::regex($postCode, '/^\d{2}-\d{3}$/', 'Nieprawidłowy kod pocztowy.');

        $this->streetWithNumber = $streetWithNumber;
        $this->city = $city;
        $this->postCode = $postCode;
    }

    public static function create(string $streetWithNumber, string $city, string $postCode): self
    {
        return new self($streetWithNumber, $city, $postCode);
    }

    public function isEqualsTo(Address $other): bool
    {
        return $this->streetWithNumber === $other->streetWithNumber()
            && $this->city === $other->city()
            && $this->postCode === $other->postCode();
    }

    public function streetWithNumber(): string
    {
        return $this->streetWithNumber;
    }

    public function city(): string
    {
        return $this->city;
    }

    public function postCode(): string
    {
        return $this->postCode;
    }

    public function jsonSerialize(): mixed
    {
        return [
            'streetWithNumber' => $this->streetWithNumber,
            'city' => $this->city,
            'postCode' => $this->postCode,
        ];
    }
}
