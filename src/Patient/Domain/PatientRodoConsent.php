<?php

declare(strict_types=1);

namespace App\Patient\Domain;

use App\Common\Attribute\TrackEntityChange;
use App\Common\Uuid;

#[TrackEntityChange(objectLogDetailsFunction: 'logDetails')]
class PatientRodoConsent
{
    private Uuid $patientRodoConsentId;
    private string $fileName;
    private string $extension;
    private \DateTimeImmutable $createdAt;
    private Patient $patient;

    private function __construct(Uuid $patientRodoConsentId, string $fileName, string $extension, Patient $patient)
    {
        $this->patientRodoConsentId = $patientRodoConsentId;
        $this->fileName = $fileName;
        $this->extension = $extension;
        $this->createdAt = new \DateTimeImmutable();
        $this->patient = $patient;
    }

    public static function create(Uuid $patientRodoConsentId, string $fileName, string $extension, Patient $patient): self
    {
        return new self($patientRodoConsentId, $fileName, $extension, $patient);
    }

    /** @return array<string, string> */
    public function logDetails(): array
    {
        return $this->patient->logDetails();
    }

    public function patientRodoConsentId(): Uuid
    {
        return $this->patientRodoConsentId;
    }

    public function fileName(): string
    {
        return $this->fileName;
    }

    public function extension(): string
    {
        return $this->extension;
    }

    public function createdAt(): \DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function patient(): Patient
    {
        return $this->patient;
    }
}
