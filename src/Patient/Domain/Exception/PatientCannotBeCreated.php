<?php

declare(strict_types=1);

namespace App\Patient\Domain\Exception;

use App\Common\Exception\DomainException;

class PatientCannotBeCreated extends DomainException
{
    public static function becauseHospitalListCannotBeEmpty(): self
    {
        return new self('<PERSON><PERSON><PERSON> wybrać przynajmniej jeden szpital.', 422);
    }

    public static function becauseFollowUpProtocolCannotBeAddedToPatient(): self
    {
        return new self('Nie można dodać protokołu FOLLOWUP do pacjenta, poniewa<PERSON> jest on dodawany automatycznie.', 422);
    }
}
