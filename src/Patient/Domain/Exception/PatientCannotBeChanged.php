<?php

declare(strict_types=1);

namespace App\Patient\Domain\Exception;

use App\Common\Exception\DomainException;

class PatientCannotBeChanged extends DomainException
{
    public static function becausePeselIsRequiredForPolishCitizenship(): self
    {
        return new self('Podaj numer PESEL, jeśli jesteś obywatelem Polski.', 422);
    }

    public static function becausePassportNumberShouldNotBeFilledInIfPatientIsPolishCitizenshipAndHasPesel(): self
    {
        return new self('Numer paszportu nie powinien być wypełniony, jeśli pacjent jest obywatelem Polski i posiada numer PESEL.', 422);
    }

    public static function becauseLegalRepresentativePeselIsRequiredForPolishCitizenship(): self
    {
        return new self('Podaj numer PESEL przedstawiciela ustawowego, jeśli jesteś obywatelem <PERSON>ski.', 422);
    }

    public static function becausePeselOrPassportIsRequiredForNonPolishCitizenship(): self
    {
        return new self('Podaj numer PESEL lub numer paszportu, jeśli nie jesteś obywatelem Polski.', 422);
    }

    public static function becauseRegisteredAddressCannotBeDefinedIfSameAsResidence(): self
    {
        return new self('Jeśli twój adres zameldowania i zamieszkania są takie same, nie musisz podawać adresu zameldowania.', 422);
    }

    public static function becauseCorrespondenceAddressCannotBeDefinedIfSameAsResidence(): self
    {
        return new self('Jeśli twój adres korespondencyjny i zamieszkania są takie same, nie musisz podawać adresu korespondencyjnego.', 422);
    }

    public static function becauseRegisteredAddressCannotBeEmptyIfDifferentFromResidence(): self
    {
        return new self('Podaj adres zameldowania, jeśli jest inny niż adres zamieszkania.', 422);
    }

    public static function becauseCorrespondenceAddressCannotBeEmptyIfDifferentFromResidence(): self
    {
        return new self('Podaj adres korespondencyjny, jeśli jest inny niż adres zamieszkania.', 422);
    }

    public static function becauseBirthDateMustMatchPesel(): self
    {
        return new self('Data urodzenia musi zgadzać się z datą urodzenia w numerze PESEL.', 422);
    }

    public static function becauseGenderMustMatchPesel(): self
    {
        return new self('Płeć musi zgadzać się z płcią w numerze PESEL.', 422);
    }
}
