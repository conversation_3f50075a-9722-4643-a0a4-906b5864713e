<?php

declare(strict_types=1);

namespace App\Patient\Domain\Exception;

use App\Common\Exception\DomainException;

class PatientProtocolCannotBeActivated extends DomainException
{
    public static function becauseIsAlreadyActivated(string $patientId, string $patientProtocolId): self
    {
        return new self(sprintf('Jednostka chorobowa %s jest już aktywna dla pacjenta %s.', $patientProtocolId, $patientId), 422);
    }

    public static function becauseDoesNotExist(string $patientId, string $patientProtocolId): self
    {
        return new self(sprintf('Jednostka chorobowa %s nie istnieje dla pacjenta %s.', $patientProtocolId, $patientId), 404);
    }
}
