<?php

declare(strict_types=1);

namespace App\Patient\Domain\Exception;

use App\Common\Exception\DomainException;

class HospitalCannotBeRemovedFromPatient extends DomainException
{
    public static function becauseHospitalDoesNotExist(string $hospitalId, string $patientId): self
    {
        return new self(sprintf('Szpital %s już istnieje w naszej bazie danych dla pacjenta o identyfikatorze %s.', $hospitalId, $patientId), 409);
    }

    public static function becausePatientMustHaveAtLeastOneHospital(string $patientId): self
    {
        return new self(sprintf('Pacjent o identyfikatorze %s musi mieć przynajmniej jeden szpital.', $patientId), 422);
    }
}
