<?php

declare(strict_types=1);

namespace App\Patient\Domain\Exception;

use App\Common\Exception\DomainException;

class PatientProtocolCannotBeDeactivated extends DomainException
{
    public static function becauseIsAlreadyDeactivated(string $patientId, string $patientProtocolId): self
    {
        return new self(sprintf('Jednostka chorobowa %s jest już nieaktywna dla pacjenta %s.', $patientProtocolId, $patientId), 422);
    }

    public static function becauseAtLeastOnePatientProtocolMustBeActive(string $patientId): self
    {
        return new self(sprintf('Przynajmniej jedna jednostka chorobowa musi być aktywna dla pacjenta %s.', $patientId), 422);
    }

    public static function becauseDoesNotExist(string $patientId, string $patientProtocolId): self
    {
        return new self(sprintf('Jednostka chorobowa %s nie istnieje dla pacjenta %s.', $patientProtocolId, $patientId), 404);
    }
}
