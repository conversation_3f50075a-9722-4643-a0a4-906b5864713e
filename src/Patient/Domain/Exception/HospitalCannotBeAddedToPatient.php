<?php

declare(strict_types=1);

namespace App\Patient\Domain\Exception;

use App\Common\Exception\DomainException;

class HospitalCannotBeAddedToPatient extends DomainException
{
    public static function becauseHospitalAlreadyExists(string $hospitalId, string $patientId): self
    {
        return new self(sprintf('Szpital %s już istnieje dla pacjenta o identyfikatorze %s.', $hospitalId, $patientId), 409);
    }
}
