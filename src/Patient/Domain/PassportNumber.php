<?php

declare(strict_types=1);

namespace App\Patient\Domain;

use App\Common\ValueObject;
use Webmozart\Assert\Assert;

class PassportNumber implements ValueObject
{
    private ?string $passportNumber;

    private function __construct(string $passportNumber)
    {
        Assert::maxLength($passportNumber, 20, 'Numer paszportu nie może przekraczać 20 znaków.');
        Assert::regex($passportNumber, '/^[A-Za-z0-9]+$/', 'Numer paszportu może składać się tylko z liter i cyfr.');

        $this->passportNumber = $passportNumber;
    }

    public static function create(string $number): self
    {
        return new self($number);
    }

    public function passportNumber(): string
    {
        if ($this->passportNumber === null) {
            throw new \RuntimeException('Numer paszportu nie jest ustawiony.');
        }

        return $this->passportNumber;
    }

    public function isEqualsTo(self $other): bool
    {
        return $this->passportNumber === $other->passportNumber();
    }

    public function jsonSerialize(): ?string
    {
        return $this->passportNumber;
    }
}
