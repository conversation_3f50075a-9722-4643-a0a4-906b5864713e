<?php

declare(strict_types=1);

namespace App\Patient\Domain;

use App\Common\Attribute\TrackEntityChange;
use App\Common\Uuid;
use App\SharedKernel\Protocol;

#[TrackEntityChange(objectLogDetailsFunction: 'logDetails')]
class PatientProtocol
{
    private Uuid $patientProtocolId;
    private Patient $patient;
    private Protocol $protocol;
    private bool $isActive;
    private ?string $icd10Code = null;
    private ?\DateTimeImmutable $treatmentStartDate = null;

    private function __construct(Uuid $patientProtocolId, Patient $patient, Protocol $protocol, bool $isActive)
    {
        $this->patientProtocolId = $patientProtocolId;
        $this->patient = $patient;
        $this->protocol = $protocol;
        $this->isActive = $isActive;
    }

    public static function create(Uuid $patientProtocolId, Patient $patient, Protocol $protocol): self
    {
        return new self($patientProtocolId, $patient, $protocol, true);
    }

    public function setIcd10Code(?string $icd10Code): void
    {
        $this->icd10Code = $icd10Code;
    }

    public function setStartTreatment(?\DateTimeImmutable $startDate): void
    {
        $this->treatmentStartDate = $startDate;
    }

    /** @return array<string, string> */
    public function logDetails(): array
    {
        return $this->patient->logDetails();
    }

    public function activate(): void
    {
        $this->isActive = true;
    }

    public function deactivate(): void
    {
        $this->isActive = false;
    }

    public function patientProtocolId(): Uuid
    {
        return $this->patientProtocolId;
    }

    public function patient(): Patient
    {
        return $this->patient;
    }

    public function protocol(): Protocol
    {
        return $this->protocol;
    }

    public function isActive(): bool
    {
        return $this->isActive;
    }
}
