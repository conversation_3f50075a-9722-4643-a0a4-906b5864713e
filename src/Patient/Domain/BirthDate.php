<?php

declare(strict_types=1);

namespace App\Patient\Domain;

use App\Common\ValueObject;

class BirthDate implements ValueObject
{
    private \DateTimeImmutable $birthDate;

    private function __construct(\DateTimeImmutable $birthDate)
    {
        $this->birthDate = $birthDate;
    }

    public static function createFromString(string $birthDate): self
    {
        try {
            $date = new \DateTimeImmutable($birthDate);
        } catch (\Exception $e) {
            throw new \InvalidArgumentException('Nieprawidłowy format daty urodzenia.', 422);
        }

        if ($date > new \DateTimeImmutable()) {
            throw new \InvalidArgumentException('Data urodzenia nie może być w przyszłości.', 422);
        }

        return new self($date);
    }

    public function isEqualsTo(BirthDate $other): bool
    {
        return $this->birthDateString() === $other->birthDateString();
    }

    public function birthDateString(): string
    {
        return $this->__toString();
    }

    public function __toString(): string
    {
        return $this->birthDate->format('Y-m-d');
    }

    public function jsonSerialize(): mixed
    {
        return $this->__toString();
    }
}
