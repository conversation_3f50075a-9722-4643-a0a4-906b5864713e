<?php

declare(strict_types=1);

namespace App\Patient\Domain;

use App\Common\Attribute\EntityWithNullableEmbeddable;
use App\Common\Attribute\NullableEmbeddable;
use App\Common\Attribute\TrackEntityChange;
use App\Common\Uuid;
use App\Patient\Domain\Exception\HospitalCannotBeAddedToPatient;
use App\Patient\Domain\Exception\HospitalCannotBeRemovedFromPatient;
use App\Patient\Domain\Exception\PatientAttachmentNotFound;
use App\Patient\Domain\Exception\PatientCannotBeChanged;
use App\Patient\Domain\Exception\PatientCannotBeCreated;
use App\Patient\Domain\Exception\PatientProtocolCannotBeActivated;
use App\Patient\Domain\Exception\PatientProtocolCannotBeDeactivated;
use App\Patient\Domain\Exception\PatientProtocolCannotBeUpdated;
use App\Patient\Domain\Exception\PatientRodoConsentNotFound;
use App\SharedKernel\Protocol;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;

#[EntityWithNullableEmbeddable]
#[TrackEntityChange(objectLogDetailsFunction: 'logDetails')]
class Patient
{
    private Uuid $patientId;
    private PatientPublicId $patientPublicId;
    private string $firstName;
    private string $lastName;
    private Citizenship $citizenship;
    private bool $isRegisteredAddressSameAsResidence;
    private bool $isCorrespondenceAddressSameAsResidence;
    private Address $residenceAddress;
    private ?Address $registeredAddress;
    private ?Address $correspondenceAddress;
    private ?string $email;
    private ?string $contactNumber;
    private bool $isPatientIdentified;
    #[NullableEmbeddable]
    private ?Pesel $pesel;
    #[NullableEmbeddable]
    private ?PassportNumber $passportNumber;
    #[NullableEmbeddable]
    private ?Pesel $legalRepresentativePesel;
    #[NullableEmbeddable]
    private ?PassportNumber $legalRepresentativePassportNumber;
    private Gender $gender;
    private BirthDate $birthDate;
    /** @var array<string> */
    private array $hospitals;
    /** @var Collection<int, PatientProtocol> */
    private Collection $protocols;
    private bool $isActive;
    private ?\DateTimeImmutable $followUpDateOfCompletionForm = null;
    private ?string $deactivationReason = null;
    private bool $isDeceased = false;
    private \DateTimeImmutable $createdAt;
    private ?\DateTimeImmutable $updatedAt = null;

    /** @var Collection<int, PatientRodoConsent> */
    private Collection $rodoConsents;

    /** @var Collection<string, PatientAttachment> */
    private Collection $attachments;

    /** @param array<string> $hospitals */
    public function __construct(
        Uuid $patientId,
        PatientPublicId $patientPublicId,
        string $firstName,
        string $lastName,
        Citizenship $citizenship,
        bool $isRegisteredAddressSameAsResidence,
        bool $isCorrespondenceAddressSameAsResidence,
        Address $residenceAddress,
        ?Address $registeredAddress,
        ?Address $correspondenceAddress,
        ?string $email,
        ?string $contactNumber,
        bool $isPatientIdentified,
        ?Pesel $pesel,
        ?PassportNumber $passportNumber,
        ?Pesel $legalRepresentativePesel,
        ?PassportNumber $legalRepresentativePassportNumber,
        Gender $gender,
        BirthDate $birthDate,
        array $hospitals,
        Protocol $protocol,
    ) {
        $this->validatePatientDataOrThrow(
            $citizenship,
            $isPatientIdentified,
            $pesel,
            $birthDate,
            $gender,
            $legalRepresentativePesel,
            $legalRepresentativePassportNumber,
            $passportNumber,
            $isRegisteredAddressSameAsResidence,
            $registeredAddress,
            $isCorrespondenceAddressSameAsResidence,
            $correspondenceAddress,
        );

        if (empty($hospitals)) {
            throw PatientCannotBeCreated::becauseHospitalListCannotBeEmpty();
        }

        if ($isPatientIdentified) {
            $legalRepresentativePesel = null;
            $legalRepresentativePassportNumber = null;
        } else {
            $pesel = null;
            $passportNumber = null;
        }

        if ($isRegisteredAddressSameAsResidence) {
            $registeredAddress = null;
        }

        if ($isCorrespondenceAddressSameAsResidence) {
            $correspondenceAddress = null;
        }

        $this->patientId = $patientId;
        $this->patientPublicId = $patientPublicId;
        $this->firstName = $firstName;
        $this->lastName = $lastName;
        $this->citizenship = $citizenship;
        $this->isRegisteredAddressSameAsResidence = $isRegisteredAddressSameAsResidence;
        $this->isCorrespondenceAddressSameAsResidence = $isCorrespondenceAddressSameAsResidence;
        $this->residenceAddress = $residenceAddress;
        $this->registeredAddress = $registeredAddress;
        $this->correspondenceAddress = $correspondenceAddress;
        $this->email = $email;
        $this->contactNumber = $contactNumber;
        $this->isPatientIdentified = $isPatientIdentified;
        $this->pesel = $pesel;
        $this->passportNumber = $passportNumber;
        $this->legalRepresentativePesel = $legalRepresentativePesel;
        $this->legalRepresentativePassportNumber = $legalRepresentativePassportNumber;
        $this->gender = $gender;
        $this->birthDate = $birthDate;
        $this->hospitals = $hospitals;
        $this->isActive = true;
        $this->createdAt = new \DateTimeImmutable();
        $this->protocols = new ArrayCollection();
        $this->rodoConsents = new ArrayCollection();
        $this->attachments = new ArrayCollection();

        if ($protocol->isEqualsTo(Protocol::FOLLOWUP)) {
            throw PatientCannotBeCreated::becauseFollowUpProtocolCannotBeAddedToPatient();
        }

        $this->addPatientProtocol(PatientProtocol::create(Uuid::generate(), $this, $protocol));
        $this->protocols->add(PatientProtocol::create(Uuid::generate(), $this, Protocol::FOLLOWUP));
    }

    /** @param array<string> $hospitals */
    public static function create(
        Uuid $patientId,
        PatientPublicId $patientPublicId,
        string $firstName,
        string $lastName,
        Citizenship $citizenship,
        bool $isRegisteredAddressSameAsResidence,
        bool $isCorrespondenceAddressSameAsResidence,
        Address $residenceAddress,
        ?Address $registeredAddress,
        ?Address $correspondenceAddress,
        ?string $email,
        ?string $contactNumber,
        bool $isPatientIdentified,
        ?Pesel $pesel,
        ?PassportNumber $passportNumber,
        ?Pesel $legalRepresentativePesel,
        ?PassportNumber $legalRepresentativePassportNumber,
        Gender $gender,
        BirthDate $birthDate,
        array $hospitals,
        Protocol $protocol,
    ): self {
        return new self(
            $patientId,
            $patientPublicId,
            $firstName,
            $lastName,
            $citizenship,
            $isRegisteredAddressSameAsResidence,
            $isCorrespondenceAddressSameAsResidence,
            $residenceAddress,
            $registeredAddress,
            $correspondenceAddress,
            $email,
            $contactNumber,
            $isPatientIdentified,
            $pesel,
            $passportNumber,
            $legalRepresentativePesel,
            $legalRepresentativePassportNumber,
            $gender,
            $birthDate,
            $hospitals,
            $protocol
        );
    }

    public function update(
        string $firstName,
        string $lastName,
        Citizenship $citizenship,
        bool $isRegisteredAddressSameAsResidence,
        bool $isCorrespondenceAddressSameAsResidence,
        Address $residenceAddress,
        ?Address $registeredAddress,
        ?Address $correspondenceAddress,
        ?string $email,
        ?string $contactNumber,
        bool $isPatientIdentified,
        ?Pesel $pesel,
        ?PassportNumber $passportNumber,
        ?Pesel $legalRepresentativePesel,
        ?PassportNumber $legalRepresentativePassportNumber,
        Gender $gender,
        BirthDate $birthDate,
    ): void {
        $this->validatePatientDataOrThrow(
            $citizenship,
            $isPatientIdentified,
            $pesel,
            $birthDate,
            $gender,
            $legalRepresentativePesel,
            $legalRepresentativePassportNumber,
            $passportNumber,
            $isRegisteredAddressSameAsResidence,
            $registeredAddress,
            $isCorrespondenceAddressSameAsResidence,
            $correspondenceAddress,
        );

        if ($isPatientIdentified) {
            $legalRepresentativePesel = null;
            $legalRepresentativePassportNumber = null;
        } else {
            $pesel = null;
            $passportNumber = null;
        }

        if ($isRegisteredAddressSameAsResidence) {
            $registeredAddress = null;
        }

        if ($isCorrespondenceAddressSameAsResidence) {
            $correspondenceAddress = null;
        }

        $this->firstName = $firstName;
        $this->lastName = $lastName;
        $this->citizenship = $citizenship;
        $this->isRegisteredAddressSameAsResidence = $isRegisteredAddressSameAsResidence;
        $this->isCorrespondenceAddressSameAsResidence = $isCorrespondenceAddressSameAsResidence;
        $this->residenceAddress = $residenceAddress;
        $this->registeredAddress = $registeredAddress;
        $this->correspondenceAddress = $correspondenceAddress;
        $this->email = $email;
        $this->contactNumber = $contactNumber;
        $this->isPatientIdentified = $isPatientIdentified;
        $this->pesel = $pesel;
        $this->passportNumber = $passportNumber;
        $this->legalRepresentativePesel = $legalRepresentativePesel;
        $this->legalRepresentativePassportNumber = $legalRepresentativePassportNumber;
        $this->gender = $gender;
        $this->birthDate = $birthDate;
        $this->updatedAt = new \DateTimeImmutable();
    }

    private function validatePatientDataOrThrow(Citizenship $citizenship, bool $isPatientIdentified, ?Pesel $pesel,
        BirthDate $birthDate, Gender $gender, ?Pesel $legalRepresentativePesel, ?PassportNumber $legalRepresentativePassportNumber,
        ?PassportNumber $passportNumber,
        bool $isRegisteredAddressSameAsResidence, ?Address $registeredAddress,
        bool $isCorrespondenceAddressSameAsResidence, ?Address $correspondenceAddress,
    ): void {
        if ($citizenship->isEqualsTo(Citizenship::PL)) {
            if ($isPatientIdentified) {
                if ($pesel === null) {
                    throw PatientCannotBeChanged::becausePeselIsRequiredForPolishCitizenship();
                }

                if ($passportNumber !== null) {
                    throw PatientCannotBeChanged::becausePassportNumberShouldNotBeFilledInIfPatientIsPolishCitizenshipAndHasPesel();
                }

                if ($birthDate->birthDateString() !== $pesel->birthDate()->birthDateString()) {
                    throw PatientCannotBeChanged::becauseBirthDateMustMatchPesel();
                }

                if ($gender->value !== $pesel->gender()->value) {
                    throw PatientCannotBeChanged::becauseGenderMustMatchPesel();
                }
            }

            if (!$isPatientIdentified && $legalRepresentativePesel === null) {
                throw PatientCannotBeChanged::becauseLegalRepresentativePeselIsRequiredForPolishCitizenship();
            }
        }

        if (!$citizenship->isEqualsTo(Citizenship::PL)) {
            if ($isPatientIdentified) {
                if ($pesel === null && $passportNumber === null) {
                    throw PatientCannotBeChanged::becausePeselOrPassportIsRequiredForNonPolishCitizenship();
                }

                if ($pesel !== null && $birthDate->birthDateString() !== $pesel->birthDate()->birthDateString()) {
                    throw PatientCannotBeChanged::becauseBirthDateMustMatchPesel();
                }

                if ($pesel !== null && $gender->value !== $pesel->gender()->value) {
                    throw PatientCannotBeChanged::becauseGenderMustMatchPesel();
                }
            }

            if (!$isPatientIdentified && ($legalRepresentativePesel === null && $legalRepresentativePassportNumber === null)) {
                throw PatientCannotBeChanged::becausePeselOrPassportIsRequiredForNonPolishCitizenship();
            }
        }

        if ($isRegisteredAddressSameAsResidence && $registeredAddress !== null) {
            throw PatientCannotBeChanged::becauseRegisteredAddressCannotBeDefinedIfSameAsResidence();
        }

        if ($isCorrespondenceAddressSameAsResidence && $correspondenceAddress !== null) {
            throw PatientCannotBeChanged::becauseCorrespondenceAddressCannotBeDefinedIfSameAsResidence();
        }

        if (!$isRegisteredAddressSameAsResidence && $registeredAddress === null) {
            throw PatientCannotBeChanged::becauseRegisteredAddressCannotBeEmptyIfDifferentFromResidence();
        }

        if (!$isCorrespondenceAddressSameAsResidence && $correspondenceAddress === null) {
            throw PatientCannotBeChanged::becauseCorrespondenceAddressCannotBeEmptyIfDifferentFromResidence();
        }
    }

    public function updateDeceasedStatus(bool $isDeceased): void
    {
        $this->isDeceased = $isDeceased;
        $this->updatedAt = new \DateTimeImmutable();
    }

    public function updateFollowUpDateOfCompletionForm(?\DateTimeImmutable $completionDate): void
    {
        $this->followUpDateOfCompletionForm = $completionDate;
        $this->updatedAt = new \DateTimeImmutable();
    }

    public function addHospital(Uuid $hospitalId): void
    {
        if (!in_array($hospitalId->toString(), $this->hospitals, true)) {
            $this->hospitals[] = $hospitalId->toString();
            $this->hospitals = array_values($this->hospitals); // resetowanie kluczy
            $this->updatedAt = new \DateTimeImmutable();
        } else {
            throw HospitalCannotBeAddedToPatient::becauseHospitalAlreadyExists($hospitalId->toString(), $this->patientId->toString());
        }
    }

    public function removeHospital(Uuid $hospitalId): void
    {
        $key = array_search($hospitalId->toString(), $this->hospitals, true);
        if ($key !== false) {
            unset($this->hospitals[$key]);
            $this->hospitals = array_values($this->hospitals); // resetowanie kluczy
            $this->updatedAt = new \DateTimeImmutable();
        } else {
            throw HospitalCannotBeRemovedFromPatient::becauseHospitalDoesNotExist($hospitalId->toString(), $this->patientId->toString());
        }

        if (empty($this->hospitals)) {
            throw HospitalCannotBeRemovedFromPatient::becausePatientMustHaveAtLeastOneHospital($this->patientId->toString());
        }
    }

    public function updatePatientProtocolIcd10Code(Uuid $patientProtocolId, ?string $icd10Code): void
    {
        $filteredProtocols = $this->protocols->filter(function (PatientProtocol $protocol) use ($patientProtocolId) {
            return $protocol->patientProtocolId()->equals($patientProtocolId);
        });

        if ($filteredProtocols->isEmpty()) {
            throw PatientProtocolCannotBeUpdated::becauseDoesNotExist($this->patientId->toString(), $patientProtocolId->toString());
        }

        $protocolToUpdate = $filteredProtocols->first();
        $protocolToUpdate->setIcd10Code($icd10Code);

        $this->updatedAt = new \DateTimeImmutable();
    }

    public function updatePatientProtocolTreatmentStartDate(Uuid $patientProtocolId, ?\DateTimeImmutable $treatmentStartDate): void
    {
        $filteredProtocols = $this->protocols->filter(function (PatientProtocol $protocol) use ($patientProtocolId) {
            return $protocol->patientProtocolId()->equals($patientProtocolId);
        });

        if ($filteredProtocols->isEmpty()) {
            throw PatientProtocolCannotBeUpdated::becauseDoesNotExist($this->patientId->toString(), $patientProtocolId->toString());
        }

        $protocolToUpdate = $filteredProtocols->first();
        $protocolToUpdate->setStartTreatment($treatmentStartDate);

        $this->updatedAt = new \DateTimeImmutable();
    }

    public function activate(): void
    {
        $this->isActive = true;
        $this->deactivationReason = null;
        $this->updatedAt = new \DateTimeImmutable();
    }

    public function deactivate(string $reason): void
    {
        $this->isActive = false;
        $this->deactivationReason = $reason;
        $this->updatedAt = new \DateTimeImmutable();
    }

    public function addPatientProtocol(PatientProtocol $patientProtocol): void
    {
        if ($patientProtocol->protocol()->isEqualsTo(Protocol::FOLLOWUP)) {
            return;
        }

        $this->protocols->add($patientProtocol);
        $this->updatedAt = new \DateTimeImmutable();
    }

    public function addPatientRodoConsent(PatientRodoConsent $create): void
    {
        if (!$this->rodoConsents->contains($create)) {
            $this->rodoConsents->add($create);
            $this->updatedAt = new \DateTimeImmutable();
        }
    }

    public function activatePatientProtocol(Uuid $patientProtocolId): void
    {
        foreach ($this->protocols as $protocol) {
            if ($protocol->patientProtocolId()->equals($patientProtocolId)) {
                if ($protocol->isActive()) {
                    throw PatientProtocolCannotBeActivated::becauseIsAlreadyActivated($this->patientId->toString(), $patientProtocolId->toString());
                }

                $protocol->activate();

                return;
            }
        }

        $this->updatedAt = new \DateTimeImmutable();

        throw PatientProtocolCannotBeActivated::becauseDoesNotExist($this->patientId->toString(), $patientProtocolId->toString());
    }

    public function deactivatePatientProtocol(Uuid $patientProtocolId): void
    {
        $activeProtocols = $this->protocols->filter(function (PatientProtocol $protocol) {
            return $protocol->isActive() && !$protocol->protocol()->isEqualsTo(Protocol::FOLLOWUP);
        });

        if (count($activeProtocols) <= 1) {
            throw PatientProtocolCannotBeDeactivated::becauseAtLeastOnePatientProtocolMustBeActive($this->patientId->toString());
        }

        foreach ($this->protocols as $protocol) {
            if ($protocol->patientProtocolId()->equals($patientProtocolId)) {
                if (!$protocol->isActive()) {
                    throw PatientProtocolCannotBeDeactivated::becauseIsAlreadyDeactivated($this->patientId->toString(), $patientProtocolId->toString());
                }

                $protocol->deactivate();

                return;
            }
        }

        $this->updatedAt = new \DateTimeImmutable();

        throw PatientProtocolCannotBeDeactivated::becauseDoesNotExist($this->patientId->toString(), $patientProtocolId->toString());
    }

    public function addRodoConsent(PatientRodoConsent $rodoConsent): void
    {
        if (!$this->rodoConsents->contains($rodoConsent)) {
            $this->rodoConsents->add($rodoConsent);
            $this->updatedAt = new \DateTimeImmutable();
        }
    }

    public function getPatientRodoConsent(Uuid $patientRodoConsentId): PatientRodoConsent
    {
        foreach ($this->rodoConsents as $rodoConsent) {
            if ($rodoConsent->patientRodoConsentId()->equals($patientRodoConsentId)) {
                return $rodoConsent;
            }
        }

        throw PatientRodoConsentNotFound::becauseDoesNotExist($patientRodoConsentId->toString());
    }

    public function addPatientAttachment(PatientAttachment $attachment): void
    {
        $attachmentId = $attachment->patientAttachmentId()->toString();
        if (!$this->attachments->containsKey($attachmentId)) {
            $this->attachments->set($attachmentId, $attachment);
            $this->updatedAt = new \DateTimeImmutable();
        }
    }

    public function removePatientAttachment(Uuid $patientAttachmentId): void
    {
        $attachmentIdString = $patientAttachmentId->toString();
        if ($this->attachments->containsKey($attachmentIdString)) {
            $this->attachments->remove($attachmentIdString);
            $this->updatedAt = new \DateTimeImmutable();
        } else {
            throw PatientAttachmentNotFound::becauseDoesNotExist($attachmentIdString);
        }
    }

    public function getPatientAttachment(Uuid $patientAttachmentId): PatientAttachment
    {
        $attachmentIdString = $patientAttachmentId->toString();
        $attachment = $this->attachments->get($attachmentIdString);

        if ($attachment === null) {
            throw PatientAttachmentNotFound::becauseDoesNotExist($attachmentIdString);
        }

        return $attachment;
    }

    /** @return array<string, string> */
    public function logDetails(): array
    {
        return [
            'firstName' => $this->firstName,
            'lastName' => $this->lastName,
        ];
    }

    public function patientId(): Uuid
    {
        return $this->patientId;
    }

    public function isActive(): bool
    {
        return $this->isActive;
    }

    /** @return Collection<int, PatientProtocol> */
    public function protocols(): Collection
    {
        return $this->protocols;
    }

    /** @return Collection<int, PatientRodoConsent> */
    public function rodoConsent(): Collection
    {
        return $this->rodoConsents;
    }

    /** @return Collection<string, PatientAttachment> */
    public function attachments(): Collection
    {
        return $this->attachments;
    }

    /** @return array<string> */
    public function hospitals(): array
    {
        return $this->hospitals;
    }
}
