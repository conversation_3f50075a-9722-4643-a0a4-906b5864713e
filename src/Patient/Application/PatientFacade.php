<?php

declare(strict_types=1);

namespace App\Patient\Application;

use App\BackendForFrontend\Security\Patient\Dto\AddPatientAttachmentDto;
use App\BackendForFrontend\Security\Patient\Dto\CreatePatientDto;
use App\BackendForFrontend\Security\Patient\Dto\UpdatePatientDto;
use App\Common\Query\GridConfiguration;
use App\Common\Query\GridResult;
use App\Common\Uuid;
use App\Patient\Application\Query\PatientQueryInterface;
use App\Patient\Application\Query\PatientView;
use App\Patient\Application\Service\PatientService;
use App\Patient\Application\Service\PeselService;
use App\Patient\Domain\PeselDetails;

class PatientFacade
{
    public function __construct(
        private PatientService $patientService,
        private PeselService $peselService,
        private PatientQueryInterface $patientQuery)
    {
    }

    public function create(string $patientId, CreatePatientDto $dto): void
    {
        $this->patientService->create($patientId, $dto);
    }

    public function update(string $patientId, UpdatePatientDto $dto): void
    {
        $this->patientService->update($patientId, $dto);
    }

    public function addHospital(string $patientId, string $hospitalId): void
    {
        $this->patientService->addHospital($patientId, $hospitalId);
    }

    public function removeHospital(string $patientId, string $hospitalId): void
    {
        $this->patientService->removeHospital($patientId, $hospitalId);
    }

    public function activate(string $patientId): void
    {
        $this->patientService->activate($patientId);
    }

    public function deactivate(string $patientId, string $reason): void
    {
        $this->patientService->deactivate($patientId, $reason);
    }

    public function addPatientProtocol(string $patientId, string $patientProtocolId, string $protocol): void
    {
        $this->patientService->addPatientProtocol($patientId, $patientProtocolId, $protocol);
    }

    public function addPatientRodoConsent(string $patientId, string $patientRodoConsentId, string $fileName,
        string $extension, string $fileContentBase64): void
    {
        $this->patientService->addPatientRodoConsent(
            $patientId,
            $patientRodoConsentId,
            $fileName,
            $extension,
            $fileContentBase64
        );
    }

    public function activatePatientProtocol(string $patientId, string $protocol): void
    {
        $this->patientService->activatePatientProtocol($patientId, $protocol);
    }

    public function deactivatePatientProtocol(string $patientId, string $protocol): void
    {
        $this->patientService->deactivatePatientProtocol($patientId, $protocol);
    }

    public function updatePatientProtocolIcd10Code(string $patientId, string $patientProtocolId, string $icd10Code): void
    {
        $this->patientService->updatePatientProtocolIcd10Code(
            $patientId,
            $patientProtocolId,
            $icd10Code
        );
    }

    public function updatePatientProtocolStartTreatment(string $patientId, string $patientProtocolId,
        ?\DateTimeImmutable $treatmentStartDate): void
    {
        $this->patientService->updatePatientProtocolTreatmentStartDate(
            $patientId,
            $patientProtocolId,
            $treatmentStartDate
        );
    }

    public function updateDecreaseStatus(string $patientId, bool $isDeceased): void
    {
        $this->patientService->updateDecreaseStatus($patientId, $isDeceased);
    }

    public function updateFollowUpDateOfCompletionForm(string $patientId, ?\DateTimeImmutable $completionDate): void
    {
        $this->patientService->updateFollowUpDateOfCompletionForm($patientId, $completionDate);
    }

    public function getPeselDetails(string $pesel): PeselDetails
    {
        return $this->peselService->getPeselDetails($pesel);
    }

    public function findByPatientId(string $patientId): ?PatientView
    {
        return $this->patientQuery->findByPatientId($patientId);
    }

    /** @return array<PatientView> */
    public function findAll(?\DateTimeImmutable $changedSince = null): array
    {
        return $this->patientQuery->findAll($changedSince);
    }

    public function findByPatientProtocolId(string $patientProtocolId): ?PatientView
    {
        return $this->patientQuery->findByPatientProtocolId($patientProtocolId);
    }

    /**
     * @param array<string>|null $onlyAllowHospitals
     * @param array<string>|null $onlyAllowProtocols
     *
     * @return GridResult<string, PatientView>
     */
    public function findForGrid(GridConfiguration $gridConfiguration, ?array $onlyAllowHospitals, ?array $onlyAllowProtocols): GridResult
    {
        return $this->patientQuery->findForGrid($gridConfiguration, $onlyAllowHospitals, $onlyAllowProtocols);
    }

    public function getPatientRodoConsentFilePath(string $patientId, string $patientRodoConsentId): string
    {
        return $this->patientService->getPatientRodoConsentFilePath($patientId, $patientRodoConsentId);
    }

    public function addPatientAttachment(string $patientId, AddPatientAttachmentDto $dto): string
    {
        $patientAttachmentId = Uuid::generate()->toString();
        $this->patientService->addPatientAttachment(
            $patientId,
            $patientAttachmentId,
            $dto->type,
            $dto->fileName,
            $dto->extension,
            $dto->fileContentBase64
        );

        return $patientAttachmentId;
    }

    public function getPatientAttachmentFilePath(string $patientId, string $patientAttachmentId): string
    {
        return $this->patientService->getPatientAttachmentFilePath($patientId, $patientAttachmentId);
    }

    public function deletePatientAttachment(string $patientId, string $patientAttachmentId): void
    {
        $this->patientService->deletePatientAttachment($patientId, $patientAttachmentId);
    }
}
