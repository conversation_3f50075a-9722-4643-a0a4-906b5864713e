<?php

declare(strict_types=1);

namespace App\Patient\Application\Service;

use App\BackendForFrontend\Security\Patient\Dto\CreatePatientDto;
use App\BackendForFrontend\Security\Patient\Dto\UpdatePatientDto;
use App\Common\Uuid;
use App\Patient\Application\Exception\PatientCannotBeActivated;
use App\Patient\Application\Exception\PatientCannotBeCreated;
use App\Patient\Application\Exception\PatientCannotBeDeactivated;
use App\Patient\Application\Query\HospitalQueryInterface;
use App\Patient\Domain\Address;
use App\Patient\Domain\BirthDate;
use App\Patient\Domain\Citizenship;
use App\Patient\Domain\Gender;
use App\Patient\Domain\PassportNumber;
use App\Patient\Domain\Patient;
use App\Patient\Domain\PatientAttachment;
use App\Patient\Domain\PatientAttachmentType;
use App\Patient\Domain\PatientProtocol;
use App\Patient\Domain\PatientRepositoryInterface;
use App\Patient\Domain\PatientRodoConsent;
use App\Patient\Domain\Pesel;
use App\SharedKernel\Protocol;

readonly class PatientService
{
    public function __construct(
        private PatientRepositoryInterface $patientRepository,
        private PatientPublicIdGenerator $patientPublicIdGenerator,
        private HospitalQueryInterface $hospitalQuery,
        private PatientRodoConsentFileService $patientRodoConsentFileService,
        private PatientAttachmentFileService $patientAttachmentFileService,
    ) {
    }

    public function create(string $patientId, CreatePatientDto $dto): void
    {
        $patientPublicId = $this->patientPublicIdGenerator->generate($dto->firstName, $dto->lastName);
        $residenceAddress = $this->createResidenceAddress($dto);
        $registeredAddress = $this->createRegisteredAddress($dto);
        $correspondenceAddress = $this->createCorrespondenceAddress($dto);
        $pesel = $this->createPesel($dto->pesel);
        $passportNumber = $this->createPassportNumber($dto->passportNumber);
        $legalRepresentativePesel = $this->createPesel($dto->legalRepresentativePesel);
        $legalRepresentativePassportNumber = $this->createPassportNumber($dto->legalRepresentativePassportNumber);

        if (!$this->validateHospitals($dto->hospitals)) {
            throw PatientCannotBeCreated::becauseOneOrMoreHospitalsDoNotExist();
        }

        $patient = Patient::create(
            Uuid::fromString($patientId),
            $patientPublicId,
            $dto->firstName,
            $dto->lastName,
            Citizenship::from($dto->citizenship),
            $dto->isRegisteredAddressSameAsResidence,
            $dto->isCorrespondenceAddressSameAsResidence,
            $residenceAddress,
            $registeredAddress,
            $correspondenceAddress,
            $dto->email,
            $dto->contactNumber,
            $dto->isPatientIdentified,
            $pesel,
            $passportNumber,
            $legalRepresentativePesel,
            $legalRepresentativePassportNumber,
            Gender::from($dto->gender),
            BirthDate::createFromString($dto->birthDate),
            $dto->hospitals,
            Protocol::from($dto->protocol),
        );

        if ($pesel !== null) {
            $this->throwIfPeselAlreadyExists($pesel);
        }

        if ($passportNumber !== null) {
            $this->throwIfPassportNumberAlreadyExists($passportNumber);
        }

        $this->patientRepository->add($patient);

        $this->addPatientRodoConsent(
            $patientId,
            Uuid::generate()->toString(),
            $dto->rodoConsent->fileName,
            $dto->rodoConsent->extension,
            $dto->rodoConsent->fileContentBase64
        );
    }

    public function update(string $patientId, UpdatePatientDto $dto): void
    {
        $residenceAddress = $this->createResidenceAddress($dto);
        $registeredAddress = $this->createRegisteredAddress($dto);
        $correspondenceAddress = $this->createCorrespondenceAddress($dto);
        $pesel = $this->createPesel($dto->pesel);
        $passportNumber = $this->createPassportNumber($dto->passportNumber);
        $legalRepresentativePesel = $this->createPesel($dto->legalRepresentativePesel);
        $legalRepresentativePassportNumber = $this->createPassportNumber($dto->legalRepresentativePassportNumber);

        $patient = $this->patientRepository->getByPatientId(Uuid::fromString($patientId));

        $patient->update(
            $dto->firstName,
            $dto->lastName,
            Citizenship::from($dto->citizenship),
            $dto->isRegisteredAddressSameAsResidence,
            $dto->isCorrespondenceAddressSameAsResidence,
            $residenceAddress,
            $registeredAddress,
            $correspondenceAddress,
            $dto->email,
            $dto->contactNumber,
            $dto->isPatientIdentified,
            $pesel,
            $passportNumber,
            $legalRepresentativePesel,
            $legalRepresentativePassportNumber,
            Gender::from($dto->gender),
            BirthDate::createFromString($dto->birthDate)
        );

        if ($pesel !== null) {
            $this->throwIfPeselAlreadyExists($pesel, Uuid::fromString($patientId));
        }

        if ($passportNumber !== null) {
            $this->throwIfPassportNumberAlreadyExists($passportNumber, Uuid::fromString($patientId));
        }

        $this->patientRepository->update($patient);
    }

    public function addHospital(string $patientId, string $hospitalId): void
    {
        $patient = $this->patientRepository->getByPatientId(Uuid::fromString($patientId));
        $patient->addHospital(Uuid::fromString($hospitalId));

        $this->patientRepository->update($patient);
    }

    public function removeHospital(string $patientId, string $hospitalId): void
    {
        $patient = $this->patientRepository->getByPatientId(Uuid::fromString($patientId));
        $patient->removeHospital(Uuid::fromString($hospitalId));

        $this->patientRepository->update($patient);
    }

    public function activate(string $patientId): void
    {
        $patient = $this->patientRepository->getByPatientId(Uuid::fromString($patientId));

        if ($patient->isActive()) {
            throw PatientCannotBeActivated::becauseIsAlreadyActive();
        }

        $patient->activate();

        $this->patientRepository->update($patient);
    }

    public function deactivate(string $patientId, string $reason): void
    {
        $patient = $this->patientRepository->getByPatientId(Uuid::fromString($patientId));

        if (!$patient->isActive()) {
            throw PatientCannotBeDeactivated::becauseIsAlreadyInactive();
        }

        $patient->deactivate($reason);

        $this->patientRepository->update($patient);
    }

    public function addPatientProtocol(string $patientId, string $patientProtocolId, string $protocol): void
    {
        $patient = $this->patientRepository->getByPatientId(Uuid::fromString($patientId));
        $patient->addPatientProtocol(PatientProtocol::create(
            Uuid::fromString($patientProtocolId),
            $patient,
            Protocol::from($protocol))
        );

        $this->patientRepository->update($patient);
    }

    public function addPatientRodoConsent(string $patientId, string $patientRodoConsentId, string $fileName, string $extension, string $fileContentBase64): void
    {
        $patient = $this->patientRepository->getByPatientId(Uuid::fromString($patientId));
        $sanitizedFileName = PatientRodoConsentFileService::sanitizeFileName($fileName);

        $patientRodoConsent = PatientRodoConsent::create(
            Uuid::fromString($patientRodoConsentId),
            $sanitizedFileName,
            $extension,
            $patient
        );

        $patient->addPatientRodoConsent($patientRodoConsent);
        $this->patientRepository->update($patient);

        $this->patientRodoConsentFileService->upload(
            $patientId,
            $patientRodoConsentId,
            $extension,
            $fileContentBase64
        );
    }

    public function getPatientRodoConsentFilePath(string $patientId, string $patientRodoConsentId): string
    {
        $patient = $this->patientRepository->getByPatientId(Uuid::fromString($patientId));
        $patientRodoConsent = $patient->getPatientRodoConsent(Uuid::fromString($patientRodoConsentId));

        return $this->patientRodoConsentFileService->getFilePath(
            $patientId,
            $patientRodoConsentId,
            $patientRodoConsent->extension()
        );
    }

    public function activatePatientProtocol(string $patientId, string $patientProtocolId): void
    {
        $patient = $this->patientRepository->getByPatientId(Uuid::fromString($patientId));
        $patient->activatePatientProtocol(Uuid::fromString($patientProtocolId));

        $this->patientRepository->update($patient);
    }

    public function deactivatePatientProtocol(string $patientId, string $patientProtocolId): void
    {
        $patient = $this->patientRepository->getByPatientId(Uuid::fromString($patientId));
        $patient->deactivatePatientProtocol(Uuid::fromString($patientProtocolId));

        $this->patientRepository->update($patient);
    }

    public function updatePatientProtocolIcd10Code(string $patientId, string $patientProtocolId, ?string $icd10Code): void
    {
        $patient = $this->patientRepository->getByPatientId(Uuid::fromString($patientId));
        $patient->updatePatientProtocolIcd10Code(
            Uuid::fromString($patientProtocolId),
            $icd10Code
        );

        $this->patientRepository->update($patient);
    }

    public function updatePatientProtocolTreatmentStartDate(string $patientId, string $patientProtocolId,
        ?\DateTimeImmutable $treatmentStartDate): void
    {
        $patient = $this->patientRepository->getByPatientId(Uuid::fromString($patientId));
        $patient->updatePatientProtocolTreatmentStartDate(
            Uuid::fromString($patientProtocolId),
            $treatmentStartDate
        );

        $this->patientRepository->update($patient);
    }

    public function updateDecreaseStatus(string $patientId, bool $isDeceased): void
    {
        $patient = $this->patientRepository->getByPatientId(Uuid::fromString($patientId));
        $patient->updateDeceasedStatus($isDeceased);

        $this->patientRepository->update($patient);
    }

    public function addPatientAttachment(string $patientId, string $patientAttachmentId, string $type, string $fileName, string $extension, string $fileContentBase64): void
    {
        $patient = $this->patientRepository->getByPatientId(Uuid::fromString($patientId));
        $sanitizedFileName = PatientAttachmentFileService::sanitizeFileName($fileName);

        $patientAttachment = PatientAttachment::create(
            Uuid::fromString($patientAttachmentId),
            $patient,
            PatientAttachmentType::from($type),
            $sanitizedFileName,
            $extension
        );

        $patient->addPatientAttachment($patientAttachment);
        $this->patientRepository->update($patient);

        $this->patientAttachmentFileService->upload(
            $patientId,
            $patientAttachmentId,
            $extension,
            $fileContentBase64
        );
    }

    public function getPatientAttachmentFilePath(string $patientId, string $patientAttachmentId): string
    {
        $patient = $this->patientRepository->getByPatientId(Uuid::fromString($patientId));
        $patientAttachment = $patient->getPatientAttachment(Uuid::fromString($patientAttachmentId));

        return $this->patientAttachmentFileService->getFilePath(
            $patientId,
            $patientAttachmentId,
            $patientAttachment->extension()
        );
    }

    public function deletePatientAttachment(string $patientId, string $patientAttachmentId): void
    {
        $patient = $this->patientRepository->getByPatientId(Uuid::fromString($patientId));
        $patientAttachment = $patient->getPatientAttachment(Uuid::fromString($patientAttachmentId));

        $patient->removePatientAttachment(Uuid::fromString($patientAttachmentId));
        $this->patientRepository->update($patient);

        $this->patientAttachmentFileService->delete(
            $patientId,
            $patientAttachmentId,
            $patientAttachment->extension()
        );
    }

    public function updateFollowUpDateOfCompletionForm(string $patientId, ?\DateTimeImmutable $completionDate): void
    {
        $patient = $this->patientRepository->getByPatientId(Uuid::fromString($patientId));
        $patient->updateFollowUpDateOfCompletionForm($completionDate);

        $this->patientRepository->update($patient);
    }

    private function createResidenceAddress(CreatePatientDto|UpdatePatientDto $dto): Address
    {
        return Address::create(
            $dto->residenceAddress->streetWithNumber,
            $dto->residenceAddress->city,
            $dto->residenceAddress->postCode,
        );
    }

    private function createRegisteredAddress(CreatePatientDto|UpdatePatientDto $dto): ?Address
    {
        if ($dto->isRegisteredAddressSameAsResidence || $dto->registeredAddress === null) {
            $registeredAddress = null;
        } else {
            $registeredAddress = Address::create(
                $dto->registeredAddress->streetWithNumber,
                $dto->registeredAddress->city,
                $dto->registeredAddress->postCode,
            );
        }

        return $registeredAddress;
    }

    private function createCorrespondenceAddress(CreatePatientDto|UpdatePatientDto $dto): ?Address
    {
        if ($dto->isCorrespondenceAddressSameAsResidence || $dto->correspondenceAddress === null) {
            $correspondenceAddress = null;
        } else {
            $correspondenceAddress = Address::create(
                $dto->correspondenceAddress->streetWithNumber,
                $dto->correspondenceAddress->city,
                $dto->correspondenceAddress->postCode,
            );
        }

        return $correspondenceAddress;
    }

    private function createPesel(?string $pesel): ?Pesel
    {
        return $pesel !== null ? Pesel::create($pesel) : null;
    }

    private function createPassportNumber(?string $passportNumber): ?PassportNumber
    {
        return $passportNumber !== null ? PassportNumber::create($passportNumber) : null;
    }

    private function throwIfPeselAlreadyExists(Pesel $pesel, ?Uuid $excludePatientId = null): void
    {
        if ($this->patientRepository->findByPesel($pesel, $excludePatientId) !== null) {
            throw PatientCannotBeCreated::becausePeselAlreadyExists($pesel->pesel());
        }
    }

    private function throwIfPassportNumberAlreadyExists(PassportNumber $passportNumber, ?Uuid $excludePatientId = null): void
    {
        if ($this->patientRepository->findByPassportNumber($passportNumber, $excludePatientId) !== null) {
            throw PatientCannotBeCreated::becausePassportNumberAlreadyExists($passportNumber->passportNumber());
        }
    }

    /** @param array<string> $hospitals */
    private function validateHospitals(array $hospitals): bool
    {
        $hospitalIds = $this->hospitalQuery->findAllActiveHospitalSelect();

        foreach ($hospitals as $hospitalId) {
            if (!$hospitalIds->containsKey($hospitalId)) {
                return false;
            }
        }

        return true;
    }
}
