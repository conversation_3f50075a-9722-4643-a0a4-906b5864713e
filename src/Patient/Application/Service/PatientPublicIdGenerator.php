<?php

declare(strict_types=1);

namespace App\Patient\Application\Service;

use App\Patient\Domain\PatientPublicId;
use App\Patient\Domain\PatientRepositoryInterface;

readonly class PatientPublicIdGenerator
{
    public function __construct(private PatientRepositoryInterface $patientRepository)
    {
    }

    public function generate(string $firstName, string $lastName): PatientPublicId
    {
        do {
            $patientPublicId = PatientPublicId::create($firstName, $lastName);
            $patientPublicIdExists = $this->patientRepository->patientPublicIdExists($patientPublicId);
        } while ($patientPublicIdExists);

        return $patientPublicId;
    }
}
