<?php

declare(strict_types=1);

namespace App\Patient\Application\Service;

use App\Common\Uuid;
use App\Patient\Domain\FavoritePatient;
use App\Patient\Domain\FavoritePatientRepositoryInterface;

readonly class FavoritePatientService
{
    public function __construct(
        private FavoritePatientRepositoryInterface $favoritePatientRepository,
    ) {
    }

    public function addFavoritePatient(string $authUserId, string $patientId): void
    {
        if ($this->findFavoritePatient($authUserId, $patientId) === null) {
            $favoritePatient = new FavoritePatient(
                Uuid::fromString($authUserId),
                Uuid::fromString($patientId)
            );
            $this->favoritePatientRepository->add($favoritePatient);
        }
    }

    public function removeFavoritePatient(string $authUserId, string $patientId): void
    {
        $favoritePatient = $this->findFavoritePatient($authUserId, $patientId);
        if ($favoritePatient !== null) {
            $this->favoritePatientRepository->remove($favoritePatient);
        }
    }

    private function findFavoritePatient(string $authUserId, string $patientId): ?FavoritePatient
    {
        return $this->favoritePatientRepository->findByAuthUserAndPatient(
            Uuid::fromString($authUserId),
            Uuid::fromString($patientId)
        );
    }
}
