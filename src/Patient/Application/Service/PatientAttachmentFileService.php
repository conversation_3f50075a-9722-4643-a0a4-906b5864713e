<?php

declare(strict_types=1);

namespace App\Patient\Application\Service;

use Symfony\Component\Filesystem\Exception\IOException;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\String\Slugger\AsciiSlugger;

readonly class PatientAttachmentFileService
{
    private const DIRECTORY_PERMISSION = 0o775;

    public function __construct(
        private ParameterBagInterface $parameterBag,
        private Filesystem $filesystem,
    ) {
    }

    public function upload(string $patientId, string $patientAttachmentId, string $extension, string $base64Content): void
    {
        /** @var string|false $fileContent */
        $fileContent = base64_decode($base64Content);
        if (!is_string($fileContent)) {
            throw new \RuntimeException('Nie udało się zdekodować zawartości pliku z base64');
        }

        $patientDir = $this->parameterBag->getPatientAttachmentUploadDir($patientId);
        $filePath = $patientDir.'/'.$patientAttachmentId.'.'.$extension;

        try {
            $this->filesystem->mkdir($patientDir, self::DIRECTORY_PERMISSION);
            $this->filesystem->dumpFile($filePath, $fileContent);
        } catch (IOException $exception) {
            throw new \RuntimeException(sprintf('Nie udało się zapisać pliku: "%s". Powód: %s', $filePath, $exception->getMessage()), 0, $exception);
        }
    }

    public function getFilePath(string $patientId, string $patientAttachmentId, string $extension): string
    {
        $patientDir = $this->parameterBag->getPatientAttachmentUploadDir($patientId);

        return $patientDir.'/'.$patientAttachmentId.'.'.$extension;
    }

    public function delete(string $patientId, string $patientAttachmentId, string $extension): void
    {
        $filePath = $this->getFilePath($patientId, $patientAttachmentId, $extension);

        try {
            $this->filesystem->remove($filePath);

            // Opcjonalnie: usuń katalog pacjenta, jeśli jest pusty
            $patientDir = $this->parameterBag->getPatientAttachmentUploadDir($patientId);
            if ($this->filesystem->exists($patientDir) && is_dir($patientDir)) {
                $files = scandir($patientDir);
                if ($files !== false && count($files) == 2) {
                    $this->filesystem->remove($patientDir);
                }
            }
        } catch (IOException $exception) {
            throw new \RuntimeException(sprintf('Nie udało się usunąć pliku: "%s". Powód: %s', $filePath, $exception->getMessage()), 0, $exception);
        }
    }

    public static function sanitizeFileName(string $fileName): string
    {
        $slugger = new AsciiSlugger();
        // Używamy basename do pobrania nazwy pliku, a następnie sluggera
        $safeFilename = $slugger->slug(pathinfo($fileName, PATHINFO_FILENAME))->toString();

        return $safeFilename;
    }
}
