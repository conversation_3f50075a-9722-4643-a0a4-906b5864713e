<?php

declare(strict_types=1);

namespace App\Patient\Application\Service;

use Symfony\Component\Filesystem\Exception\IOException;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\String\Slugger\AsciiSlugger;

readonly class PatientRodoConsentFileService
{
    private const DIRECTORY_PERMISSION = 0o775;

    public function __construct(
        private ParameterBagInterface $parameterBag,
        private Filesystem $filesystem,
    ) {
    }

    public function upload(string $patientId, string $patientRodoConsentId, string $extension, string $base64Content): void
    {
        /** @var string|false $fileContent */
        $fileContent = base64_decode($base64Content);
        if (!is_string($fileContent)) {
            throw new \RuntimeException('Nie udało się zdekodować zawartości pliku z base64');
        }

        $projectDir = $this->parameterBag->getPatientRodoConsentUploadDir($patientId);
        $filePath = $projectDir.'/'.$patientRodoConsentId.'.'.$extension;

        try {
            $this->filesystem->mkdir($projectDir, self::DIRECTORY_PERMISSION);
            $this->filesystem->dumpFile($filePath, $fileContent);
        } catch (IOException $exception) {
            throw new \RuntimeException(sprintf('Nie udało się zapisać pliku: "%s". Powód: %s', $filePath, $exception->getMessage()), 0, $exception);
        }
    }

    public function getFilePath(string $patientId, string $patientRodoConsentId, string $extension): string
    {
        $projectDir = $this->parameterBag->getPatientRodoConsentUploadDir($patientId);

        return $projectDir.'/'.$patientRodoConsentId.'.'.$extension;
    }

    public static function sanitizeFileName(string $fileName): string
    {
        $slugger = new AsciiSlugger();
        // Używamy basename do pobrania nazwy pliku, a następnie sluggera
        $safeFilename = $slugger->slug(pathinfo($fileName, PATHINFO_FILENAME))->toString();

        return $safeFilename;
    }
}
