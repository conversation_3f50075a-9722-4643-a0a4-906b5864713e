<?php

declare(strict_types=1);

namespace App\Patient\Application\Service;

use App\Common\Uuid;
use App\Hospital\Application\HospitalFacade;
use App\Patient\Application\Exception\CannotCreateCorrectionFlag; // Use specific exception
use App\Patient\Application\PatientFacade;
use App\Patient\Domain\PatientCorrectionFlag;
use App\Patient\Domain\PatientCorrectionFlagRepositoryInterface;

final readonly class CorrectionFlagService
{
    public function __construct(
        private PatientCorrectionFlagRepositoryInterface $flagRepository,
        private HospitalFacade $hospitalFacade, // To validate hospital existence
        private PatientFacade $patientFacade,   // To validate patient existence
    ) {
    }

    public function createFlag(string $patientId, string $hospitalId, string $comment): string
    {
        $patientUuid = Uuid::fromString($patientId);
        $hospitalUuid = Uuid::fromString($hospitalId);

        if ($this->hospitalFacade->findByHospitalId($hospitalId) === null) {
            throw CannotCreateCorrectionFlag::becauseHospitalNotFound($hospitalId);
        }
        if ($this->patientFacade->findByPatientId($patientId) === null) {
            throw CannotCreateCorrectionFlag::becausePatientNotFound($patientId);
        }

        $flag = new PatientCorrectionFlag(
            patientId: $patientUuid,
            hospitalId: $hospitalUuid,
            comment: $comment
        );

        $this->flagRepository->save($flag);

        return $flag->id()->toString();
    }

    public function resolveFlag(string $flagId, string $resolverUserId): void
    {
        $flagUuid = Uuid::fromString($flagId);
        $resolverUuid = Uuid::fromString($resolverUserId);

        $flag = $this->flagRepository->getById($flagUuid);

        $flag->resolve($resolverUuid);
        $this->flagRepository->save($flag);
    }
}
