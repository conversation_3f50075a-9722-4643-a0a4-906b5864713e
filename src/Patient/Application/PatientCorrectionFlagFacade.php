<?php

declare(strict_types=1);

namespace App\Patient\Application;

use App\Common\Query\GridConfiguration;
use App\Common\Query\GridResult;
use App\Patient\Application\Query\PatientCorrectionFlagQueryInterface;
use App\Patient\Application\Query\PatientCorrectionFlagView;
use App\Patient\Application\Service\CorrectionFlagService;

final readonly class PatientCorrectionFlagFacade
{
    public function __construct(
        private CorrectionFlagService $correctionFlagService,
        private PatientCorrectionFlagQueryInterface $correctionFlagQuery,
    ) {
    }

    /** @see CorrectionFlagService::createFlag() */
    public function createFlag(string $patientId, string $hospitalId, string $comment): string
    {
        return $this->correctionFlagService->createFlag($patientId, $hospitalId, $comment);
    }

    /** @see CorrectionFlagService::resolveFlag() */
    public function resolveFlag(string $flagId, string $resolverUserId): void
    {
        $this->correctionFlagService->resolveFlag($flagId, $resolverUserId);
    }

    /**
     * @see PatientCorrectionFlagQueryInterface::findForGrid()
     *
     * @param array<string> $onlyAllowHospitals
     * @param array<string> $onlyAllowProtocols
     *
     * @return GridResult<string, PatientCorrectionFlagView>
     */
    public function findForGrid(GridConfiguration $gridConfiguration, array $onlyAllowHospitals, array $onlyAllowProtocols): GridResult
    {
        return $this->correctionFlagQuery->findForGrid($gridConfiguration, $onlyAllowHospitals, $onlyAllowProtocols);
    }

    /** @see PatientCorrectionFlagQueryInterface::findById() */
    public function findById(string $flagId): ?PatientCorrectionFlagView
    {
        return $this->correctionFlagQuery->findById($flagId);
    }

    /** @return iterable<PatientCorrectionFlagView> */
    public function findAll(?\DateTimeImmutable $createdSince = null): iterable
    {
        return $this->correctionFlagQuery->findAll($createdSince);
    }
}
