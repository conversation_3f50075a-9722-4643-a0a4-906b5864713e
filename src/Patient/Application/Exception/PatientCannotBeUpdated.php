<?php

declare(strict_types=1);

namespace App\Patient\Application\Exception;

use App\Common\Exception\ApplicationException;

class PatientCannotBeUpdated extends ApplicationException
{
    public static function becausePeselAlreadyExists(string $pesel): self
    {
        return new self(sprintf('Pacjent z numerem PESEL %s już istnieje.', $pesel), 409);
    }

    public static function becausePassportNumberAlreadyExists(string $passportNumber): self
    {
        return new self(sprintf('Pacjent z numerem paszportu %s już istnieje.', $passportNumber), 409);
    }
}
