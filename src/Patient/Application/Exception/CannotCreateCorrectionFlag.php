<?php

declare(strict_types=1);

namespace App\Patient\Application\Exception;

use App\Common\Exception\ApplicationException;

class CannotCreateCorrectionFlag extends ApplicationException
{
    public static function becauseHospitalNotFound(string $hospitalId): self
    {
        return new self(sprintf('Nie można utworzyć flagi korekty: Szpital o identyfikatorze "%s" nie został znaleziony.', $hospitalId));
    }

    public static function becausePatientNotFound(string $patientId): self
    {
        return new self(sprintf('Nie można utworzyć flagi korekty: Pacjent o identyfikatorze "%s" nie został znaleziony.', $patientId));
    }
}
