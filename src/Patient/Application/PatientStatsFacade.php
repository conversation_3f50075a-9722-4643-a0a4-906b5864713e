<?php

declare(strict_types=1);

namespace App\Patient\Application;

use App\Patient\Application\Query\Stats\PatientsCountByGenderView;
use App\Patient\Application\Query\Stats\PatientsCountByHospitalView;
use App\Patient\Application\Query\Stats\PatientsCountByProtocolTypeView;
use App\Patient\Application\Query\Stats\PatientsCountByProtocolView;
use App\Patient\Application\Query\Stats\PatientStatsQueryInterface;
use Doctrine\Common\Collections\Collection;

final readonly class PatientStatsFacade
{
    public function __construct(
        private PatientStatsQueryInterface $patientStatsQuery,
    ) {
    }

    /** @return Collection<int, int> */
    public function getUniqueTreatmentStartYears(): Collection
    {
        return $this->patientStatsQuery->getUniqueTreatmentStartYears();
    }

    /** @return Collection<int, PatientsCountByHospitalView> */
    public function countOfPatientsByHospitals(?int $year = null): Collection
    {
        return $this->patientStatsQuery->countOfPatientsByHospitals($year);
    }

    /** @return Collection<int, PatientsCountByGenderView> */
    public function countOfPatientsByGender(?int $year = null): Collection
    {
        return $this->patientStatsQuery->countOfPatientsByGender($year);
    }

    /** @return Collection<int, PatientsCountByProtocolView> */
    public function countOfPatientsByProtocols(?int $year = null): Collection
    {
        return $this->patientStatsQuery->countOfPatientsByProtocols($year);
    }

    /** @return Collection<int, PatientsCountByProtocolTypeView> */
    public function countAllProtocolsByProtocolType(?int $year = null): Collection
    {
        return $this->patientStatsQuery->countAllProtocolsByProtocolType($year);
    }

    /**
     * @param array<string>|null $onlyAllowHospitals
     * @param array<string>|null $onlyAllowProtocols
     */
    public function countOfVisiblePatientsForAccount(?array $onlyAllowHospitals, ?array $onlyAllowProtocols): int
    {
        return $this->patientStatsQuery->countOfVisiblePatientsForAccount(
            $onlyAllowHospitals,
            $onlyAllowProtocols
        );
    }
}
