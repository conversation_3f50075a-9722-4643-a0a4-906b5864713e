<?php

declare(strict_types=1);

namespace App\Patient\Application;

use App\Patient\Application\Query\PatientQueryInterface;

readonly class PatientHospitalFacade
{
    public function __construct(
        private PatientQueryInterface $patientQuery,
    ) {
    }

    public function hospitalIsUsed(string $hospitalId): bool
    {
        return $this->patientQuery->hospitalIsUsed($hospitalId);
    }
}
