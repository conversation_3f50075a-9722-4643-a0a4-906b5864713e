<?php

declare(strict_types=1);

// Corrected namespace

namespace App\Patient\Application\Query;

use App\Common\QueryView;

final readonly class PatientCorrectionFlagView implements QueryView
{
    public function __construct(
        public string $flagId,
        public string $patientId,
        public string $patientPublicId,
        public string $patientFirstName,
        public string $patientLastName,
        public string $hospitalId,
        public string $comment,
        public \DateTimeImmutable $createdAt,
        public ?\DateTimeImmutable $resolvedAt = null,
        public ?string $resolvedById = null,
    ) {
    }

    /** @param array<string, mixed> $data Raw data from DB query */
    public static function deserialize(array $data): self
    {
        return new self(
            flagId: (string) $data['flagId'],
            patientId: (string) $data['patientId'],
            patientPublicId: (string) $data['patientPublicId'],
            patientFirstName: (string) $data['patientFirstName'],
            patientLastName: (string) $data['patientLastName'],
            hospitalId: (string) $data['hospitalId'],
            comment: (string) $data['comment'],
            createdAt: new \DateTimeImmutable((string) $data['createdAt']),
            resolvedAt: isset($data['resolvedAt']) ? new \DateTimeImmutable((string) $data['resolvedAt']) : null,
            resolvedById: isset($data['resolvedById']) ? (string) $data['resolvedById'] : null
        );
    }

    /** @return array<string, mixed> */
    public function jsonSerialize(): array
    {
        return [
            'flagId' => $this->flagId,
            'patientId' => $this->patientId,
            'patientPublicId' => $this->patientPublicId,
            'patientFirstName' => $this->patientFirstName,
            'patientLastName' => $this->patientLastName,
            'hospitalId' => $this->hospitalId,
            'comment' => $this->comment,
            'createdAt' => $this->createdAt->format('Y-m-d H:i:s'),
            'resolvedAt' => $this->resolvedAt?->format('Y-m-d H:i:s'),
            'resolvedById' => $this->resolvedById,
        ];
    }
}
