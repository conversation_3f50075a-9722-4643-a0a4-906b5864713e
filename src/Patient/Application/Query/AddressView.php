<?php

declare(strict_types=1);

namespace App\Patient\Application\Query;

use App\Common\QueryView;

class AddressView implements QueryView
{
    private string $streetWithNumber;
    private string $city;
    private string $postCode;

    public function __construct(
        string $streetWithNumber,
        string $city,
        string $postCode,
    ) {
        $this->streetWithNumber = $streetWithNumber;
        $this->city = $city;
        $this->postCode = $postCode;
    }

    /** @param array<string, string> $data */
    public static function deserialize(array $data): self
    {
        return new self(
            $data['streetWithNumber'],
            $data['city'],
            $data['postCode']
        );
    }

    /** @return array<string, string> */
    public function jsonSerialize(): array
    {
        return [
            'streetWithNumber' => $this->streetWithNumber,
            'city' => $this->city,
            'postCode' => $this->postCode,
        ];
    }

    public function streetWithNumber(): string
    {
        return $this->streetWithNumber;
    }

    public function city(): string
    {
        return $this->city;
    }

    public function postCode(): string
    {
        return $this->postCode;
    }
}
