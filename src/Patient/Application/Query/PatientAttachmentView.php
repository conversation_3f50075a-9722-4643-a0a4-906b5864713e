<?php

declare(strict_types=1);

namespace App\Patient\Application\Query;

use App\Common\QueryView;

class PatientAttachmentView implements QueryView
{
    private string $patientAttachmentId;
    private string $type;
    private string $fileName;
    private string $extension;
    private \DateTimeImmutable $createdAt;

    public function __construct(
        string $patientAttachmentId,
        string $type,
        string $fileName,
        string $extension,
        \DateTimeImmutable $createdAt,
    ) {
        $this->patientAttachmentId = $patientAttachmentId;
        $this->type = $type;
        $this->fileName = $fileName;
        $this->extension = $extension;
        $this->createdAt = $createdAt;
    }

    /** @param array<string, mixed> $data */
    public static function deserialize(array $data): self
    {
        return new self(
            $data['patientAttachmentId'],
            $data['type'],
            $data['fileName'],
            $data['extension'],
            new \DateTimeImmutable($data['createdAt']),
        );
    }

    /** @return array<string, mixed> */
    public function jsonSerialize(): array
    {
        return [
            'patientAttachmentId' => $this->patientAttachmentId,
            'type' => $this->type,
            'fileName' => $this->fileName,
            'extension' => $this->extension,
            'createdAt' => $this->createdAt->format('Y-m-d H:i:s'),
        ];
    }

    public function patientAttachmentId(): string
    {
        return $this->patientAttachmentId;
    }

    public function type(): string
    {
        return $this->type;
    }

    public function fileName(): string
    {
        return $this->fileName;
    }

    public function extension(): string
    {
        return $this->extension;
    }

    public function createdAt(): \DateTimeImmutable
    {
        return $this->createdAt;
    }
}
