<?php

declare(strict_types=1);

namespace App\Patient\Application\Query;

use App\Common\QueryView;

class PatientProtocolView implements QueryView
{
    private string $patientProtocolId;
    private string $protocol;
    private bool $isActive;
    private ?string $icd10Code;
    private ?\DateTimeImmutable $treatmentStartDate;

    public function __construct(
        string $patientProtocolId,
        string $protocol,
        bool $isActive,
        ?string $icd10Code,
        ?\DateTimeImmutable $treatmentStartDate,
    ) {
        $this->patientProtocolId = $patientProtocolId;
        $this->protocol = $protocol;
        $this->isActive = $isActive;
        $this->icd10Code = $icd10Code;
        $this->treatmentStartDate = $treatmentStartDate;
    }

    /** @param array<string, mixed> $data */
    public static function deserialize(array $data): self
    {
        return new self(
            $data['patientProtocolId'],
            $data['protocol'],
            (bool) $data['isActive'],
            $data['icd10Code'],
            isset($data['treatmentStartDate']) ? new \DateTimeImmutable($data['treatmentStartDate']) : null,
        );
    }

    /** @return array<string, mixed> */
    public function jsonSerialize(): array
    {
        return [
            'patientProtocolId' => $this->patientProtocolId,
            'protocol' => $this->protocol,
            'isActive' => $this->isActive,
            'icd10Code' => $this->icd10Code,
            'treatmentStartDate' => $this->treatmentStartDate?->format('Y-m-d H:i:s'),
        ];
    }

    public function patientProtocolId(): string
    {
        return $this->patientProtocolId;
    }

    public function protocol(): string
    {
        return $this->protocol;
    }

    public function isActive(): bool
    {
        return $this->isActive;
    }

    public function icd10Code(): ?string
    {
        return $this->icd10Code;
    }

    public function treatmentStartDate(): ?\DateTimeImmutable
    {
        return $this->treatmentStartDate;
    }
}
