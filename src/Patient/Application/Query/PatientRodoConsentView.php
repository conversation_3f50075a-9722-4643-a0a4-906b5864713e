<?php

declare(strict_types=1);

namespace App\Patient\Application\Query;

use App\Common\QueryView;

class PatientRodoConsentView implements QueryView
{
    private string $patientRodoConsentId;
    private string $fileName;
    private string $extension;
    private \DateTimeImmutable $createdAt;

    public function __construct(
        string $patientRodoConsentId,
        string $fileName,
        string $extension,
        \DateTimeImmutable $createdAt,
    ) {
        $this->patientRodoConsentId = $patientRodoConsentId;
        $this->fileName = $fileName;
        $this->extension = $extension;
        $this->createdAt = $createdAt;
    }

    /** @param array<string, mixed> $data */
    public static function deserialize(array $data): self
    {
        return new self(
            $data['patientRodoConsentId'],
            $data['fileName'],
            $data['extension'],
            new \DateTimeImmutable($data['createdAt']),
        );
    }

    /** @return array<string, mixed> */
    public function jsonSerialize(): array
    {
        return [
            'patientRodoConsentId' => $this->patientRodoConsentId,
            'fileName' => $this->fileName,
            'extension' => $this->extension,
            'createdAt' => $this->createdAt->format('Y-m-d H:i:s'),
        ];
    }

    public function patientRodoConsentId(): string
    {
        return $this->patientRodoConsentId;
    }

    public function fileName(): string
    {
        return $this->fileName;
    }

    public function extension(): string
    {
        return $this->extension;
    }

    public function createdAt(): \DateTimeImmutable
    {
        return $this->createdAt;
    }
}
