<?php

declare(strict_types=1);

namespace App\Patient\Application\Query;

use App\Common\Query\GridConfiguration;
use App\Common\Query\GridResult;

interface FavoritePatientQueryInterface
{
    public function isPatientFavorite(string $authUserId, string $patientId): bool;

    /**
     * @param array<string>|null $onlyAllowHospitals
     * @param array<string>|null $onlyAllowProtocols
     *
     * @return GridResult<string, FavoritePatientGridView>
     */
    public function findForGrid(GridConfiguration $gridConfiguration, string $authUserId, ?array $onlyAllowHospitals, ?array $onlyAllowProtocols): GridResult;
}
