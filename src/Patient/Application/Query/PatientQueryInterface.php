<?php

declare(strict_types=1);

namespace App\Patient\Application\Query;

use App\Common\Query\GridConfiguration;
use App\Common\Query\GridResult;

interface PatientQueryInterface
{
    public function findByPatientId(string $patientId): ?PatientView;

    /** @return array<PatientView> */
    public function findAll(?\DateTimeImmutable $changedSince = null): array;

    public function findByPatientProtocolId(string $patientProtocolId): ?PatientView;

    /**
     * @param array<string>|null $onlyAllowHospitals
     * @param array<string>|null $onlyAllowProtocols
     *
     * @return GridResult<string, PatientView>
     */
    public function findForGrid(GridConfiguration $gridConfiguration, ?array $onlyAllowHospitals, ?array $onlyAllowProtocols): GridResult;

    public function hospitalIsUsed(string $hospitalId): bool;
}
