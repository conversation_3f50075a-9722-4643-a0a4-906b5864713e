<?php

declare(strict_types=1);

namespace App\Patient\Application\Query;

use App\Common\QueryView;

class FavoritePatientGridView implements QueryView
{
    private string $patientId;
    private string $patientPublicId;
    private string $firstName;
    private string $lastName;

    public function __construct(
        string $patientId,
        string $patientPublicId,
        string $firstName,
        string $lastName,
    ) {
        $this->patientId = $patientId;
        $this->patientPublicId = $patientPublicId;
        $this->firstName = $firstName;
        $this->lastName = $lastName;
    }

    /** @param array<string, string> $data */
    public static function deserialize(array $data): self
    {
        return new self(
            $data['patientId'],
            $data['patientPublicId'],
            $data['firstName'],
            $data['lastName']
        );
    }

    /** @return array<string, string> */
    public function jsonSerialize(): array
    {
        return [
            'patientId' => $this->patientId,
            'patientPublicId' => $this->patientPublicId,
            'firstName' => $this->firstName,
            'lastName' => $this->lastName,
        ];
    }

    public function patientId(): string
    {
        return $this->patientId;
    }

    public function patientPublicId(): string
    {
        return $this->patientPublicId;
    }

    public function firstName(): string
    {
        return $this->firstName;
    }

    public function lastName(): string
    {
        return $this->lastName;
    }
}
