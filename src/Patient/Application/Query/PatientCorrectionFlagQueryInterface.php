<?php

declare(strict_types=1);

// Corrected namespace

namespace App\Patient\Application\Query;

use App\Common\Query\GridConfiguration;
use App\Common\Query\GridResult;

interface PatientCorrectionFlagQueryInterface
{
    /**
     * @param array<string> $onlyAllowHospitals hospitals the user has access to
     * @param array<string> $onlyAllowProtocols protocols the user has access to
     *
     * @return GridResult<string, PatientCorrectionFlagView> grid result keyed by flag ID
     */
    public function findForGrid(GridConfiguration $gridConfiguration, array $onlyAllowHospitals, array $onlyAllowProtocols): GridResult;

    public function findById(string $flagId): ?PatientCorrectionFlagView;

    /**
     * Finds all correction flags, optionally filtering by creation date.
     *
     * @param \DateTimeImmutable|null $createdSince if provided, only flags created on or after this date are returned
     *
     * @return iterable<PatientCorrectionFlagView> an iterable collection of flag views
     */
    public function findAll(?\DateTimeImmutable $createdSince = null): iterable;
}
