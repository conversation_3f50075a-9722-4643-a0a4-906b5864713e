<?php

declare(strict_types=1);

namespace App\Patient\Application\Query;

use App\Common\QueryView;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;

class PatientView implements QueryView
{
    private string $patientId;
    private string $patientPublicId;
    private string $firstName;
    private string $lastName;
    private string $citizenship;
    private bool $isRegisteredAddressSameAsResidence;
    private bool $isCorrespondenceAddressSameAsResidence;
    private AddressView $residenceAddress;
    private ?AddressView $registeredAddress;
    private ?AddressView $correspondenceAddress;
    private ?string $email;
    private ?string $contactNumber;
    private bool $isPatientIdentified;
    private ?string $pesel;
    private ?string $passportNumber;
    private ?string $legalRepresentativePesel;
    private ?string $legalRepresentativePassportNumber;
    private string $gender;
    private string $birthDate;
    /** @var array<string> */
    private array $hospitals;
    /** @var Collection<int, PatientProtocolView> */
    private Collection $protocols;
    private bool $isActive;
    private ?string $deactivationReason;
    private bool $isDeceased;
    private \DateTimeImmutable $createdAt;
    private ?\DateTimeImmutable $updatedAt;
    /** @var Collection<int, PatientRodoConsentView> */
    private Collection $rodoConsents;
    /** @var Collection<int, PatientAttachmentView> */
    private Collection $attachments;
    private ?\DateTimeImmutable $followUpDateOfCompletionForm;

    /** @param array<string> $hospitals */
    public function __construct(
        string $patientId,
        string $patientPublicId,
        string $firstName,
        string $lastName,
        string $citizenship,
        bool $isRegisteredAddressSameAsResidence,
        bool $isCorrespondenceAddressSameAsResidence,
        AddressView $residenceAddress,
        ?AddressView $registeredAddress,
        ?AddressView $correspondenceAddress,
        ?string $email,
        ?string $contactNumber,
        bool $isPatientIdentified,
        ?string $pesel,
        ?string $passportNumber,
        ?string $legalRepresentativePesel,
        ?string $legalRepresentativePassportNumber,
        string $gender,
        string $birthDate,
        array $hospitals,
        bool $isActive,
        ?string $deactivationReason,
        bool $isDeceased,
        \DateTimeImmutable $createdAt,
        ?\DateTimeImmutable $updatedAt,
        ?\DateTimeImmutable $followUpDateOfCompletionForm,
    ) {
        $this->patientId = $patientId;
        $this->patientPublicId = $patientPublicId;
        $this->firstName = $firstName;
        $this->lastName = $lastName;
        $this->citizenship = $citizenship;
        $this->isRegisteredAddressSameAsResidence = $isRegisteredAddressSameAsResidence;
        $this->isCorrespondenceAddressSameAsResidence = $isCorrespondenceAddressSameAsResidence;
        $this->residenceAddress = $residenceAddress;
        $this->registeredAddress = $registeredAddress;
        $this->correspondenceAddress = $correspondenceAddress;
        $this->email = $email;
        $this->contactNumber = $contactNumber;
        $this->isPatientIdentified = $isPatientIdentified;
        $this->pesel = $pesel;
        $this->passportNumber = $passportNumber;
        $this->legalRepresentativePesel = $legalRepresentativePesel;
        $this->legalRepresentativePassportNumber = $legalRepresentativePassportNumber;
        $this->gender = $gender;
        $this->birthDate = $birthDate;
        $this->hospitals = $hospitals;
        $this->isActive = $isActive;
        $this->deactivationReason = $deactivationReason;
        $this->isDeceased = $isDeceased;
        $this->createdAt = $createdAt;
        $this->updatedAt = $updatedAt;
        $this->followUpDateOfCompletionForm = $followUpDateOfCompletionForm;

        $this->rodoConsents = new ArrayCollection();
        $this->protocols = new ArrayCollection();
        $this->attachments = new ArrayCollection();
    }

    /** @param array<string, mixed> $data */
    public static function deserialize(array $data): self
    {
        $patientView = new self(
            $data['patientId'],
            $data['patientPublicId'],
            $data['firstName'],
            $data['lastName'],
            $data['citizenship'],
            (bool) $data['isRegisteredAddressSameAsResidence'],
            (bool) $data['isCorrespondenceAddressSameAsResidence'],
            AddressView::deserialize($data['residenceAddress']),
            (bool) $data['isRegisteredAddressSameAsResidence'] === false ? AddressView::deserialize($data['registeredAddress']) : null,
            (bool) $data['isCorrespondenceAddressSameAsResidence'] === false ? AddressView::deserialize($data['correspondenceAddress']) : null,
            $data['email'],
            $data['contactNumber'],
            (bool) $data['isPatientIdentified'],
            $data['pesel_pesel'],
            $data['passportNumber_passportNumber'],
            $data['legalRepresentativePesel_pesel'],
            $data['legalRepresentativePassportNumber_passportNumber'],
            $data['gender'],
            $data['birthDate'],
            json_decode($data['hospitals'], true),
            (bool) $data['isActive'],
            $data['deactivationReason'],
            (bool) $data['isDeceased'],
            new \DateTimeImmutable($data['createdAt']),
            isset($data['updatedAt']) ? new \DateTimeImmutable($data['updatedAt']) : null,
            isset($data['followUpDateOfCompletionForm']) ? new \DateTimeImmutable($data['followUpDateOfCompletionForm']) : null
        );

        foreach ($data['rodoConsents'] as $rodoConsent) {
            $patientView->rodoConsents->add(PatientRodoConsentView::deserialize($rodoConsent));
        }

        foreach ($data['protocols'] as $protocol) {
            $patientView->protocols->add(PatientProtocolView::deserialize($protocol));
        }

        foreach ($data['attachments'] as $attachment) {
            $attachmentView = PatientAttachmentView::deserialize($attachment);
            $patientView->attachments->add($attachmentView);
        }

        return $patientView;
    }

    /** @return array<string, mixed> */
    public function jsonSerialize(): array
    {
        return [
            'patientId' => $this->patientId,
            'patientPublicId' => $this->patientPublicId,
            'firstName' => $this->firstName,
            'lastName' => $this->lastName,
            'citizenship' => $this->citizenship,
            'isRegisteredAddressSameAsResidence' => $this->isRegisteredAddressSameAsResidence,
            'isCorrespondenceAddressSameAsResidence' => $this->isCorrespondenceAddressSameAsResidence,
            'residenceAddress' => $this->residenceAddress,
            'registeredAddress' => $this->registeredAddress,
            'correspondenceAddress' => $this->correspondenceAddress,
            'email' => $this->email,
            'contactNumber' => $this->contactNumber,
            'isPatientIdentified' => $this->isPatientIdentified,
            'pesel' => $this->pesel,
            'passportNumber' => $this->passportNumber,
            'legalRepresentativePesel' => $this->legalRepresentativePesel,
            'legalRepresentativePassportNumber' => $this->legalRepresentativePassportNumber,
            'gender' => $this->gender,
            'birthDate' => $this->birthDate,
            'hospitals' => $this->hospitals,
            'protocols' => $this->protocols->toArray(),
            'isActive' => $this->isActive,
            'deactivationReason' => $this->deactivationReason,
            'isDeceased' => $this->isDeceased,
            'createdAt' => $this->createdAt->format('Y-m-d H:i:s'),
            'updatedAt' => $this->updatedAt ? $this->updatedAt->format('Y-m-d H:i:s') : null,
            'rodoConsents' => $this->rodoConsents->toArray(),
            'attachments' => $this->attachments->toArray(),
            'followUpDateOfCompletionForm' => $this->followUpDateOfCompletionForm ? $this->followUpDateOfCompletionForm->format('Y-m-d') : null,
        ];
    }

    public function patientId(): string
    {
        return $this->patientId;
    }

    public function firstName(): string
    {
        return $this->firstName;
    }

    public function lastName(): string
    {
        return $this->lastName;
    }

    /** @return array<string> */
    public function hospitals(): array
    {
        return $this->hospitals;
    }

    /** @return Collection<int, PatientProtocolView> */
    public function protocols(): Collection
    {
        return $this->protocols;
    }

    /** @return array<string> */
    public function protocolsValueList(): array
    {
        $data = [];

        foreach ($this->protocols as $protocol) {
            $data[] = $protocol->protocol();
        }

        return $data;
    }

    /** @return Collection<int, PatientRodoConsentView> */
    public function rodoConsents(): Collection
    {
        return $this->rodoConsents;
    }

    /** @return Collection<int, PatientAttachmentView> */
    public function attachments(): Collection
    {
        return $this->attachments;
    }
}
