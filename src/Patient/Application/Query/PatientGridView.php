<?php

declare(strict_types=1);

namespace App\Patient\Application\Query;

use App\Common\QueryView;

class PatientGridView implements QueryView
{
    private string $patientId;
    private string $patientPublicId;
    private string $firstName;
    private string $lastName;
    private string $gender;
    private string $birthDate;
    /** @var array<string> */
    private array $hospitals;
    /** @var array<string> */
    private array $protocols;
    private bool $isActive;
    private bool $isDeceased;
    private ?string $followUpDateOfCompletionForm;

    /**
     * @param array<string> $protocols
     * @param array<string> $hospitals
     */
    public function __construct(
        string $patientId,
        string $patientPublicId,
        string $firstName,
        string $lastName,
        string $gender,
        string $birthDate,
        array $protocols,
        array $hospitals,
        bool $isActive,
        bool $isDeceased,
        ?string $followUpDateOfCompletionForm,
    ) {
        $this->patientId = $patientId;
        $this->patientPublicId = $patientPublicId;
        $this->firstName = $firstName;
        $this->lastName = $lastName;
        $this->gender = $gender;
        $this->birthDate = $birthDate;
        $this->hospitals = $hospitals;
        $this->protocols = $protocols;
        $this->isActive = $isActive;
        $this->isDeceased = $isDeceased;
        $this->followUpDateOfCompletionForm = $followUpDateOfCompletionForm;
    }

    /** @param array<string, mixed> $data */
    public static function deserialize(array $data): self
    {
        return new self(
            $data['patientId'],
            $data['patientPublicId'],
            $data['firstName'],
            $data['lastName'],
            $data['gender'],
            $data['birthDate'],
            json_decode($data['protocols']),
            json_decode($data['hospitals'], true),
            (bool) $data['isActive'],
            (bool) $data['isDeceased'],
            $data['followUpDateOfCompletionForm'] ?? null
        );
    }

    /** @return array<string, mixed> */
    public function jsonSerialize(): array
    {
        return [
            'patientId' => $this->patientId,
            'patientPublicId' => $this->patientPublicId,
            'firstName' => $this->firstName,
            'lastName' => $this->lastName,
            'gender' => $this->gender,
            'birthDate' => $this->birthDate,
            'hospitals' => $this->hospitals,
            'protocols' => $this->protocols,
            'isActive' => $this->isActive,
            'isDeceased' => $this->isDeceased,
            'followUpDateOfCompletionForm' => $this->followUpDateOfCompletionForm,
        ];
    }
}
