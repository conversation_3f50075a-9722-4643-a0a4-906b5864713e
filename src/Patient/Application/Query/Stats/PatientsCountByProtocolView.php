<?php

declare(strict_types=1);

namespace App\Patient\Application\Query\Stats;

use App\Common\QueryView;
use App\SharedKernel\Protocol;

final class PatientsCountByProtocolView implements QueryView
{
    private Protocol $protocol;
    private int $count;

    public function __construct(Protocol $protocol, int $count)
    {
        $this->protocol = $protocol;
        $this->count = $count;
    }

    /** @param array<string, mixed> $data */
    public static function deserialize(array $data): QueryView
    {
        return new self(
            Protocol::from($data['protocol']),
            $data['count']
        );
    }

    public function jsonSerialize(): mixed
    {
        return [
            'protocol' => $this->protocol->value,
            'count' => $this->count,
        ];
    }

    public function protocol(): Protocol
    {
        return $this->protocol;
    }

    public function count(): int
    {
        return $this->count;
    }
}
