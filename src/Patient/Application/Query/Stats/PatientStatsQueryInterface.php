<?php

declare(strict_types=1);

namespace App\Patient\Application\Query\Stats;

use Doctrine\Common\Collections\Collection;

interface PatientStatsQueryInterface
{
    /** @return Collection<int, int> */
    public function getUniqueTreatmentStartYears(): Collection;

    /**
     * @param int|null $year Optional year to filter by treatment start date
     *
     * @return Collection<int, PatientsCountByHospitalView>
     */
    public function countOfPatientsByHospitals(?int $year = null): Collection;

    /**
     * @param int|null $year Optional year to filter by treatment start date
     *
     * @return Collection<int, PatientsCountByGenderView>
     */
    public function countOfPatientsByGender(?int $year = null): Collection;

    /**
     * @param int|null $year Optional year to filter by treatment start date
     *
     * @return Collection<int, PatientsCountByProtocolView>
     */
    public function countOfPatientsByProtocols(?int $year = null): Collection;

    /**
     * @param int|null $year Optional year to filter by treatment start date
     *
     * @return Collection<int, PatientsCountByProtocolTypeView>
     */
    public function countAllProtocolsByProtocolType(?int $year = null): Collection;

    /**
     * @param array<string>|null $onlyAllowHospitals
     * @param array<string>|null $onlyAllowProtocols
     */
    public function countOfVisiblePatientsForAccount(?array $onlyAllowHospitals, ?array $onlyAllowProtocols): int;
}
