<?php

declare(strict_types=1);

namespace App\Patient\Application\Query\Stats;

use App\Common\QueryView;

final class PatientsCountByHospitalView implements QueryView
{
    private string $hospital;
    private int $count;

    public function __construct(string $hospital, int $count)
    {
        $this->hospital = $hospital;
        $this->count = $count;
    }

    /** @param array<string, mixed> $data */
    public static function deserialize(array $data): QueryView
    {
        return new self(
            $data['hospital'],
            $data['count']
        );
    }

    public function jsonSerialize(): mixed
    {
        return [
            'hospital' => $this->hospital,
            'count' => $this->count,
        ];
    }

    public function hospital(): string
    {
        return $this->hospital;
    }

    public function count(): int
    {
        return $this->count;
    }
}
