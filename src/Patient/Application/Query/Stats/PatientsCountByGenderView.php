<?php

declare(strict_types=1);

namespace App\Patient\Application\Query\Stats;

use App\Common\QueryView;
use App\Patient\Domain\Gender;

final class PatientsCountByGenderView implements QueryView
{
    private Gender $gender;
    private int $count;

    public function __construct(Gender $gender, int $count)
    {
        $this->gender = $gender;
        $this->count = $count;
    }

    /** @param array<string, mixed> $data */
    public static function deserialize(array $data): QueryView
    {
        return new self(
            Gender::from($data['gender']),
            $data['count']
        );
    }

    public function jsonSerialize(): mixed
    {
        return [
            'gender' => $this->gender->value,
            'count' => $this->count,
        ];
    }

    public function gender(): Gender
    {
        return $this->gender;
    }

    public function count(): int
    {
        return $this->count;
    }
}
