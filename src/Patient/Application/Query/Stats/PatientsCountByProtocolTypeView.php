<?php

declare(strict_types=1);

namespace App\Patient\Application\Query\Stats;

use App\Common\QueryView;
use App\SharedKernel\ProtocolType;

final class PatientsCountByProtocolTypeView implements QueryView
{
    private ProtocolType $protocolType;
    private int $count;

    public function __construct(ProtocolType $protocolType, int $count)
    {
        $this->protocolType = $protocolType;
        $this->count = $count;
    }

    /** @param array{protocolType: string, count: int} $data */
    public static function deserialize(array $data): QueryView
    {
        return new self(
            ProtocolType::from($data['protocolType']),
            $data['count']
        );
    }

    /** @return array{protocolType: string, count: int} */
    public function jsonSerialize(): array
    {
        return [
            'protocolType' => $this->protocolType->value,
            'count' => $this->count,
        ];
    }

    public function protocolType(): ProtocolType
    {
        return $this->protocolType;
    }

    public function count(): int
    {
        return $this->count;
    }
}
