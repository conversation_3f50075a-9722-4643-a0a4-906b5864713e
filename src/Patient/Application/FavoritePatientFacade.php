<?php

declare(strict_types=1);

namespace App\Patient\Application;

use App\Common\Query\GridConfiguration;
use App\Common\Query\GridResult;
use App\Patient\Application\Query\FavoritePatientGridView;
use App\Patient\Application\Query\FavoritePatientQueryInterface;
use App\Patient\Application\Service\FavoritePatientService;

readonly class FavoritePatientFacade
{
    public function __construct(
        private FavoritePatientService $favoritePatientService,
        private FavoritePatientQueryInterface $favoritePatientQuery,
    ) {
    }

    public function addFavoritePatient(string $authUserId, string $patientId): void
    {
        $this->favoritePatientService->addFavoritePatient($authUserId, $patientId);
    }

    public function removeFavoritePatient(string $authUserId, string $patientId): void
    {
        $this->favoritePatientService->removeFavoritePatient($authUserId, $patientId);
    }

    public function isPatientFavorite(string $authUserId, string $patientId): bool
    {
        return $this->favoritePatientQuery->isPatientFavorite($authUserId, $patientId);
    }

    /**
     * @param array<string>|null $onlyAllowHospitals
     * @param array<string>|null $onlyAllowProtocols
     *
     * @return GridResult<string, FavoritePatientGridView>
     */
    public function findForGrid(GridConfiguration $gridConfiguration, string $authUserId, ?array $onlyAllowHospitals, ?array $onlyAllowProtocols): GridResult
    {
        return $this->favoritePatientQuery->findForGrid(
            $gridConfiguration,
            $authUserId,
            $onlyAllowHospitals,
            $onlyAllowProtocols
        );
    }
}
