<?php

declare(strict_types=1);

namespace App\Dictionary;

use App\Shared<PERSON>ernel\DictionaryType;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\Entity;

#[Entity]
#[ORM\Table(name: 'dictionary_dictionary_entry')]
#[ORM\Index(columns: ['searchableDescription', 'dictionaryType'])]
#[ORM\UniqueConstraint(columns: ['value', 'dictionaryType'])]
class DictionaryEntry
{
    #[ORM\Id]
    #[ORM\Column(type: 'integer', unique: true)]
    #[ORM\GeneratedValue]
    private int $dictionaryEntryId;

    #[ORM\Column(type: 'string', length: 50)]
    private string $value;

    #[ORM\Column(length: 500)]
    private string $description;

    #[ORM\Column(length: 500)]
    private string $searchableDescription;

    #[ORM\Column(length: 50, enumType: DictionaryType::class)]
    private DictionaryType $dictionaryType;

    private function __construct(string $value, string $description, string $searchableDescription, DictionaryType $dictionaryType)
    {
        $this->value = $value;
        $this->description = $description;
        $this->searchableDescription = $searchableDescription;
        $this->dictionaryType = $dictionaryType;
    }

    public static function create(string $value, string $description, string $searchableDescription, DictionaryType $dictionaryType): DictionaryEntry
    {
        return new DictionaryEntry($value, $description, $searchableDescription, $dictionaryType);
    }

    public function update(string $description, string $searchableDescription): void
    {
        $this->description = $description;
        $this->searchableDescription = $searchableDescription;
    }

    public function value(): string
    {
        return $this->value;
    }

    public function description(): string
    {
        return $this->description;
    }

    public function searchableDescription(): string
    {
        return $this->searchableDescription;
    }

    public function dictionaryType(): DictionaryType
    {
        return $this->dictionaryType;
    }
}
