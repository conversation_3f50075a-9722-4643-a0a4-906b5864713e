<?php

declare(strict_types=1);

namespace App\Dictionary\Query;

use App\Common\QueryView;

readonly class DictionaryEntryView implements QueryView
{
    public function __construct(
        private string $value,
        private string $description,
    ) {
    }

    #[\Override]
    public static function deserialize(array $data): QueryView
    {
        return new self(
            $data['value'],
            $data['description'],
        );
    }

    /** @return array<string, string> */
    #[\Override]
    public function jsonSerialize(): array
    {
        return [
            'label' => $this->description,
            'value' => $this->value,
        ];
    }
}
