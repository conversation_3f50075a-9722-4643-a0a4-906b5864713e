<?php

declare(strict_types=1);

namespace App\Dictionary\Query;

use App\Common\Query\GridConfiguration;
use App\Common\Query\GridResult;
use App\SharedKernel\DictionaryType;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Query\QueryBuilder;

readonly class DictionaryEntryQuery
{
    private const string TABLE_NAME = 'dictionary_dictionary_entry';

    public function __construct(
        private Connection $connection,
    ) {
    }

    public function dictionaryValueExists(string $value, DictionaryType $dictionaryType): bool
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('COUNT(*)')
            ->from(self::TABLE_NAME)
            ->andWhere('value = :value')
            ->andWhere('dictionaryType = :dictionaryType')
            ->setParameter('value', $value)
            ->setParameter('dictionaryType', $dictionaryType->value);

        return (bool) $queryBuilder->executeQuery()->fetchOne();
    }

    /** @return GridResult<string, DictionaryEntryView> */
    public function findForSelect(GridConfiguration $gridConfiguration, DictionaryType $dictionaryType): GridResult
    {
        $baseQueryBuilder = $this->prepareQueryForSelect($gridConfiguration, $dictionaryType);

        // Count total items for pagination
        $queryBuilderTotalItems = clone $baseQueryBuilder;
        $queryBuilderTotalItems->resetOrderBy()
            ->select('COUNT(DISTINCT (dictionaryEntryId))');
        $totalItems = (int) $this->connection->fetchOne($queryBuilderTotalItems->getSQL(), $queryBuilderTotalItems->getParameters());

        // Add pagination
        $queryBuilder = clone $baseQueryBuilder;
        $queryBuilder->setFirstResult(($gridConfiguration->page() - 1) * $gridConfiguration->itemsPerPage())
            ->setMaxResults($gridConfiguration->itemsPerPage());

        $data = $this->connection->fetchAllAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        $collection = new ArrayCollection();

        foreach ($data as $key => $row) {
            $collection->set($row['value'], DictionaryEntryView::deserialize($row));
        }

        return new GridResult($collection,
            $totalItems,
            $gridConfiguration->page(),
            $gridConfiguration->itemsPerPage()
        );
    }

    /** @return Collection<int, DictionaryEntryGridView> */
    public function findAll(): Collection
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('dictionaryEntryId', 'value', 'description', 'searchableDescription', 'dictionaryType')
            ->from(self::TABLE_NAME)
            ->addOrderBy('dictionaryType', 'ASC')
            ->addOrderBy('value', 'ASC');

        $data = $this->connection->fetchAllAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        $collection = new ArrayCollection();
        foreach ($data as $key => $row) {
            $collection->set($row['dictionaryEntryId'], DictionaryEntryGridView::deserialize($row));
        }

        return $collection;
    }

    /** @return GridResult<int, DictionaryEntryGridView> */
    public function findForGrid(GridConfiguration $gridConfiguration): GridResult
    {
        $baseQueryBuilder = $this->prepareQueryForGrid($gridConfiguration);

        // Count total items for pagination
        $queryBuilderTotalItems = clone $baseQueryBuilder;
        $queryBuilderTotalItems->resetOrderBy()
            ->select('COUNT(DISTINCT dictionaryEntryId)');
        $totalItems = (int) $this->connection->fetchOne($queryBuilderTotalItems->getSQL(), $queryBuilderTotalItems->getParameters());

        // Add pagination
        $queryBuilder = clone $baseQueryBuilder;
        $queryBuilder->setFirstResult(($gridConfiguration->page() - 1) * $gridConfiguration->itemsPerPage())
            ->setMaxResults($gridConfiguration->itemsPerPage());

        $data = $this->connection->fetchAllAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        $collection = new ArrayCollection();

        foreach ($data as $key => $row) {
            $collection->set($row['dictionaryEntryId'], DictionaryEntryGridView::deserialize($row));
        }

        return new GridResult($collection,
            $totalItems,
            $gridConfiguration->page(),
            $gridConfiguration->itemsPerPage()
        );
    }

    public function getLastInsertedId(): int
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('MAX(dictionaryEntryId)')
            ->from(self::TABLE_NAME);

        return (int) $queryBuilder->executeQuery()->fetchOne();
    }

    private function prepareQueryForSelect(GridConfiguration $gridConfiguration, DictionaryType $dictionaryType): QueryBuilder
    {
        $queryBuilder = $this->connection->createQueryBuilder();

        $queryBuilder
            ->select('value', 'description')
            ->from(self::TABLE_NAME)
            ->andWhere('dictionaryType = :dictionaryType')
            ->setParameter('dictionaryType', $dictionaryType->value);

        $gridConfiguration->addSortAndFilterToDbalQueryBuilder($queryBuilder);

        return $queryBuilder;
    }

    private function prepareQueryForGrid(GridConfiguration $gridConfiguration): QueryBuilder
    {
        $queryBuilder = $this->connection->createQueryBuilder();

        $queryBuilder
            ->select('dictionaryEntryId', 'value', 'description', 'searchableDescription', 'dictionaryType')
            ->from(self::TABLE_NAME);

        $gridConfiguration->addSortAndFilterToDbalQueryBuilder($queryBuilder);

        return $queryBuilder;
    }
}
