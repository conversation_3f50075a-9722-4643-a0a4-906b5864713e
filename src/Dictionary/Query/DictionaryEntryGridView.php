<?php

declare(strict_types=1);

namespace App\Dictionary\Query;

use App\Common\QueryView;
use App\SharedKernel\DictionaryType;

readonly class DictionaryEntryGridView implements QueryView
{
    public function __construct(
        private int $dictionaryEntryId,
        private string $value,
        private string $description,
        private string $searchableDescription,
        private DictionaryType $dictionaryType,
    ) {
    }

    #[\Override]
    public static function deserialize(array $data): QueryView
    {
        return new self(
            $data['dictionaryEntryId'],
            $data['value'],
            $data['description'],
            $data['searchableDescription'],
            DictionaryType::from($data['dictionaryType']),
        );
    }

    /** @return array<string, string|int> */
    #[\Override]
    public function jsonSerialize(): array
    {
        return [
            'dictionaryEntryId' => $this->dictionaryEntryId,
            'label' => $this->description,
            'value' => $this->value,
            'searchableDescription' => $this->searchableDescription,
            'dictionaryType' => $this->dictionaryType->value,
        ];
    }
}
