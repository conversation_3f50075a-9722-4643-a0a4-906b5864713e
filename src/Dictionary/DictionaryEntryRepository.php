<?php

declare(strict_types=1);

namespace App\Dictionary;

use Doctrine\ORM\EntityManagerInterface;

class DictionaryEntryRepository
{
    public function __construct(
        private EntityManagerInterface $em)
    {
    }

    public function add(DictionaryEntry $dictionaryEntry): void
    {
        $this->em->persist($dictionaryEntry);
        $this->em->flush();
    }

    public function update(DictionaryEntry $dictionaryEntry): void
    {
        $this->em->persist($dictionaryEntry);
        $this->em->flush();
    }

    public function findById(int $dictionaryEntryId): ?DictionaryEntry
    {
        $qb = $this->em->createQueryBuilder();

        $qb->select('de')
           ->from(DictionaryEntry::class, 'de')
           ->where('de.dictionaryEntryId = :id')
           ->setParameter('id', $dictionaryEntryId);

        return $qb->getQuery()->getOneOrNullResult();
    }
}
