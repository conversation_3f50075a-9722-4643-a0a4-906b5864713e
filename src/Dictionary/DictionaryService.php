<?php

declare(strict_types=1);

namespace App\Dictionary;

use App\Common\Query\GridConfiguration;
use App\Common\Query\GridResult;
use App\Dictionary\Query\DictionaryEntryGridView;
use App\Dictionary\Query\DictionaryEntryQuery;
use App\Dictionary\Query\DictionaryEntryView;
use App\SharedKernel\DictionaryType;
use Doctrine\Common\Collections\Collection;

readonly class DictionaryService
{
    public function __construct(
        private DictionaryEntryRepository $dictionaryEntryRepository,
        private DictionaryEntryQuery $dictionaryEntryQuery,
    ) {
    }

    public function addDictionaryEntry(string $value, string $description, string $searchableDescription, DictionaryType $dictionaryType): void
    {
        if ($this->dictionaryEntryQuery->dictionaryValueExists($value, $dictionaryType)) {
            throw new \InvalidArgumentException('Wpis o wartości '.$value.' już istnieje w słowniku '.$dictionaryType->value);
        }

        $dictionaryEntry = DictionaryEntry::create(
            $value,
            $description,
            $searchableDescription,
            $dictionaryType
        );

        $this->dictionaryEntryRepository->add($dictionaryEntry);
    }

    public function editDictionaryEntry(int $dictionaryEntryId, string $description, string $searchableDescription): void
    {
        $dictionaryEntry = $this->dictionaryEntryRepository->findById($dictionaryEntryId);

        if ($dictionaryEntry === null) {
            throw new \InvalidArgumentException('Wpis słownika o id '.$dictionaryEntryId.' nie istnieje');
        }

        $dictionaryEntry->update($description, $searchableDescription);

        $this->dictionaryEntryRepository->update($dictionaryEntry);
    }

    public function dictionaryValueExists(string $value, string $dictionaryType): bool
    {
        return $this->dictionaryEntryQuery->dictionaryValueExists($value, DictionaryType::from($dictionaryType));
    }

    /** @return GridResult<string, DictionaryEntryView> */
    public function findDictionaryEntriesForSelect(GridConfiguration $gridConfiguration, string $dictionaryType): GridResult
    {
        return $this->dictionaryEntryQuery->findForSelect(
            $gridConfiguration,
            DictionaryType::from($dictionaryType)
        );
    }

    /** @return Collection<int, DictionaryEntryGridView> */
    public function findAll(): Collection
    {
        return $this->dictionaryEntryQuery->findAll();
    }

    /** @return GridResult<int, DictionaryEntryGridView> */
    public function findForGrid(GridConfiguration $gridConfiguration): GridResult
    {
        return $this->dictionaryEntryQuery->findForGrid(
            $gridConfiguration
        );
    }

    public function getDictionaryEntryById(int $dictionaryEntryId): ?DictionaryEntry
    {
        return $this->dictionaryEntryRepository->findById($dictionaryEntryId);
    }

    public function getLastInsertedId(): int
    {
        return $this->dictionaryEntryQuery->getLastInsertedId();
    }
}
