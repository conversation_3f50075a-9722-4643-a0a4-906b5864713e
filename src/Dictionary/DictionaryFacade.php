<?php

declare(strict_types=1);

namespace App\Dictionary;

use App\Common\Query\GridConfiguration;
use App\Common\Query\GridResult;
use App\Dictionary\Query\DictionaryEntryGridView;
use App\Dictionary\Query\DictionaryEntryView;
use App\SharedKernel\DictionaryType;
use Doctrine\Common\Collections\Collection;

readonly class DictionaryFacade
{
    public function __construct(
        private DictionaryService $dictionaryService,
    ) {
    }

    public function addDictionaryEntry(string $value, string $description, string $searchableDescription, DictionaryType $dictionaryType): void
    {
        $this->dictionaryService->addDictionaryEntry($value, $description, $searchableDescription, $dictionaryType);
    }

    public function editDictionaryEntry(int $dictionaryEntryId, string $description, string $searchableDescription): void
    {
        $this->dictionaryService->editDictionaryEntry($dictionaryEntryId, $description, $searchableDescription);
    }

    public function dictionaryValueExists(string $value, string $dictionaryType): bool
    {
        return $this->dictionaryService->dictionaryValueExists($value, $dictionaryType);
    }

    /** @return GridResult<string, DictionaryEntryView> */
    public function findDictionaryEntriesForSelect(GridConfiguration $gridConfiguration, string $dictionaryType): GridResult
    {
        return $this->dictionaryService->findDictionaryEntriesForSelect($gridConfiguration, $dictionaryType);
    }

    /** @return Collection<int, DictionaryEntryGridView> */
    public function findAll(): Collection
    {
        return $this->dictionaryService->findAll();
    }

    /** @return GridResult<int, DictionaryEntryGridView> */
    public function findForGrid(GridConfiguration $gridConfiguration): GridResult
    {
        return $this->dictionaryService->findForGrid($gridConfiguration);
    }

    public function getDictionaryEntryById(int $dictionaryEntryId): ?DictionaryEntry
    {
        return $this->dictionaryService->getDictionaryEntryById($dictionaryEntryId);
    }

    public function getLastInsertedId(): int
    {
        return $this->dictionaryService->getLastInsertedId();
    }
}
