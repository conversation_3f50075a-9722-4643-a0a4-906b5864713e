<?php

declare(strict_types=1);

namespace App\Auth\Application\Exception;

class AuthUserAlreadyExists extends \Exception
{
    public static function becauseThisEmailAsAlreadyInUse(string $email): self
    {
        return new self(sprintf('Użytkownik z adresem e-mail %s już istnieje. Nie można utworzyć nowego konta.', $email));
    }

    public static function becauseThisMobilePhoneNumberAsAlreadyInUse(string $email): self
    {
        return new self(sprintf('Użytkownik z numerem telefonu %s już istnieje. Nie można utworzyć nowego konta.', $email));
    }
}
