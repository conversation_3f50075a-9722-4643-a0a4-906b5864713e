<?php

declare(strict_types=1);

namespace App\Auth\Application\Query;

use App\Common\QueryView;

readonly class AuthUserView implements QueryView
{
    public function __construct(
        private string $authUserId,
        private string $email,
        private string $password,
        private string $role,
        private bool $isActive,
    ) {
    }

    /** @return array<string, mixed> */
    public function serialize(): array
    {
        return get_object_vars($this);
    }

    /** @return array<string, mixed> */
    public function jsonSerialize(): array
    {
        $array = $this->serialize();
        unset($array['password']);

        return $array;
    }

    /** @param array<string, mixed> $data */
    public static function deserialize(array $data): self
    {
        return new self(
            $data['authUserId'],
            $data['email'],
            $data['password'],
            $data['role'],
            (bool) $data['isActive']
        );
    }

    public function authUserId(): string
    {
        return $this->authUserId;
    }

    public function email(): string
    {
        return $this->email;
    }

    public function password(): string
    {
        return $this->password;
    }

    public function role(): string
    {
        return $this->role;
    }

    public function isActive(): bool
    {
        return $this->isActive;
    }
}
