<?php

declare(strict_types=1);

namespace App\Auth\Application\Query;

use App\Auth\Infrastructure\Doctrine\Exception\AccountNotExist;
use Doctrine\DBAL\Exception;

interface AuthUserQueryInterface
{
    /**
     * @throws AccountNotExist
     * @throws Exception
     */
    public function findByEmail(string $email): AuthUserView;

    public function checkIfEmailExists(string $email, ?string $excludeAuthUserId = null): bool;

    public function checkIfMobilePhoneNumberExists(string $mobilePhoneNumber): bool;
}
