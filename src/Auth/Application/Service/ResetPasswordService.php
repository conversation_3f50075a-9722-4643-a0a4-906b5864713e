<?php

declare(strict_types=1);

namespace App\Auth\Application\Service;

use App\Auth\Application\Exception\ExpiredResetPasswordTokenException;
use App\Auth\Application\Exception\InvalidResetPasswordTokenException;
use App\Auth\Application\Exception\TooManyPasswordRequestsException;
use App\Auth\Application\Query\ResetPasswordRequestQueryInterface;
use App\Auth\Domain\AuthUserRepositoryInterface;
use App\Auth\Domain\ResetPasswordRequest;
use App\Auth\Domain\ResetPasswordRequestRepositoryInterface;
use App\SharedKernel\Email;

readonly class ResetPasswordService implements ResetPasswordServiceInterface
{
    public function __construct(
        private TokenComponentsInterface $tokenComponents,
        private AuthUserRepositoryInterface $authUserRepository,
        private ResetPasswordRequestRepositoryInterface $resetPasswordRequestRepository,
        private ResetPasswordRequestQueryInterface $resetPasswordRequestQuery,
        private ParameterBagInterface $parameterBag,
        private EmailInterface $email,
    ) {
    }

    public function createResetToken(string $email): void
    {
        $authUser = $this->authUserRepository->getByEmail(Email::create($email));

        if ($this->hasUserHitThrottling($authUser->aggregateRootId())) {
            throw TooManyPasswordRequestsException::becauseYouHaveAlreadyRequestedPasswordReset();
        }

        $resetRequestLifetime = $this->parameterBag->getResetPasswordRequestLifetime();
        $token = $this->tokenComponents->generateToken($authUser->aggregateRootId(), $resetRequestLifetime);

        $resetPasswordRequest = new ResetPasswordRequest(
            verifier: $token->verifier(),
            authUser: $authUser,
            expiresAt: $token->expiresAt(),
        );

        $this->resetPasswordRequestRepository->add($resetPasswordRequest);

        $this->email->sendResetPasswordEmail($authUser->email(), 'Resetowanie hasła', $token->publicToken());
    }

    public function validateResetPasswordToken(string $fullToken): ResetPasswordRequest
    {
        $verifier = $this->tokenComponents->validateFullTokenAndFetchVerifier($fullToken);

        $resetPasswordRequest = $this->resetPasswordRequestRepository->findByVerifier($verifier);

        if (null === $resetPasswordRequest) {
            throw InvalidResetPasswordTokenException::becauseResetPasswordLinkIsInvalid();
        }

        if ($resetPasswordRequest->isExpired()) {
            throw ExpiredResetPasswordTokenException::becauseLinkIsExpired();
        }

        return $resetPasswordRequest;
    }

    public function changePasswordWithToken(string $fullToken, string $newPassword1, string $newPassword2): void
    {
        $resetPasswordRequest = $this->validateResetPasswordToken($fullToken);

        $authUser = $resetPasswordRequest->authUser();
        $authUser->changePassword($newPassword1, $newPassword2);

        $this->authUserRepository->update($authUser);

        $this->resetPasswordRequestRepository->removeResetPasswordRequest($resetPasswordRequest);

        $this->email->sendEmailPasswordHasChanged($authUser->email());
    }

    private function hasUserHitThrottling(string $authUserId): bool
    {
        $lastRequestDate = $this->resetPasswordRequestQuery->getMostRecentNonExpiredRequestDate($authUserId);

        if (null === $lastRequestDate) {
            return false;
        }

        $availableAt = (clone $lastRequestDate)->add(new \DateInterval("PT{$this->parameterBag->getResetPasswordThrottleLimit()}S"));

        return $availableAt > new \DateTime('now');
    }
}
