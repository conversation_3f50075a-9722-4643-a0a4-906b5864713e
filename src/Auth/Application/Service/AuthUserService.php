<?php

declare(strict_types=1);

namespace App\Auth\Application\Service;

use App\Auth\Application\Exception\AuthUserAlreadyExists;
use App\Auth\Application\Exception\AuthUserCannotBeActivated;
use App\Auth\Application\Exception\AuthUserCannotBeDeactivated;
use App\Auth\Application\Exception\AuthUserEmailCannotBeChanged;
use App\Auth\Application\Query\AuthUserQueryInterface;
use App\Auth\Domain\AuthUser;
use App\Auth\Domain\AuthUserRepositoryInterface;
use App\Auth\Domain\Password;
use App\Auth\Domain\RefreshTokenRepositoryInterface;
use App\Common\Uuid;
use App\SharedKernel\Email;
use App\SharedKernel\Roles;

readonly class AuthUserService implements AuthUserServiceInterface
{
    public function __construct(
        private AuthUserRepositoryInterface $authUserRepository,
        private RefreshTokenRepositoryInterface $refreshTokenRepository,
        private AuthUserQueryInterface $authUserQuery,
        private EmailInterface $email,
    ) {
    }

    public function createAuthUser(Uuid $authUserId, Email $email, Roles $role, bool $isActive): void
    {
        if ($this->authUserQuery->checkIfEmailExists($email->valueString()) === false) {
            throw AuthUserAlreadyExists::becauseThisEmailAsAlreadyInUse($email->valueString());
        }

        $authUser = AuthUser::createAuthUser(
            authUserId: $authUserId,
            email: $email,
            password: Password::generateFirstPassword(),
            role: $role,
            isActive: $isActive
        );

        $this->authUserRepository->add($authUser);

        $this->email->sendWelcomeEmail($email);
    }

    public function changeAuthUserEmail(Uuid $authUserId, string $email): void
    {
        $authUser = $this->authUserRepository->getByAuthUserId($authUserId);

        if ($this->authUserQuery->checkIfEmailExists($email, $authUserId->valueString()) === false) {
            throw AuthUserEmailCannotBeChanged::becauseThisEmailAsAlreadyInUse($authUserId->toString());
        }

        $authUser->changeEmail(Email::create($email));
        $this->authUserRepository->update($authUser);
    }

    public function activateAuthUser(Uuid $authUserId): void
    {
        $authUser = $this->authUserRepository->getByAuthUserId($authUserId);

        if ($authUser->isActive() === true) {
            throw AuthUserCannotBeActivated::becauseIsAlreadyActive();
        }

        $authUser->activate();

        $this->authUserRepository->update($authUser);
    }

    public function deactivateAuthUser(Uuid $authUserId): void
    {
        $authUser = $this->authUserRepository->getByAuthUserId($authUserId);

        if ($authUser->isActive() === false) {
            throw AuthUserCannotBeDeactivated::becauseIsAlreadyInactive();
        }

        $authUser->deactivate();

        $this->authUserRepository->update($authUser);
    }

    public function logoutAuthUser(Uuid $authUserId, string $username, string $refreshToken): void
    {
        $authUser = $this->authUserRepository->getByAuthUserId($authUserId);
        $authUser->logout();
        $this->authUserRepository->update($authUser);
        $this->refreshTokenRepository->revokeRefreshToken($username, $refreshToken);
    }
}
