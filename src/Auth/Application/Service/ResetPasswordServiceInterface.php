<?php

declare(strict_types=1);

namespace App\Auth\Application\Service;

use App\Auth\Domain\ResetPasswordRequest;

interface ResetPasswordServiceInterface
{
    public function createResetToken(string $email): void;

    public function validateResetPasswordToken(string $fullToken): ResetPasswordRequest;

    public function changePasswordWithToken(string $fullToken, string $newPassword1, string $newPassword2): void;
}
