<?php

declare(strict_types=1);

namespace App\Auth\Application\Service;

use App\Common\Uuid;
use App\SharedKernel\Email;
use App\SharedKernel\Roles;

interface AuthUserServiceInterface
{
    public function createAuthUser(Uuid $authUserId, Email $email, Roles $role, bool $isActive): void;

    public function changeAuthUserEmail(Uuid $authUserId, string $email): void;

    public function activateAuthUser(Uuid $authUserId): void;

    public function deactivateAuthUser(Uuid $authUserId): void;

    public function logoutAuthUser(Uuid $authUserId, string $username, string $refreshToken): void;
}
