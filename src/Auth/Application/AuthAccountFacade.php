<?php

declare(strict_types=1);

namespace App\Auth\Application;

use App\Auth\Application\Service\AuthUserServiceInterface;
use App\Common\Uuid;
use App\SharedKernel\Email;
use App\SharedKernel\Roles;

readonly class AuthAccountFacade
{
    public function __construct(
        private AuthUserServiceInterface $authUserService,
    ) {
    }

    public function createAuthUser(string $authUserId, string $email, string $role, bool $isActive): void
    {
        $this->authUserService->createAuthUser(
            Uuid::fromString($authUserId),
            Email::create($email),
            Roles::from($role),
            $isActive,
        );
    }

    public function changeAuthUserEmail(string $authUserId, string $email): void
    {
        $this->authUserService->changeAuthUserEmail(
            Uuid::fromString($authUserId),
            $email
        );
    }

    public function activateAuthUser(string $authUserId): void
    {
        $this->authUserService->activateAuthUser(Uuid::fromString($authUserId));
    }

    public function deactivateAuthUser(string $authUserId): void
    {
        $this->authUserService->deactivateAuthUser(Uuid::fromString($authUserId));
    }

    public function logoutAuthUser(string $authUserId, string $username, string $refreshToken): void
    {
        $this->authUserService->logoutAuthUser(
            Uuid::fromString($authUserId),
            $username,
            $refreshToken
        );
    }
}
