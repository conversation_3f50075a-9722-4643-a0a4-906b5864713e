<?php

declare(strict_types=1);

namespace App\Auth\Application;

use App\Auth\Application\Service\ResetPasswordServiceInterface;

readonly class NoAuthFacade
{
    public function __construct(
        private ResetPasswordServiceInterface $resetPasswordService,
    ) {
    }

    public function forgotPasswordRequest(string $email): void
    {
        $this->resetPasswordService->createResetToken($email);
    }

    public function checkToken(string $token): void
    {
        $this->resetPasswordService->validateResetPasswordToken($token);
    }

    public function changePasswordWithToken(string $token, string $newPassword1, string $newPassword2): void
    {
        $this->resetPasswordService->changePasswordWithToken($token, $newPassword1, $newPassword2);
    }
}
