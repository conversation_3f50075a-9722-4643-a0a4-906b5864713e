<?php

declare(strict_types=1);

namespace App\Auth\Domain;

use App\Common\Uuid;
use App\SharedKernel\Email;

interface AuthUserRepositoryInterface
{
    public function add(AuthUser $authUser): void;

    public function update(AuthUser $authUser): void;

    public function getByAuthUserId(Uuid $authUserId): AuthUser;

    public function getByEmail(Email $email): AuthUser;

    public function findByEmail(Email $email): ?AuthUser;
}
