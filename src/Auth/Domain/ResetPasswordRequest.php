<?php

declare(strict_types=1);

namespace App\Auth\Domain;

use App\Common\Attribute\TrackEntityChange;

#[TrackEntityChange(objectLogDetailsFunction: 'logDetails')]
class ResetPasswordRequest
{
    protected int $id;
    protected \DateTimeImmutable $requestedAt;

    public function __construct(
        protected readonly string $verifier,
        protected readonly AuthUser $authUser,
        protected readonly \DateTimeImmutable $expiresAt,
    ) {
        $this->requestedAt = new \DateTimeImmutable('now');
    }

    /** @return array<string, mixed> */
    public function logDetails(): array
    {
        return [
            'email' => $this->authUser->email()->valueString(),
        ];
    }

    public function verifier(): string
    {
        return $this->verifier;
    }

    public function requestedAt(): \DateTimeImmutable
    {
        return $this->requestedAt;
    }

    public function isExpired(): bool
    {
        return $this->expiresAt->getTimestamp() <= time();
    }

    public function expiresAt(): \DateTimeImmutable
    {
        return $this->expiresAt;
    }

    public function authUser(): AuthUser
    {
        return $this->authUser;
    }
}
