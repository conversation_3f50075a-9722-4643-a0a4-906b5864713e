<?php

declare(strict_types=1);

namespace App\Auth\Domain;

use App\Common\ValueObject;
use Webmozart\Assert\Assert;

class Password implements ValueObject
{
    public const int MIN_LENGTH = 8;
    public const int MAX_LENGTH = 12;

    public const string EXCEPTION_NOT_EMPTY = 'Password not be empty.';
    public const string EXCEPTION_MIN_LENGTH = 'Password must contain min '.self::MIN_LENGTH.' characters.';
    public const string EXCEPTION_MAX_LENGTH = 'Password must contain max '.self::MAX_LENGTH.' characters.';
    public const string EXCEPTION_REGEX = 'The password you provided does not meet the requirements.';

    private string $passwordHash;

    public function __construct(string $password)
    {
        Assert::notEmpty($password, self::EXCEPTION_NOT_EMPTY);
        Assert::minLength($password, self::MIN_LENGTH, self::EXCEPTION_MIN_LENGTH);
        Assert::maxLength($password, self::MAX_LENGTH, self::EXCEPTION_MAX_LENGTH);
        Assert::regex($password, '/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[\W_]).{'.self::MIN_LENGTH.','.self::MAX_LENGTH.'}$/', self::EXCEPTION_REGEX);

        $this->passwordHash = password_hash($password, PASSWORD_DEFAULT);
    }

    public static function generateFirstPassword(): self
    {
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $digits = '0123456789';
        $specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';
        $allChars = $uppercase.$lowercase.$digits.$specialChars;

        $password = $uppercase[random_int(0, strlen($uppercase) - 1)];
        $password .= $lowercase[random_int(0, strlen($lowercase) - 1)];
        $password .= $digits[random_int(0, strlen($digits) - 1)];
        $password .= $specialChars[random_int(0, strlen($specialChars) - 1)];

        for ($i = 0; $i < random_int(4, 8); ++$i) {
            $password .= $allChars[random_int(0, strlen($allChars) - 1)];
        }

        $password = str_shuffle($password);

        return new self($password);
    }

    public function verify(string $plainPassword): bool
    {
        return password_verify($plainPassword, $this->passwordHash);
    }

    public function __toString(): string
    {
        return $this->passwordHash;
    }

    public function equals(Password $password): bool
    {
        return $this->passwordHash === $password->passwordHash;
    }

    public function passwordHash(): string
    {
        return $this->passwordHash;
    }

    public function jsonSerialize(): mixed
    {
        return '********';
    }
}
