<?php

declare(strict_types=1);

namespace App\Auth\Domain;

interface ResetPasswordRequestRepositoryInterface
{
    public function add(ResetPasswordRequest $resetPasswordRequest): void;

    public function findByVerifier(string $verifier): ?ResetPasswordRequest;

    public function removeResetPasswordRequest(ResetPasswordRequest $resetPasswordRequest): void;

    public function removeExpiredResetPasswordRequests(): int;
}
