<?php

declare(strict_types=1);

namespace App\Auth\Domain;

use App\Auth\Domain\Exception\PasswordCannotChange;
use App\Common\Attribute\TrackEntityChange;
use App\Common\Uuid;
use App\SharedKernel\Email;
use App\SharedKernel\Roles;

use function Symfony\Component\Clock\now;

#[TrackEntityChange(objectLogDetailsFunction: 'logDetails')]
class AuthUser
{
    private ?\DateTimeImmutable $verificationAt = null;
    private ?\DateTimeImmutable $logoutAt = null;
    private ?\DateTimeImmutable $loginAt = null;
    private \DateTimeImmutable $createdAt;
    private ?\DateTimeImmutable $updatedAt = null;

    private function __construct(
        private Uuid $authUserId,
        private Email $email,
        private Password $password,
        private readonly Roles $role,
        private bool $isActive,
    ) {
        $this->createdAt = now();
    }

    public static function createAuthUser(Uuid $authUserId, Email $email, Password $password, Roles $role, bool $isActive): self
    {
        return new self($authUserId, $email, $password, $role, $isActive);
    }

    public function changePassword(string $newPassword1, string $newPassword2): void
    {
        if ($newPassword1 !== $newPassword2) {
            throw PasswordCannotChange::becausePasswordsDoNotMatch();
        }

        $this->password = new Password($newPassword1);
        $this->updatedAt = now();
        $this->verify();
    }

    public function changeEmail(Email $email): void
    {
        $this->email = $email;
        $this->updatedAt = now();
        $this->revertVerification();
    }

    private function verify(): void
    {
        if ($this->verificationAt === null) {
            $this->verificationAt = now();
        }
    }

    private function revertVerification(): void
    {
        if ($this->verificationAt instanceof \DateTimeImmutable) {
            $this->verificationAt = null;
        }
    }

    public function activate(): void
    {
        $this->isActive = true;
        $this->updatedAt = now();
    }

    public function deactivate(): void
    {
        $this->isActive = false;
        $this->updatedAt = now();
    }

    public function updateLoginAt(): void
    {
        $this->loginAt = now();
        $this->updatedAt = now();
    }

    public function logout(): void
    {
        $this->logoutAt = now();
        $this->updatedAt = now();
    }

    public function aggregateRootId(): string
    {
        return $this->authUserId->valueString();
    }

    /** @return array<string, mixed> */
    public function logDetails(): array
    {
        return [
            'email' => $this->email->valueString(),
        ];
    }

    public function email(): Email
    {
        return $this->email;
    }

    public function role(): Roles
    {
        return $this->role;
    }

    public function password(): Password
    {
        return $this->password;
    }

    public function isActive(): bool
    {
        return $this->isActive;
    }

    public function verificationAt(): ?\DateTimeImmutable
    {
        return $this->verificationAt;
    }

    public function logoutAt(): ?\DateTimeImmutable
    {
        return $this->logoutAt;
    }

    public function loginAt(): ?\DateTimeImmutable
    {
        return $this->loginAt;
    }
}
