<?php

declare(strict_types=1);

namespace App\Auth\Infrastructure\Util;

use App\Auth\Domain\ResetPasswordRequestRepositoryInterface;

class ResetPasswordCleaner
{
    /** @var bool Enable/disable garbage collection */
    private bool $enabled;

    public function __construct(
        private readonly ResetPasswordRequestRepositoryInterface $passwordRequestRepository,
        bool $enabled = true,
    ) {
        $this->enabled = $enabled;
    }

    /**
     * Clears expired reset password requests from persistence.
     *
     * Enable/disable in configuration. Calling with $force = true
     * will attempt to remove expired requests regardless of
     * configuration setting.
     */
    public function handleGarbageCollection(bool $force = false): int
    {
        if ($this->enabled || $force) {
            return $this->passwordRequestRepository->removeExpiredResetPasswordRequests();
        }

        return 0;
    }
}
