<?php

declare(strict_types=1);

namespace App\Auth\Infrastructure\Security;

use App\Auth\Domain\AuthUser;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;

class UserSecurity implements UserSecurityInterface, PasswordAuthenticatedUserInterface
{
    public function __construct(
        private readonly AuthUser $authUser,
        /** @var array<string> */
        private array $hospitals = [],
        /** @var array<string> */
        private array $protocols = [],
    ) {
    }

    public function authUser(): AuthUser
    {
        return $this->authUser;
    }

    /** @param array<string> $hospitals */
    public function setHospitals(array $hospitals): void
    {
        $this->hospitals = $hospitals;
    }

    /** @param array<string> $protocols */
    public function setProtocols(array $protocols): void
    {
        $this->protocols = $protocols;
    }

    public function getRoles(): array
    {
        return [$this->authUser->role()->value];
    }

    public function eraseCredentials(): void
    {
        // TODO: Implement eraseCredentials() method.
    }

    public function getUserIdentifier(): string
    {
        return $this->authUser->email()->valueString();
    }

    public function getRootId(): string
    {
        return $this->authUser->aggregateRootId();
    }

    public function getPassword(): ?string
    {
        return $this->authUser->password()->passwordHash();
    }

    /** @return array<string> */
    public function getHospitals(): array
    {
        return $this->hospitals;
    }

    /** @return array<string> */
    public function getProtocols(): array
    {
        return $this->protocols;
    }
}
