<?php

declare(strict_types=1);

namespace App\Auth\Infrastructure\Security;

use App\Auth\Domain\AuthUser;
use App\Auth\Domain\AuthUserRepositoryInterface;
use Symfony\Component\Security\Core\Exception\UserNotFoundException;
use Symfony\Component\Security\Core\User\UserCheckerInterface;
use Symfony\Component\Security\Core\User\UserInterface;

class UserChecker implements UserCheckerInterface
{
    public function __construct(
        private AuthUserRepositoryInterface $authUserRepository)
    {
    }

    public function checkPreAuth(UserInterface $user): void
    {
        if (!$user instanceof UserSecurity) {
            throw new UserNotFoundException();
        }

        /** @var AuthUser $authUser */
        $authUser = $user->authUser();

        if ($authUser->isActive() === false) {
            throw new UserNotFoundException();
        }
    }

    public function checkPostAuth(UserInterface $user): void
    {
        if (!$user instanceof UserSecurity) {
            throw new UserNotFoundException();
        }

        $authUser = $user->authUser();

        if ($authUser->verificationAt() === null) {
            throw new UserNotFoundException();
        }

        $this->updateLoginAt($authUser);
    }

    protected function updateLoginAt(AuthUser $authUser): void
    {
        $authUser->updateLoginAt();
        $this->authUserRepository->update($authUser);
    }
}
