<?php

declare(strict_types=1);

namespace App\Auth\Infrastructure\Security;

use App\Account\Application\AccountFacade;
use App\Auth\Domain\AuthUserRepositoryInterface;
use App\SharedKernel\Email;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Security\Core\Exception\UnsupportedUserException;
use Symfony\Component\Security\Core\Exception\UserNotFoundException;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Security\Core\User\UserProviderInterface;

/**
 * @implements UserProviderInterface<UserSecurity>
 */
readonly class DoctrineUserProvider implements UserProviderInterface
{
    public function __construct(
        private Security $security,
        private AuthUserRepositoryInterface $authUserRepository,
        private AccountFacade $accountFacade,
    ) {
    }

    public function loadUserByIdentifier(string $identifier): UserSecurity
    {
        if ($this->security->getUser() instanceof UserSecurity) {
            /** @var UserSecurity $user */
            $user = $this->security->getUser();

            return $user;
        }

        $auth = $this->authUserRepository->findByEmail(Email::create($identifier));

        if ($auth === null) {
            throw new UserNotFoundException();
        }

        $account = $this->accountFacade->findByAccountId($auth->aggregateRootId());

        if ($account === null) {
            throw new UserNotFoundException();
        }

        return new UserSecurity($auth, $account->hospitals(), $account->protocols());
    }

    public function refreshUser(UserInterface $user): UserInterface
    {
        if (!$user instanceof UserSecurity) {
            throw new UnsupportedUserException(sprintf('Invalid user class "%s".', get_class($user)));
        }

        return $user;
    }

    public function supportsClass(string $class): bool
    {
        return UserSecurity::class === $class;
    }
}
