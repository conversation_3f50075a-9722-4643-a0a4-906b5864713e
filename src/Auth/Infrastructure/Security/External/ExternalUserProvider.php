<?php

declare(strict_types=1);

namespace App\Auth\Infrastructure\Security\External;

use App\Auth\Infrastructure\Security\External\ExternalAuthenticatorStrategy\DataAnalyticsAuthenticatorStrategy;
use Symfony\Component\Security\Core\User\UserInterface;

class ExternalUserProvider
{
    /** @var array<ExternalAuthenticatorStrategy> */
    private array $strategies;

    public function __construct()
    {
        $this->strategies = [
            new DataAnalyticsAuthenticatorStrategy(),
        ];
    }

    public function getUserByApiKey(string $apiKey): ?UserInterface
    {
        foreach ($this->strategies as $strategy) {
            if ($strategy->supports($apiKey)) {
                return $strategy->getUser();
            }
        }

        return null;
    }
}
