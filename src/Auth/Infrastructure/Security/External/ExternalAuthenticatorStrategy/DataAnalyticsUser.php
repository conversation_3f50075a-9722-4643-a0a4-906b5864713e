<?php

declare(strict_types=1);

namespace App\Auth\Infrastructure\Security\External\ExternalAuthenticatorStrategy;

use App\Auth\Infrastructure\Security\External\ExternalUserInterface;

class DataAnalyticsUser implements ExternalUserInterface
{
    public function getRoles(): array
    {
        return ['ROLE_EXTERNAL_API', 'ROLE_EXTERNAL_API_ANALYTICS'];
    }

    public function getPassword(): ?string
    {
        return null;
    }

    public function getSalt(): ?string
    {
        return null;
    }

    public function getUsername(): string
    {
        return 'external_api_analytics';
    }

    public function eraseCredentials(): void
    {
    }

    public function getUserIdentifier(): string
    {
        return $this->getUsername();
    }

    public function getRootId(): string
    {
        return $this->getUsername();
    }
}
