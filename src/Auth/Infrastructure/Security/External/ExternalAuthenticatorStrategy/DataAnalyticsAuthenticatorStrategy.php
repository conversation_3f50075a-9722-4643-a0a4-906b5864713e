<?php

declare(strict_types=1);

namespace App\Auth\Infrastructure\Security\External\ExternalAuthenticatorStrategy;

use App\Auth\Infrastructure\Security\External\ExternalAuthenticatorStrategy;
use App\Auth\Infrastructure\Security\External\ExternalUserInterface;

class DataAnalyticsAuthenticatorStrategy implements ExternalAuthenticatorStrategy
{
    public function supports(string $apiKey): bool
    {
        return $apiKey === $_ENV['API_KEY_EXTERNAL_ANALYTICS'];
    }

    public function getUser(): ExternalUserInterface
    {
        return new DataAnalyticsUser();
    }
}
