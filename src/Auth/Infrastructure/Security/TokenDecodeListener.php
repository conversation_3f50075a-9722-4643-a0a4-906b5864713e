<?php

declare(strict_types=1);

namespace App\Auth\Infrastructure\Security;

use App\Auth\Domain\AuthUserRepositoryInterface;
use App\SharedKernel\Email;
use Lexik\Bundle\JWTAuthenticationBundle\Event\JWTDecodedEvent;

readonly class TokenDecodeListener
{
    public function __construct(
        private AuthUserRepositoryInterface $authUserRepository,
    ) {
    }

    public function invalidateTokenAfterLogout(JWTDecodedEvent $event): void
    {
        $payload = $event->getPayload();
        $authUser = $this->authUserRepository->findByEmail(Email::create($payload['username']));

        if ($authUser === null) {
            $event->markAsInvalid();

            return;
        }

        $tokenCreatedAt = new \DateTimeImmutable('@'.$payload['iat']);

        if ($authUser->logoutAt() !== null && $tokenCreatedAt < $authUser->logoutAt()) {
            $event->markAsInvalid();
        }
    }
}
