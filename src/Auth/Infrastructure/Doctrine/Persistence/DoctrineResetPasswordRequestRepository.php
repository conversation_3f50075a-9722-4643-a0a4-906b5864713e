<?php

declare(strict_types=1);

namespace App\Auth\Infrastructure\Doctrine\Persistence;

use App\Auth\Domain\ResetPasswordRequest;
use App\Auth\Domain\ResetPasswordRequestRepositoryInterface;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ResetPasswordRequest>
 */
class DoctrineResetPasswordRequestRepository extends ServiceEntityRepository implements ResetPasswordRequestRepositoryInterface
{
    public function __construct(
        private readonly EntityManagerInterface $em,
        ManagerRegistry $registry,
    ) {
        parent::__construct($registry, ResetPasswordRequest::class);
    }

    public function add(ResetPasswordRequest $resetPasswordRequest): void
    {
        $this->em->persist($resetPasswordRequest);
        $this->em->flush();
    }

    public function findByVerifier(string $verifier): ?ResetPasswordRequest
    {
        /** @var ?ResetPasswordRequest $resetPasswordRequest */
        $resetPasswordRequest = $this->findOneBy(['verifier' => $verifier]);

        return $resetPasswordRequest;
    }

    public function removeResetPasswordRequest(ResetPasswordRequest $resetPasswordRequest): void
    {
        $this->em->createQueryBuilder()
            ->delete(ResetPasswordRequest::class, 'rpr')
            ->where('rpr.authUser = :authUser')
            ->setParameter('authUser', $resetPasswordRequest->authUser()->aggregateRootId())
            ->getQuery()
            ->execute()
        ;
    }

    public function removeExpiredResetPasswordRequests(): int
    {
        $time = new \DateTimeImmutable('-1 week');
        $query = $this->em->createQueryBuilder()
            ->delete(ResetPasswordRequest::class, 'rpr')
            ->where('rpr.expiresAt <= :time')
            ->setParameter('time', $time)
            ->getQuery()
        ;

        return $query->execute();
    }
}
