<?php

declare(strict_types=1);

namespace App\Auth\Infrastructure\Doctrine\Persistence;

use App\Auth\Domain\RefreshTokenRepositoryInterface;
use Doctrine\DBAL\Connection;

readonly class DoctrineRefreshTokenRepository implements RefreshTokenRepositoryInterface
{
    public function __construct(
        private Connection $connection,
    ) {
    }

    public function revokeRefreshToken(string $username, string $refreshToken): void
    {
        $sql = '
        UPDATE auth_refresh_token
        SET valid = :pastDate
        WHERE username = :username
        AND refresh_token = :refreshToken
    ';

        $stmt = $this->connection->prepare($sql);
        $stmt->executeStatement([
            'username' => $username,
            'refreshToken' => $refreshToken,
            'pastDate' => (new \DateTime('-1 day'))->format('Y-m-d H:i:s'),
        ]);
    }
}
