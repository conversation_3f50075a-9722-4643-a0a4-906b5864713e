<?php

declare(strict_types=1);

namespace App\Auth\Infrastructure\Doctrine\Persistence;

use App\Auth\Domain\AuthUser;
use App\Auth\Domain\AuthUserRepositoryInterface;
use App\Auth\Infrastructure\Doctrine\Exception\AccountNotExist;
use App\Common\Uuid;
use App\SharedKernel\Email;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AuthUser>
 */
class DoctrineAuthUserRepository extends ServiceEntityRepository implements AuthUserRepositoryInterface
{
    public function __construct(
        private readonly EntityManagerInterface $em,
        ManagerRegistry $registry,
    ) {
        parent::__construct($registry, AuthUser::class);
    }

    public function getByAuthUserId(Uuid $authUserId): AuthUser
    {
        $authUser = $this->em->getRepository(AuthUser::class)->findOneBy([
            'authUserId' => $authUserId->valueString()]
        );

        if (!$authUser instanceof AuthUser) {
            throw AccountNotExist::becauseNotExistInDatabase();
        }

        return $authUser;
    }

    public function getByEmail(Email $email): AuthUser
    {
        $authUser = $this->findByEmail($email);

        if (!$authUser instanceof AuthUser) {
            throw AccountNotExist::becauseNotExistInDatabase();
        }

        return $authUser;
    }

    public function findByEmail(Email $email): ?AuthUser
    {
        $authUser = $this->findOneBy(['email.email' => $email->valueString()]);

        if (!$authUser instanceof AuthUser) {
            return null;
        }

        return $authUser;
    }

    public function add(AuthUser $authUser): void
    {
        $this->em->persist($authUser);
        $this->em->flush();
    }

    public function update(AuthUser $authUser): void
    {
        $this->em->flush();
    }
}
