<doctrine-mapping xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping
                                      https://www.doctrine-project.org/schemas/orm/doctrine-mapping.xsd">

    <entity name="App\Auth\Domain\ResetPasswordRequest" table="auth_reset_password_request">


        <id name="id" type="integer" column="id">
            <generator/>
            <sequence-generator sequence-name="tablename_seq" allocation-size="100" />
        </id>

        <many-to-one field="authUser" target-entity="App\Auth\Domain\AuthUser">
            <join-column name="authUser" referenced-column-name="authUserId" nullable="false" />
        </many-to-one>

        <field name="verifier" length="20"/>
<!--        <field name="hashedToken" type="text"/>-->
        <field name="requestedAt" type="datetime_immutable"/>
        <field name="expiresAt" type="datetime_immutable"/>

    </entity>
</doctrine-mapping>
