<?xml version="1.0" encoding="UTF-8"?>
<doctrine-mapping xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping
                                      https://www.doctrine-project.org/schemas/orm/doctrine-mapping.xsd">

    <entity name="App\Auth\Domain\AuthUser" table="auth_user">

        <unique-constraints>
            <unique-constraint columns="authUserId"/>
            <unique-constraint columns="email"/>
        </unique-constraints>

        <indexes>
            <index columns="authUserId"/>
            <index columns="email"/>
        </indexes>

        <id name="authUserId" type="uuid_symfony" column="authUserId">
            <generator strategy="NONE"/>
        </id>

        <embedded name="email" class="App\SharedKernel\Email" use-column-prefix="false"/>
        <embedded name="password" class="App\Auth\Domain\Password" use-column-prefix="false"/>
        <field name="role" enum-type="App\SharedKernel\Roles"/>
        <field name="isActive" type="boolean"/>
        <field name="verificationAt" type="datetime_immutable" nullable="true"/>
        <field name="loginAt" type="datetime_immutable" nullable="true"/>
        <field name="logoutAt" type="datetime_immutable" nullable="true"/>
        <field name="createdAt" type="datetime_immutable"/>
        <field name="updatedAt" type="datetime_immutable" nullable="true"/>

    </entity>
</doctrine-mapping>
