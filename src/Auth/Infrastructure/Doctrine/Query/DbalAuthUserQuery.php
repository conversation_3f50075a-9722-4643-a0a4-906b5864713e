<?php

declare(strict_types=1);

namespace App\Auth\Infrastructure\Doctrine\Query;

use App\Auth\Application\Query\AuthUserQueryInterface;
use App\Auth\Application\Query\AuthUserView;
use App\Auth\Infrastructure\Doctrine\Exception\AccountNotExist;
use Doctrine\DBAL\Connection;

class DbalAuthUserQuery implements AuthUserQueryInterface
{
    public const TABLE_NAME = 'auth_user';

    public function __construct(
        private readonly Connection $connection,
    ) {
    }

    public function findByEmail(string $email): AuthUserView
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('*')
            ->from(self::TABLE_NAME, 'au')
            ->where('au.email = :email')
            ->setParameter('email', $email);

        $authUser = $this->connection->fetchAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        if ($authUser === false) {
            throw AccountNotExist::becauseNotExistInDatabase();
        }

        return AuthUserView::deserialize($authUser);
    }

    public function checkIfEmailExists(string $email, ?string $excludeAuthUserId = null): bool
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('email')
            ->from(self::TABLE_NAME, 'au')
            ->where('au.email = :email')
            ->setParameter('email', $email);

        if ($excludeAuthUserId !== null) {
            $queryBuilder
                ->andWhere('au.authUserId != :excludeAuthUserId')
                ->setParameter('excludeAuthUserId', $excludeAuthUserId);
        }

        $authUser = $this->connection->fetchAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        return $authUser === false;
    }

    public function checkIfMobilePhoneNumberExists(string $mobilePhoneNumber): bool
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('*')
            ->from(self::TABLE_NAME, 'au')
            ->where('au.mobilePhoneNumber = :mobilePhoneNumber')
            ->setParameter('mobilePhoneNumber', $mobilePhoneNumber);

        $authUser = $this->connection->fetchAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        return $authUser === false;
    }
}
