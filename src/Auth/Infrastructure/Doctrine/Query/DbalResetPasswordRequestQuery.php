<?php

declare(strict_types=1);

namespace App\Auth\Infrastructure\Doctrine\Query;

use App\Auth\Application\Query\ResetPasswordRequestQueryInterface;
use Doctrine\DBAL\Connection;

class DbalResetPasswordRequestQuery implements ResetPasswordRequestQueryInterface
{
    public const TABLE_NAME = 'auth_reset_password_request';

    public function __construct(
        private readonly Connection $connection,
    ) {
    }

    public function getMostRecentNonExpiredRequestDate(string $authUserId): ?\DateTimeImmutable
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->select('*')
            ->from(self::TABLE_NAME, 'rpr')
            ->where('rpr.authUser = :authUser')
            ->setParameter('authUser', $authUserId, 'uuid')
            ->orderBy('rpr.requestedAt', 'DESC')
            ->setMaxResults(1)
        ;

        $data = $this->connection->fetchAssociative($queryBuilder->getSQL(), $queryBuilder->getParameters());

        if (false === $data) {
            return null;
        }

        $expiresAt = new \DateTimeImmutable($data['expiresAt']);

        if ($expiresAt->getTimestamp() >= time()) {
            return new \DateTimeImmutable($data['requestedAt']);
        }

        return null;
    }
}
