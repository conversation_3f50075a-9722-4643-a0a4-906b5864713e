<?php

declare(strict_types=1);

namespace App\Auth\Infrastructure\Mailer;

use App\Auth\Application\Service\EmailInterface;
use App\Auth\Application\Service\ParameterBagInterface;
use App\SharedKernel\Email;
use Symfony\Bridge\Twig\Mime\TemplatedEmail;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Address;
use Symfony\Component\Mime\BodyRendererInterface;

// TODO Opracowanie szablonu dla wiadomości email
readonly class SendEmail implements EmailInterface
{
    public function __construct(
        private MailerInterface $mailer,
        private ParameterBagInterface $parameterBag,
        private BodyRendererInterface $bodyRenderer,
    ) {
    }

    public function sendWelcomeEmail(Email $emailTo): void
    {
        $frontUrl = $this->parameterBag->getFrontendUrl();
        $path = 'reset-password';

        $email = (new TemplatedEmail())
            ->from(new Address($this->parameterBag->getMailerFromEmail(), $this->parameterBag->getMailerFromName()))
            ->to(new Address($emailTo->valueString()))
            ->subject('Witaj w aplikacji HOPE')
            ->html('Witaj w naszej aplikacji. Dziękujemy za założenie konta.<br>
                    Skorzystaj z resetowania hasła aby się zalogować pierwszy raz.<br><br>
                     <a href="'.$frontUrl.$path.'">'.$frontUrl.$path.'</a><br><br>
                    Z poważaniem Zespół Administratorów Hope<br/>(Wiadomość została wygenerowana automatycznie. Prosimy nie odpowiadać na nią)');

        $this->bodyRenderer->render($email);
        $this->mailer->send($email);
    }

    public function sendResetPasswordEmail(Email $emailTo, string $subject, string $publicToken): void
    {
        $frontUrl = $this->parameterBag->getFrontendUrl();
        $path = 'reset-password/reset/?token='.$publicToken;
        $tokenLifetimeInMinutes = $this->parameterBag->getResetPasswordRequestLifetime() / 60;

        $email = (new TemplatedEmail())
            ->from(new Address($this->parameterBag->getMailerFromEmail(), $this->parameterBag->getMailerFromName()))
            ->to(new Address($emailTo->valueString()))
            ->subject($subject)
            ->html('Jeśli próbujesz właśnie zresetować swoje hasło, kontynuuj proces, przechodząc pod następujący adres:<br>
                    <a href="'.$frontUrl.$path.'">'.$frontUrl.$path.'</a><br><br>
                   Ten link wygaśnie za '.$tokenLifetimeInMinutes.' min.<br><br>
                   Jeżeli nie resetowałeś swojego hasła, to zignoruj tego maila.<br><br>
                   Z poważaniem Zespół Administratorów Hope<br/>(Wiadomość została wygenerowana automatycznie. Prosimy nie odpowiadać na nią)');

        $this->bodyRenderer->render($email);
        $this->mailer->send($email);
    }

    public function sendEmailPasswordHasChanged(Email $emailTo): void
    {
        $email = (new TemplatedEmail())
            ->from(new Address($this->parameterBag->getMailerFromEmail(), $this->parameterBag->getMailerFromName()))
            ->to(new Address($emailTo->valueString()))
            ->subject('Hasło zostało zmienione')
            ->html('Twoje hasło zostało zmienione.');

        $this->bodyRenderer->render($email);
        $this->mailer->send($email);
    }
}
