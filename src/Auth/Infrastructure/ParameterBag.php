<?php

declare(strict_types=1);

namespace App\Auth\Infrastructure;

use App\Auth\Application\Service\ParameterBagInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface as ParameterBagInterfaceAlias;

readonly class ParameterBag implements ParameterBagInterface
{
    public function __construct(
        private ParameterBagInterfaceAlias $parameterBag,
    ) {
    }

    public function getResetPasswordRequestLifetime(): int
    {
        return (int) $this->parameterBag->get('app_reset_password_request_lifetime');
    }

    public function getResetPasswordThrottleLimit(): int
    {
        return (int) $this->parameterBag->get('app_reset_password_throttle_limit');
    }

    public function getFrontendUrl(): string
    {
        return $this->parameterBag->get('app_front_url');
    }

    public function getMailerFromEmail(): string
    {
        return $this->parameterBag->get('mailer_from_email');
    }

    public function getMailerFromName(): string
    {
        return $this->parameterBag->get('mailer_from_name');
    }
}
