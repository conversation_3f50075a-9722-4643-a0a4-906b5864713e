<?php

declare(strict_types=1);

namespace App\Auth\Infrastructure\Token;

use App\Common\Uuid;

readonly class TokenGenerator
{
    private const int SELECTOR_LENGTH = 20;

    public function __construct()
    {
    }

    public function createToken(\DateTimeImmutable $expiresAt, Uuid $authUserId): Token
    {
        $verifier = $this->getRandomAlphaNumStr();
        $selector = $this->getRandomAlphaNumStr();

        return new Token(
            verifier: $verifier,
            selector: $selector,
            authUserId: $authUserId->valueString(),
            expiresAt: $expiresAt
        );
    }

    public function decodePublicToken(string $publicToken): string
    {
        //        $selector = substr($publicToken, 0, self::SELECTOR_LENGTH);
        return substr($publicToken, self::SELECTOR_LENGTH, self::SELECTOR_LENGTH);
    }

    private function getRandomAlphaNumStr(): string
    {
        $string = '';

        while (($len = \strlen($string)) < self::SELECTOR_LENGTH) {
            /** @var int<1, max> $size */
            $size = self::SELECTOR_LENGTH - $len;

            $bytes = random_bytes($size);

            $string .= substr(
                str_replace(['/', '+', '='], '', base64_encode($bytes)), 0, $size);
        }

        return $string;
    }
}
