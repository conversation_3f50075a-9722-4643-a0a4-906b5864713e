<?php

declare(strict_types=1);

namespace App\Auth\Infrastructure\Token;

use App\Auth\Application\Service\TokenComponentsInterface;
use App\Common\Uuid;

readonly class TokenComponents implements TokenComponentsInterface
{
    public function __construct(
        private TokenGenerator $tokenGenerator,
    ) {
    }

    public function generateToken(string $authUserId, ?int $resetRequestLifetime = null): Token
    {
        $expiresAt = new \DateTimeImmutable(sprintf('+%d seconds', $resetRequestLifetime));

        return $this->tokenGenerator->createToken($expiresAt, Uuid::fromString($authUserId));
    }

    public function validateFullTokenAndFetchVerifier(string $fullToken): string
    {
        return $this->tokenGenerator->decodePublicToken($fullToken);
    }
}
