<?php

declare(strict_types=1);

namespace App\Auth\Infrastructure\Token;

readonly class Token
{
    public function __construct(
        private string $verifier,
        private string $selector,
        private string $authUserId,
        private \DateTimeImmutable $expiresAt,
    ) {
    }

    public function verifier(): string
    {
        return $this->verifier;
    }

    public function selector(): string
    {
        return $this->selector;
    }

    public function authUserId(): string
    {
        return $this->authUserId;
    }

    public function expiresAt(): \DateTimeImmutable
    {
        return $this->expiresAt;
    }

    public function publicToken(): string
    {
        return $this->selector.$this->verifier;
    }
}
